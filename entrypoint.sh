#!/bin/sh

# Check if the environment is not staging and set agent arguments accordingly
if [ ${MICRONAUT_ENVIRONMENTS} != "staging" ]; then
  AGENT_ARGS="-javaagent:dd-java-agent.jar -Ddd.trace.config=dd-java-agent.properties -Ddd.profiling.enabled=true -Ddd.profiling.allocation.enabled=true"
fi

exec java -XX:+UseContainerSupport $AGENT_ARGS -XX:FlightRecorderOptions=stackdepth=256 -Dcom.sun.management.jmxremote -Djava.locale.providers=COMPAT,CLDR -noverify ${JAVA_OPTS} -jar ${SERVICE}.jar
