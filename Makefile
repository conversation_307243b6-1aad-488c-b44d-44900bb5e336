DEFAULT_GOAL = compile

GRADLEW = ./gradlew

.PHONY: compile clean format-cc cc lint format

package:
	$(GRADLEW) build testClasses -x test

clean:
	$(GRADLEW) clean

format-cc: format cc

cc:
	$(GRADLEW) clean build testClasses -x test

lint:
	$(GRADLEW) ktlintCheck

format:
	$(GRADLEW) ktlintFormat

pre-commit-install:
	$(GRADLEW) addKtlintFormatGitPreCommitHook

run-friday:
	LOGGER_LEVELS_ROOT=INFO MICRONAUT_ENVIRONMENTS=friday,staging ./gradlew run
