# Open Finance Service

Serviço de integração com Open Finance (Open Banking) brasileiro desenvolvido pela Friday AI, responsável por gerenciar consentimentos de dados, contas bancárias, transações e pagamentos via iniciação (sweeping).

## Stack Tecnológica e Dependências Principais

### Framework e Linguagem
- **Kotlin 2.0.0** - Linguagem principal
- **Micronaut 4.5.4** - Framework de microserviços
- **Java 17** - Versão da JVM

### Banco de Dados e Persistência
- **Amazon DynamoDB** - Banco de dados NoSQL principal
- **DynamoDB Enhanced Client** - Cliente aprimorado para DynamoDB
- **ShedLock** - Controle de locks distribuídos para jobs agendados

### Integração e Mensageria
- **Amazon SQS** - Sistema de filas para mensageria assíncrona
- **Amazon SNS** - Sistema de notificações
- **Friday Morning Messaging** - Biblioteca interna de mensageria

### Segurança e Autenticação
- **Micronaut Security JWT** - Autenticação baseada em JWT
- **AWS KMS** - Gerenciamento de chaves criptográficas
- **Bouncy Castle** - Criptografia adicional

### Observabilidade e Monitoramento
- **OpenTelemetry** - Distributed tracing
- **Micrometer + StatsD** - Métricas de aplicação
- **Logback + Logstash** - Logging estruturado
- **DataDog** - APM e monitoramento

### Testes
- **JUnit 5** - Framework de testes unitários
- **MockK** - Biblioteca de mocks para Kotlin
- **TestContainers** - Testes de integração com containers
- **ArchUnit** - Testes de arquitetura

## Estrutura do Projeto

```
src/main/kotlin/ai/friday/openfinance/
├── Application.kt                          # Ponto de entrada da aplicação
├── adapters/                              # Camada de adaptadores
│   ├── api/                              # Controllers REST
│   │   ├── OpenFinanceController.kt      # API principal do Open Finance
│   │   ├── BackofficeController.kt       # APIs de backoffice
│   │   └── IniciadorWebhookController.kt # Webhooks do Iniciador
│   ├── auth/                             # Autenticação e autorização
│   ├── dynamodb/                         # Repositórios DynamoDB
│   ├── iniciador/                        # Integração com Iniciador
│   ├── jobs/                             # Jobs agendados
│   ├── messaging/                        # Handlers de mensageria
│   ├── sqs/                              # Integração SQS
│   └── kms/                              # Integração KMS
├── app/                                   # Camada de domínio/negócio
│   ├── OpenFinance.kt                    # Modelos de domínio principais
│   ├── bankaccount/                      # Serviços de conta bancária
│   ├── creditcard/                       # Serviços de cartão de crédito
│   ├── connection/                       # Serviços de consentimento
│   ├── sweepingaccount/                  # Serviços de conta sweeping
│   ├── job/                              # Gerenciamento de jobs
│   └── utils/                            # Utilitários do domínio
└── resources/
    ├── application.yml                    # Configuração principal
    ├── application-staging.yml           # Configuração de staging
    └── logback.xml                       # Configuração de logs
```

### Principais Componentes

#### 1. **Controllers (API Layer)**
- `OpenFinanceController`: API REST principal para gerenciamento de consentimentos e operações
- `BackofficeController`: APIs administrativas para backoffice
- `IniciadorWebhookController`: Recebimento de webhooks do provedor Iniciador

#### 2. **Serviços de Domínio**
- `BankAccountService`: Gerenciamento de contas bancárias e transações
- `CreditCardService`: Gerenciamento de cartões de crédito e faturas
- `DataConsentService`: Gerenciamento de consentimentos de dados
- `SweepingAccountService`: Gerenciamento de contas e pagamentos sweeping

#### 3. **Adaptadores de Integração**
- `IniciadorAdapter`: Integração com a plataforma Iniciador (provedor Open Finance)
- `BankAccountAdapter`: Adaptadores para operações bancárias
- `CreditCardAdapter`: Adaptadores para operações de cartão de crédito

#### 4. **Repositórios**
- Repositórios DynamoDB para todas as entidades principais
- Implementação do padrão Repository para abstração de persistência

#### 5. **Mensageria**
- Handlers para processamento assíncrono de eventos
- Publishers para envio de mensagens para filas SQS

## Configuração e Instalação

### Pré-requisitos
- **Java 17**
- **Docker** (para testes locais com DynamoDB Local)
- **AWS CLI** configurado (para ambientes de desenvolvimento/produção)

### Variáveis de Ambiente Necessárias

```bash
# Configuração AWS
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=<sua_access_key>
AWS_SECRET_ACCESS_KEY=<sua_secret_key>

# Credenciais do Iniciador (obtidas via AWS Secrets)
INICIADOR_CLIENT_ID=<client_id>
INICIADOR_CLIENT_SECRET=<client_secret>
INICIADOR_SWEEPING_CLIENT_ID=<sweeping_client_id>
INICIADOR_SWEEPING_CLIENT_SECRET=<sweeping_client_secret>

# Chave KMS
KMS_KEY_ID=46769f9d-d5fe-49d5-9e05-b371399ee37c

# Autenticação Open Finance
OPEN_FINANCE_CLIENT_ID=<client_id>
OPEN_FINANCE_CLIENT_SECRET=<client_secret>

# Para desenvolvimento local com GitLab
GITLAB_KEY=<sua_chave_gitlab>
```

### Instalação e Execução

#### 1. Clone o repositório
```bash
git clone <repository_url>
cd open-finance-service
```

#### 2. Execute os testes
```bash
./gradlew test
```

#### 3. Compile a aplicação
```bash
./gradlew build
```

#### 4. Execute localmente
```bash
./gradlew run
```

#### 5. Execute com Docker
```bash
docker build -t open-finance-service .
docker run -p 8443:8443 open-finance-service
```

## Uso da API

### Endpoints Principais

#### Consentimento de Dados
```bash
# Criar consentimento de dados
POST /data-consents
{
  "userAccountId": "user123",
  "document": "********901",
  "participantId": "participant123"
}

# Revogar consentimento
DELETE /data-consents/{dataConsentId}
```

#### Consentimento Sweeping
```bash
# Criar consentimento sweeping
POST /sweeping-consents
{
  "userAccountId": "user123",
  "user": {
    "taxId": "********901",
    "name": "João Silva"
  },
  "participantId": "participant123",
  "creditorConsentTO": {
    "type": "ACCOUNT",
    "name": "Conta Exemplo",
    "ispb": "********",
    "number": "12345-6",
    "accountType": "CHECKING"
  },
  "sweepingLimits": {
    "totalAllowedAmount": 100000,
    "transactionLimit": 5000,
    "periodicLimits": {
      "day": { "quantityLimit": 3, "transactionLimit": 1000 },
      "week": { "quantityLimit": 10, "transactionLimit": 5000 },
      "month": { "quantityLimit": 30, "transactionLimit": 20000 },
      "year": { "quantityLimit": 365, "transactionLimit": 100000 }
    }
  }
}
```

#### Pagamentos Sweeping
```bash
# Criar pagamento
POST /sweeping-payments
{
  "requestId": "payment123",
  "consentId": "consent123",
  "creditorId": "creditor123",
  "amount": 10000,
  "description": "Pagamento teste",
  "riskSignals": {
    "type": "AUTOMATIC",
    "lastLoginDateTime": "2024-01-01T10:00:00Z",
    "pixKeyRegistrationDateTime": "2024-01-01T09:00:00Z"
  }
}

# Consultar status do pagamento
GET /sweeping-payments/{paymentRequestId}/status
```

#### Saldo da Conta Bancária
```bash
# Obter saldo da conta
GET /data-consents/{dataConsentId}/bank-accounts/{bankAccountId}/balance
```

### Health Check
```bash
GET /health
```

## Fluxos do Sistema

Esta seção descreve os principais fluxos de negócio implementados no serviço Open Finance, mostrando como os processos funcionam passo a passo.

### 1. Fluxo de Consentimento de Dados

O consentimento de dados permite que os usuários autorizem o Friday AI a acessar informações de suas contas bancárias e cartões de crédito.

```
[Cliente Friday] → [Open Finance Service] → [Iniciador] → [Instituição Financeira]
       │                    │                    │                    │
       1. Solicita           2. Cria              3. Redireciona      4. Autentica
       consentimento         consentimento        para banco          usuário
       │                    │                    │                    │
       ←────────────────────────────────────────────────────────────
                            5. Retorna link de autorização
```

**Passos do Fluxo:**

1. **Solicitação Inicial**
   - Cliente faz `POST /data-consents` com `userAccountId`, `document` e `participantId`
   - Sistema valida os dados de entrada (CPF/CNPJ, participante válido)

2. **Criação do Consentimento**
   - `DataConsentService.createConsentLink()` é invocado
   - Sistema cria registro no DynamoDB com status `PENDING_AUTHORISATION`
   - Integração com Iniciador via `IniciadorDataConsentAdapter`

3. **Integração Externa**
   - Chamada autenticada para API do Iniciador (`/v1/data/consents`)
   - Iniciador cria consentimento na instituição financeira
   - Retorna link de redirecionamento para autenticação

4. **Monitoramento Assíncrono**
   - Sistema agenda verificação periódica via `CheckDataConsentStatusHandler`
   - Handler consome mensagens da fila `check-data-consent-queue`
   - Status é atualizado: `PENDING_AUTHORISATION` → `AVAILABLE` → `UNAVAILABLE`

5. **Sincronização de Dados**
   - Com consentimento aprovado, dados são sincronizados automaticamente
   - `BankAccountService` busca contas e transações via Iniciador
   - Dados são criptografados via AWS KMS antes do armazenamento

### 2. Fluxo de Consentimento Sweeping (Pagamentos)

O consentimento sweeping permite pagamentos automáticos ou manuais através do Open Finance.

```
[Cliente] → [Open Finance] → [Iniciador] → [Banco Devedor] → [Conta Friday]
    │            │               │              │               │
    1. Cria      2. Valida       3. Cria        4. Executa      5. Credita
    pagamento    limites         consentimento  débito          valor
    │            │               │              │               │
    ←────────────────────────────────────────────────────────────
                        6. Confirma transação
```

**Passos do Fluxo:**

1. **Criação do Consentimento**
   - `POST /sweeping-consents` com dados do usuário e configurações de limite
   - `SweepingAccountService.createConsent()` valida limites e credores
   - Sistema persiste configurações no DynamoDB

2. **Configuração de Limites**
   - Limites diários, semanais, mensais e anuais
   - Controle de quantidade e valor por período
   - Validação de limites totais permitidos

3. **Execução de Pagamento**
   - `POST /sweeping-payments` com detalhes da transação
   - Sistema verifica limites disponíveis via `SweepingAccountService`
   - Validação de sinais de risco (automático ou manual)

4. **Processamento**
   - Integração com `IniciadorSweepingAdapter`
   - Criação do pagamento no Iniciador
   - Monitoramento via webhook e polling

5. **Controle de Uso**
   - `SweepingConsentPeriodicUsage` atualiza limites utilizados
   - Cálculo em tempo real via feature flag `calcLimitUsage`

### 3. Fluxo de Sincronização de Cartões de Crédito

Sistema de sincronização assíncrona para manter dados de cartão de crédito atualizados.

```
[Job Agendado] → [SQS Queue] → [Event Handler] → [Iniciador] → [DynamoDB]
       │              │              │             │            │
   1. Executa     2. Envia       3. Processa   4. Busca    5. Armazena
   mensalmente    mensagem       evento        dados       dados
```

**Passos do Fluxo:**

1. **Agendamento**
   - `CreditCardHistoryJob` executa no dia 20 de cada mês às 5:15h
   - Busca todos os consentimentos com status `AVAILABLE`
   - Envia mensagem `CARD_REFRESH` para cada consentimento

2. **Processamento Assíncrono**
   - `CreditCardEventHandler` consome mensagens da fila `sync-credit-card-queue`
   - Processa diferentes tipos de eventos:
     - `CARD_REFRESH`: Atualiza lista de cartões
     - `CARD_CREATED`: Busca faturas e transações do cartão
     - `INVOICE_CREATED/UPDATED`: Sincroniza transações específicas da fatura

3. **Integração e Armazenamento**
   - `CreditCardService` coordena as operações
   - `IniciadorCreditCardAdapter` faz chamadas para API do Iniciador
   - Dados são criptografados e armazenados no DynamoDB

### 4. Fluxo de Sincronização de Contas Bancárias

Sincronização diária de transações bancárias para manter dados atualizados.

```
[Job Diário 10h] → [BankAccountService] → [Iniciador] → [DynamoDB]
       │                    │                │            │
   1. Executa          2. Sincroniza     3. Busca      4. Atualiza
   diariamente         transações        dados         transações
```

**Passos do Fluxo:**

1. **Execução Agendada**
   - `SyncBankAccountTransactionsJob` executa diariamente às 10h
   - Chama `BankAccountService.syncBankTransactionsCurrent()`

2. **Sincronização Direta**
   - Diferente dos cartões, usa abordagem síncrona
   - Busca transações recentes via `IniciadorBankAccountAdapter`
   - Atualiza registros existentes ou cria novos

3. **Processamento de Dados**
   - Transações são validadas e normalizadas
   - Dados sensíveis criptografados via KMS
   - Metadados de sincronização atualizados

### 5. Fluxo de Webhooks

Sistema para receber notificações assíncronas do provedor Iniciador.

```
[Iniciador] → [Webhook Controller] → [SQS] → [Handler] → [Service] → [DynamoDB]
     │              │                 │        │          │          │
 1. Envia      2. Recebe         3. Publica  4. Processa 5. Atualiza 6. Persiste
 webhook       notificação       mensagem    evento      status      dados
```

**Passos do Fluxo:**

1. **Recepção de Webhook**
   - `IniciadorWebhookController` recebe POST do Iniciador
   - Valida assinatura e conteúdo da mensagem
   - Responde imediatamente com 200 OK

2. **Processamento Assíncrono**
   - Webhook é publicado na fila `sweeping-webhook-queue`
   - `SweepingWebhookHandler` processa diferentes tipos:
     - Mudanças de status de consentimento
     - Atualizações de pagamentos
     - Notificações de erro

3. **Atualização de Status**
   - Handler identifica o tipo de evento
   - Atualiza status no DynamoDB
   - Logs estruturados para auditoria

### 6. Fluxo de Monitoramento de Status

Sistema de verificação contínua de status de consentimentos.

```
[Scheduler] → [SQS Message] → [Status Handler] → [Iniciador API] → [Update Status]
     │             │                │                 │               │
 1. Agenda     2. Envia         3. Verifica      4. Consulta     5. Atualiza
 verificação   mensagem         status           status          DynamoDB
```

**Passos do Fluxo:**

1. **Agendamento de Verificações**
   - Sistema programa verificações periódicas
   - Mensagens enviadas para filas específicas de cada tipo

2. **Handlers de Status**
   - `CheckDataConsentStatusHandler`: Verifica status de consentimentos de dados
   - `CheckSweepingConsentStatusHandler`: Verifica status de consentimentos sweeping
   - Cada handler consulta a API do Iniciador

3. **Decisão de Processamento**
   - Status `final` (AVAILABLE, UNAVAILABLE, REVOKED): Remove mensagem da fila
   - Status `transitório` (PENDING_AUTHORISATION): Mantém mensagem para nova verificação
   - Implementa backoff automático via configuração SQS

### 7. Fluxo de Criptografia e Segurança

Todos os dados sensíveis são criptografados usando AWS KMS.

```
[Dados Sensíveis] → [KMS Encrypt] → [DynamoDB] → [KMS Decrypt] → [Aplicação]
        │               │             │             │              │
    1. Captura      2. Criptografa 3. Armazena  4. Descriptografa 5. Utiliza
    dados raw       via KMS        criptografado    conforme      dados
                                                   necessário
```

**Características de Segurança:**

- **Autenticação JWT**: Todos os endpoints protegidos
- **Criptografia em Trânsito**: HTTPS obrigatório
- **Criptografia em Repouso**: AWS KMS para dados sensíveis
- **Auditoria Completa**: Logs estruturados com correlation IDs
- **Rate Limiting**: Proteção contra abuse de APIs

## Configuração

### application.yml
O arquivo principal de configuração contém:

- **Configurações do Micronaut**: Servidor, SSL, pools de threads
- **Integrações**: URLs e credenciais do Iniciador
- **AWS**: Configurações de região, DynamoDB e KMS
- **Mensageria**: Configuração de filas SQS
- **Agendamentos**: Cron expressions para jobs
- **Tracing**: Configuração do OpenTelemetry
- **Segurança**: Configurações de autenticação

### Perfis de Ambiente
- `application.yml`: Configuração base
- `application-staging.yml`: Configurações específicas de staging
- `application-prodme-poupe.yml`: Configurações específicas de produção

### Jobs Agendados
- **Sincronização de Transações**: Executa diariamente às 10h
- **Atualização de Histórico de Cartão**: Executa mensalmente no dia 20 às 5:15h

## Recursos Adicionais

### Monitoramento
- **Health Checks**: `/health` endpoint para verificação de saúde
- **Métricas**: `/metrics` endpoint com métricas Prometheus
- **Tracing**: Distributed tracing com OpenTelemetry
- **Logs**: Logs estruturados com correlation IDs

### Segurança
- **HTTPS**: SSL habilitado por padrão na porta 8443
- **JWT**: Autenticação baseada em tokens JWT
- **Criptografia**: Dados sensíveis criptografados via AWS KMS
- **Rate Limiting**: Controle de taxa de requisições

### Desenvolvimento
- **Testes**: Cobertura abrangente com testes unitários e de integração
- **Linting**: Código formatado com KtLint
- **Arquitetura**: Validação de arquitetura com ArchUnit
- **Hot Reload**: Suporte a recarga automática em desenvolvimento

Para mais informações sobre APIs específicas, consulte a documentação dos controllers ou entre em contato com a equipe de desenvolvimento.