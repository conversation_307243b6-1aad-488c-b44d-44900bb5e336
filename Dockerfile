ARG DOCKER_IMAGE_MIRROR
FROM ${DOCKER_IMAGE_MIRROR}eclipse-temurin:17-jre-alpine

ARG APP_VERSION
ENV SERVICE=open-finance-service
ENV DD_VERSION=$APP_VERSION

LABEL com.datadoghq.tags.version="$APP_VERSION"

RUN apk add --no-cache curl

# Copy the Datadog agent files and entrypoint script
COPY dd-java-agent.jar dd-java-agent.jar
COPY dd-java-agent.properties dd-java-agent.properties
COPY entrypoint.sh /entrypoint.sh
COPY application/build/libs/${SERVICE}-*-all.jar ${SERVICE}.jar

# Make entrypoint script executable
RUN chmod +x /entrypoint.sh

EXPOSE 8443
ENTRYPOINT ["/entrypoint.sh"]
