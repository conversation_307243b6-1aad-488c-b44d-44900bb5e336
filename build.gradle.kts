import com.adarshr.gradle.testlogger.TestLoggerExtension
import com.adarshr.gradle.testlogger.theme.ThemeType

plugins {
    kotlin("jvm") version "2.0.0"
    id("org.jetbrains.kotlin.plugin.allopen") version "2.0.0"
    id("io.micronaut.application") version "4.5.4"
    id("org.jetbrains.kotlin.kapt") version "1.9.22"
    id("com.github.johnrengelman.shadow") version "8.1.1"
    id("org.jlleitschuh.gradle.ktlint") version "11.6.1"
    id("com.adarshr.test-logger") version "4.0.0" // plugin para mostrar os testes que estão sendo executados
    id("java-test-fixtures")
    id("jacoco")
//    id("com.google.devtools.ksp") version "2.0.0-1.0.21"
    // id("io.micronaut.test-resources") version "4.0.4"
    // id("io.micronaut.aot") version "4.2.1"
}

version = "0.1"
group = "ai.friday.openfinance"

val kotlinVersion = project.properties["kotlinVersion"]

subprojects {
    apply(plugin = "kotlin")
    apply(plugin = "org.jetbrains.kotlin.plugin.allopen")
    apply(plugin = "io.micronaut.application")
    apply(plugin = "org.jetbrains.kotlin.kapt")
    apply(plugin = "org.jlleitschuh.gradle.ktlint")
    apply(plugin = "com.adarshr.test-logger")
    apply(plugin = "jacoco")

    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            url = uri("https://gitlab.com/api/v4/groups/6318341/-/packages/maven")
            if (providers.environmentVariable("CI_JOB_TOKEN").isPresent) { // TODO - como melhorar isso?
                credentials(HttpHeaderCredentials::class) {
                    name = "Job-Token"
                    value = providers.environmentVariable("CI_JOB_TOKEN").get()
                }
            } else {
                credentials(HttpHeaderCredentials::class) {
                    name = "Private-Token"
                    value =
                        if (providers.environmentVariable("GITLAB_KEY").isPresent) { // Adicione a sua chave de acesso ao Gitlab em uma variavel de ambiente chamada GITLAB_KEY
                            providers.environmentVariable("GITLAB_KEY").get()
                        } else {
                            ""
                        }
                }
            }

            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }

    tasks {
        compileKotlin {
            incremental = true
            compilerOptions.jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
        }
        compileTestKotlin {
            incremental = true
            compilerOptions.jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
        }

        test {
            jvmArgs = listOf(
                "-Xmx2g",
                "-Djava.locale.providers=COMPAT,CLDR",
                "--add-opens",
                "java.base/java.util=ALL-UNNAMED",
            )
            maxParallelForks = 2
            useJUnitPlatform()
        }

        jar {
            isZip64 = true
        }

        configure<TestLoggerExtension> {
            theme = ThemeType.STANDARD_PARALLEL
            showPassed = false
            showSkipped = false
            showOnlySlow = false
        }
    }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        url = uri("https://gitlab.com/api/v4/groups/6318341/-/packages/maven")
        if (providers.environmentVariable("CI_JOB_TOKEN").isPresent) { // TODO - como melhorar isso?
            credentials(HttpHeaderCredentials::class) {
                name = "Job-Token"
                value = providers.environmentVariable("CI_JOB_TOKEN").get()
            }
        } else {
            credentials(HttpHeaderCredentials::class) {
                name = "Private-Token"
                value =
                    if (providers.environmentVariable("GITLAB_KEY").isPresent) { // Adicione a sua chave de acesso ao Gitlab em uma variavel de ambiente chamada GITLAB_KEY
                        providers.environmentVariable("GITLAB_KEY").get()
                    } else {
                        ""
                    }
            }
        }

        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
}

dependencies {
    // dependencias base - geradas com o Micronaut launch
//    ksp("io.micronaut:micronaut-http-validation")
//    ksp("io.micronaut.security:micronaut-security-annotations")
//    ksp("io.micronaut.validation:micronaut-validation-processor")
    kapt("io.micronaut:micronaut-http-validation")
    kapt("io.micronaut.security:micronaut-security-annotations")
    kapt("io.micronaut.validation:micronaut-validation-processor")
    api("io.micronaut.validation:micronaut-validation")
    api("jakarta.validation:jakarta.validation-api")
    api("io.micronaut:micronaut-http-client")
    api("io.micronaut:micronaut-jackson-databind")
    api("io.micronaut:micronaut-management")
    api("io.micronaut.cache:micronaut-cache-caffeine")
    api("io.micronaut.kotlin:micronaut-kotlin-runtime")
    api("io.micronaut.micrometer:micronaut-micrometer-registry-statsd")
    api("io.micronaut.rxjava2:micronaut-rxjava2-http-client")
    api("io.micronaut.security:micronaut-security-jwt")
    api("io.micronaut.validation:micronaut-validation")
    api("org.jetbrains.kotlin:kotlin-reflect:$kotlinVersion")
    api("org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlinVersion")
    api("software.amazon.awssdk:dynamodb")
    api("ch.qos.logback:logback-classic")
    api("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.yaml:snakeyaml")
    // aotPlugins(platform("io.micronaut.platform:micronaut-platform:4.2.2"))
    // aotPlugins("io.micronaut.security:micronaut-security-aot")

    // dependencias exigidas em tempo de compilação
    api("ai.friday.morning:morning-date:0.9.14")
    api("ai.friday.morning:morning-messaging:0.9.14")
    api("software.amazon.awssdk:cognitoidentityprovider")
    api("software.amazon.awssdk:dynamodb-enhanced")
    api("com.amazonaws:aws-java-sdk-dynamodb") // ao invés de pegar do shedlock
    api("io.arrow-kt:arrow-core:1.2.0")
    api("net.javacrumbs.shedlock:shedlock-micronaut:5.8.0") // ao invés de 4.33.0"
    api("net.javacrumbs.shedlock:shedlock-provider-dynamodb2:5.8.0") // ao invés de "net.javacrumbs.shedlock:shedlock-micronaut:4.33.0"
    api("io.micronaut.reactor:micronaut-reactor")
    api("io.projectreactor.kotlin:reactor-kotlin-extensions")
    api("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    api("software.amazon.awssdk:sqs")
    api("software.amazon.awssdk:sns")
    api("com.amazonaws:DynamoDBLocal:2.0.0") // 1.13.2
    api("io.micronaut.tracing:micronaut-tracing-annotation")
    api("io.micronaut:micronaut-retry")
    compileOnly("com.datadoghq:dd-trace-api:0.96.0")
    api("com.auth0:java-jwt:3.19.4") // ao invés de  3.18.3 -> testar com "io.micronaut.security:micronaut-security-oauth2"
    api("org.bouncycastle:bcpkix-jdk18on:1.76") // ao invés de org.bouncycastle:bcpkix-jdk15on:1.70
    api("net.logstash.logback:logstash-logback-encoder:7.4")

    // dependencias exigidas em tempo de execução apenas
    runtimeOnly("io.micronaut.cache:micronaut-cache-core")

    // adiciona e propaga traceId e spanId nas chamadas http
    kapt("io.micronaut.tracing:micronaut-tracing-opentelemetry-annotation")
    api("io.micronaut.tracing:micronaut-tracing-opentelemetry-http")
    api("io.micronaut.tracing:micronaut-tracing-opentracing")
    api("io.opentelemetry.instrumentation:opentelemetry-aws-sdk-2.2")
    api("io.opentelemetry:opentelemetry-extension-kotlin")

    // exporta o traceId e spanId para log
    api("io.opentelemetry:opentelemetry-exporter-logging")
    api("io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0")
    // necessario para conseguir desligar o log do opentelemetry que suja o console
    // configurar direto no logging.properties dentro do projeto nao funcionou
    // OBS: precisa do LevelChangePropagator no logback.xml para nao impactar a performance
    // e desligar o log de cada biblioteca que use o java util logging, por exemplo google cloud platform
    api("org.slf4j:jul-to-slf4j:2.0.9")

    // dependencias de teste
    testFixturesImplementation("io.mockk:mockk-jvm:1.13.8")
    testFixturesApi("ai.friday.morning:morning-date-mockk:0.9.14")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testFixturesImplementation("io.micronaut.test:micronaut-test-kotest5")
    testFixturesImplementation("org.junit.jupiter:junit-jupiter")
    testFixturesImplementation("com.tngtech.archunit:archunit-junit5:1.1.0") // ao invés de 0.23.1
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit")
    testFixturesImplementation("org.testcontainers:testcontainers")
    testImplementation("org.awaitility:awaitility-kotlin:4.2.0")

    /*
    -- podem estar faltando:
        api("io.micronaut.aws:micronaut-aws-sdk-v2")
        api("io.arrow-kt:arrow-fx-coroutines:1.2.0")
        api("aws.sdk.kotlin:kms:0.15.0")//tem o aws-java-sdk-kms-1.12.539.jar
        api("software.amazon.awssdk.crt:aws-crt:0.20.5")//Tem o crt-core
    --

    -- já estão como dependencias (não necessariamente nessas versões) (vendo pela IDE)
        api("io.micronaut:micronaut-http-server-netty")
        api("org.apache.commons:commons-lang3:3.12.0")
        api("commons-codec:commons-codec:1.15")
        api("io.micronaut:micronaut-inject")
        api("io.micronaut:micronaut-runtime")
        api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2")
        api("io.micronaut.micrometer:micronaut-micrometer-core:5.0.1")
    --


    -- dependencias de teste (algumas podem estar no projeto)
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-api:5.9.3")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:5.9.3")
    testImplementation("org.junit.jupiter:junit-jupiter-params:5.9.3")
    testImplementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.2")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    testImplementation("io.micronaut.test:micronaut-test-junit5:4.0.2")
    testImplementation("io.github.ganadist.sqlite4java:libsqlite4java-osx-arm64:1.0.392")
    testImplementation("org.testcontainers:junit-jupiter:1.19.0")
    testImplementation("com.amazonaws:DynamoDBLocal:2.0.0") // 1.13.2
    --
     */
}

// Force secure versions to fix vulnerabilities
configurations.all {
    resolutionStrategy {
        // Fix CVE-2024-6763: Eclipse Jetty URI parsing vulnerability
        // Fix ThreadLimitHandler DoS vulnerability: upgrade to 12.0.23
        force("org.eclipse.jetty:jetty-http:12.0.23")
        force("org.eclipse.jetty:jetty-client:12.0.23")
        force("org.eclipse.jetty:jetty-server:12.0.23")
        force("org.eclipse.jetty:jetty-util:12.0.23")
        force("org.eclipse.jetty:jetty-io:12.0.23")

        // Fix Logback DoS vulnerability: serialization vulnerability
        force("ch.qos.logback:logback-classic:1.5.18")
        force("ch.qos.logback:logback-core:1.5.18")

        // Fix CVE-2025-24970: Netty SslHandler validation vulnerability
        force("io.netty:netty-handler:4.1.118.Final")
        force("io.netty:netty-common:4.1.118.Final")
        force("io.netty:netty-buffer:4.1.118.Final")
        force("io.netty:netty-transport:4.1.118.Final")
        force("io.netty:netty-codec:4.1.118.Final")
        force("io.netty:netty-codec-http:4.1.118.Final")
        force("io.netty:netty-codec-http2:4.1.118.Final")
        force("io.netty:netty-resolver:4.1.118.Final")
    }
}

application {
    mainClass.set("ai.friday.openfinance.ApplicationKt")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = "17"
}

tasks {
    compileKotlin {
        incremental = true
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
    }
    compileTestKotlin {
        incremental = true
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
    }

    jar {
        isZip64 = true
    }

    shadowJar {
        isZip64 = true
        enabled = false
    }

    /*
    optimizedJitJar {
        isZip64 = true
    }
    whenTaskAdded {
        if (this.name == "optimizedJitJarAll") {
            (this as Jar).isZip64 = true
        }
    }
    */

    test {
        jvmArgs =
            listOf("-Xmx2g", "-Djava.locale.providers=COMPAT,CLDR", "--add-opens", "java.base/java.util=ALL-UNNAMED")
        maxParallelForks = 2
        useJUnitPlatform()
    }
}

micronaut {
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("ai.friday.openfinance.*")
    }
    /*testResources {
        enabled.set(true)
        sharedServer.set(true)
    }*/
    /*aot {
        // Please review carefully the optimizations enabled below
        // Check https://micronaut-projects.github.io/micronaut-aot/latest/guide/ for more details
        optimizeServiceLoading.set(false)
        convertYamlToJava.set(false)
        precomputeOperations.set(true)
        cacheEnvironment.set(true)
        optimizeClassLoading.set(true)
        deduceEnvironment.set(true)
        optimizeNetty.set(true)
        // configurationProperties.put("micronaut.security.jwks.enabled", "false")
    }*/
}

testlogger {
    theme = ThemeType.STANDARD_PARALLEL
    showPassed = false
    showSkipped = false
    showOnlySlow = false
}