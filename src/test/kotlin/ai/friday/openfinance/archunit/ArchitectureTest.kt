package ai.friday.openfinance.archunit

import ai.friday.openfinance.app.job.AbstractJob
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaMethod
import com.tngtech.archunit.core.domain.JavaModifier
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.core.importer.Location
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchIgnore
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import io.micronaut.core.annotation.Generated
import io.micronaut.scheduling.annotation.Async
import io.micronaut.scheduling.annotation.Scheduled
import io.micronaut.tracing.annotation.NewSpan
import java.util.regex.Pattern
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@AnalyzeClasses(packages = ["ai.friday.openfinance"], importOptions = [ImportOption.DoNotIncludeTests::class, ExcludeBuildTmp::class])
@Execution(ExecutionMode.CONCURRENT)
internal class ArchitectureTest {

    @ArchTest
    fun `nenhum método deve usar a anotação Scheduled`(
        importedClasses: JavaClasses,
    ) {
        methods()
            .should().notBeAnnotatedWith(Scheduled::class.java)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes que extendem AbstractJob não podem ser final`(
        importedClasses: JavaClasses,
    ) {
        classes()
            .that().areAssignableTo(AbstractJob::class.java)
            .should().notHaveModifier(JavaModifier.FINAL)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes com métodos anotadas com NewSpan não podem ser final`(
        importedClasses: JavaClasses,
    ) {
        methods()
            .that().areAnnotatedWith(NewSpan::class.java)
            .should().beDeclaredInClassesThat().doNotHaveModifier(JavaModifier.FINAL)
            .check(importedClasses)
    }

    // FIAME - resolver e re-habilitar
    @ArchTest
    @ArchIgnore
    fun `todo método anotado como Async deve usar o asyncExecutorService`(importedClasses: JavaClasses) {
        methods()
            .that().areAnnotatedWith(Async::class.java)
            .should(useAsyncExecutionService)
            .check(importedClasses)
    }

    @ArchTest
    @ArchIgnore
    fun `micronaut dependencies should not reside inside domain files`(importedClasses: JavaClasses) {
        noClasses()
            .that().resideInAPackage("ai.friday.openfinance.app.(*)")
            .and().areNotAnnotatedWith(Generated::class.java)
            .should()
            .dependOnClassesThat().resideInAnyPackage("..micronaut..")
            .check(importedClasses)
    }

    @ArchTest
    fun testHexagonal(importedClasses: JavaClasses) {
        noClasses().that()
            .resideInAnyPackage("ai.friday.openfinance.app", "ai.friday.openfinance.app.(**)")
            .should().dependOnClassesThat()
            .resideInAnyPackage("ai.friday.openfinance.adapters", "ai.friday.openfinance.adapters.(**)")
            .check(importedClasses)
    }

    private val useAsyncExecutionService = UseAsyncExecutionService()
}

private class UseAsyncExecutionService : ArchCondition<JavaMethod>("use asyncExecutorService") {
    override fun check(item: JavaMethod, events: ConditionEvents) {
        if (item.annotations.single { it.type.name == Async::class.java.name }.properties["value"] != "asyncExecutorService") {
            val message = "${item.fullName} must be annotated with @Async(ASYNC_EXECUTOR_SERVICE)"

            events.add(SimpleConditionEvent.violated(item, message))
        }
    }
}

class ExcludeBuildTmp : ImportOption {
    private val pattern = Pattern.compile(".*/build/tmp/kapt./.*")
    override fun includes(location: Location?): Boolean {
        return !pattern.matcher(location.toString()).matches()
    }
}