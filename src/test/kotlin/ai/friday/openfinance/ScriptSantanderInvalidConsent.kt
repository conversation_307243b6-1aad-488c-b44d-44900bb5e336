package ai.friday.openfinance

import ai.friday.openfinance.adapters.dynamodb.AmazonDynamoDBFactory
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDynamoDAO
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.sweepingaccount.LimitUsage
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class ScriptSantanderInvalidConsent {

    private val amazonDynamoDBFactory = AmazonDynamoDBFactory(
        dynamodbRegion = "us-east-1",
    )

    private val dynamoDbEnhancedClient = amazonDynamoDBFactory.dynamoDbEnhancedClient()

    private val sweepingConsentDbRepository = SweepingConsentDbRepository(SweepingConsentDynamoDAO(dynamoDbEnhancedClient))
    private val sweepingPaymentDbRepository = SweepingPaymentDbRepository(SweepingPaymentDynamoDAO(dynamoDbEnhancedClient))

    @Test
    @Disabled
    fun foo() {
        val sweepingConsentIds = listOf(
            "urn:mais:6828eddac375ca44e4e0b431",
        )

        val consents = sweepingConsentIds.mapNotNull {
            sweepingConsentDbRepository.find(SweepingConsentId(it)).getOrNull()
        }

        println("consents: ${consents.size}")

        consents.forEach { consent ->
            val amountsConsumed = consent.toAmountsConsumed()

            val payments = sweepingPaymentDbRepository.findByConsentId(consent.consentId).sortedBy {
                it.createdAt
            }

            payments.forEach { payment ->
                amountsConsumed.add(payment)
            }
        }
    }

    private fun SweepingConsent.toAmountsConsumed() = listOfNotNull(
        LimitUsage.buildFrom(LimitType.DAILY),
        LimitUsage.buildFrom(LimitType.WEEKLY),
        LimitUsage.buildFrom(LimitType.MONTHLY),
        LimitUsage.buildFrom(LimitType.GLOBAL),
    )

    private fun List<LimitUsage>.add(payment: SweepingPayment) = forEach { it.add(payment) }
}