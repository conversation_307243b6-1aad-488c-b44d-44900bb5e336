package ai.friday.openfinance.app.connection

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import ai.friday.openfinance.adapters.dynamodb.DataConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.DataConsentDynamoDAO
import ai.friday.openfinance.adapters.sqs.SQSMessagePublisher
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentLink
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceDataConsentStatusMessage
import ai.friday.openfinance.app.integrations.CheckDataConsentStatusMessage
import ai.friday.openfinance.app.integrations.DataConsentAdapter
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class DataConsentServiceTest {
    private val dynamoDB = setupDynamoDB()
    private val dataConsentDynamoDAO = DataConsentDynamoDAO(dynamoDB)
    private val dataConsentRepository = DataConsentDbRepository(dataConsentDynamoDAO)

    private val dataConsentAdapter = mockk<DataConsentAdapter>()

    private val publisher = mockk<SQSMessagePublisher>(relaxed = true)

    private val dataConsentService = DataConsentService(
        dataConsentRepository = dataConsentRepository,
        dataConsentAdapter = dataConsentAdapter,
        permissions = listOf(DataConsentPermission.ACCOUNTS_ALL),
        publisher = publisher,
    )
    private val userAccountId = UserAccountId("test-account-id")
    private val document = Document("***********")

    @DisplayName("ao solicitar um consentimento")
    @Nested
    inner class RequestConsentTest {
        @Test
        fun `deve solicitar um link, salvar o consentimento e aguardar a autorizacao`() {
            val dataConsentId = DataConsentId("test-consent-id")
            val url = "http://test.com"
            every { dataConsentAdapter.requestDataConsent(any(), any(), any(), any()) } returns DataConsentLink(dataConsentId, url).right()

            val link = dataConsentService.requestConsent(
                userAccountId = userAccountId,
                document = document,
                participantId = ParticipantId("participantId"),
            )
            link.isRight() shouldBe true
            link.getOrNull() shouldBe DataConsentLink(dataConsentId, url)

            val consent = dataConsentRepository.find(DataConsentId("test-consent-id"))
            consent.userAccountId shouldBe userAccountId
            consent.id shouldBe dataConsentId
            consent.participantId.value shouldBe "participantId"
            consent.status shouldBe DataConsentStatus.PENDING_AUTHORISATION

            val slot = slot<CheckDataConsentStatusMessage>()
            verify {
                publisher.sendMessage(capture(slot))
            }
            with(slot.captured) {
                this.consentId shouldBe dataConsentId.value
            }
        }

        @Test
        fun `nao deve criar um link se a lista de permissoes for vazia`() {
            val dataConsentService = DataConsentService(
                dataConsentRepository = dataConsentRepository,
                dataConsentAdapter = dataConsentAdapter,
                permissions = emptyList(),
                publisher = mockk(),
            )

            dataConsentService.requestConsent(
                userAccountId = userAccountId,
                document = document,
                participantId = ParticipantId("participantId"),
            ).isLeft() shouldBe true
        }

        @Test
        fun `nao deve criar um link se houver erro na chamada ao adapter`() {
            every { dataConsentAdapter.requestDataConsent(any(), any(), any(), any()) } throws IllegalStateException()

            assertThrows<IllegalStateException> {
                dataConsentService.requestConsent(
                    userAccountId = userAccountId,
                    document = document,
                    participantId = ParticipantId("participantId"),
                )
            }

            verify {
                publisher wasNot called
            }
        }
    }

    @DisplayName("ao consultar um consentimento")
    @Nested
    inner class GetConsentTest {
        @ParameterizedTest
        @EnumSource(value = DataConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING_AUTHORISATION", "TEMPORARILY_UNAVAILABLE"])
        fun `deve retornar o estado salvo quando eh final`(status: DataConsentStatus) {
            val consent = setupConsent(status)

            val result = dataConsentService.getConsent(
                consentId = consent.id,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe status
            }

            verify {
                dataConsentAdapter wasNot called
            }
        }

        @ParameterizedTest
        @EnumSource(value = DataConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AVAILABLE", "UNAVAILABLE", "REVOKED"])
        fun `deve consultar o iniciador quando o estado salvo nao eh final`(consentStatus: DataConsentStatus) {
            val consent = setupConsent(consentStatus)

            val response = consent.toExternalLink(otherStatus = DataConsentStatus.AVAILABLE)

            every {
                dataConsentAdapter.getLink(consent.id)
            } returns response

            val now = consent.createdAt.plusMinutes(1)
            val result = withGivenDateTime(now) {
                dataConsentService.getConsent(
                    consentId = consent.id,
                )
            }

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe response.status
            }

            val messageSlot = slot<OpenFinanceDataConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                dataConsentId shouldBe consent.id.value
                userAccountId shouldBe consent.userAccountId.value
                status shouldBe response.status
            }

            with(dataConsentRepository.find(consent.id)) {
                this.id shouldBe response.dataConsentId
                this.userAccountId shouldBe consent.userAccountId
                this.status shouldBe response.status
                this.createdAt.format(dateTimeFormat) shouldBe consent.createdAt.format(dateTimeFormat)
                this.updatedAt.format(dateTimeFormat) shouldBe now.format(dateTimeFormat)
            }
        }

        @ParameterizedTest
        @EnumSource(value = DataConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AVAILABLE", "UNAVAILABLE", "REVOKED"])
        fun `deve cancelar um consentimento que nao chegou a um estado final ha mais de 10 minutos`(consentStatus: DataConsentStatus) {
            val consent = setupConsent(consentStatus, getZonedDateTime())

            val response = consent.toExternalLink()

            every {
                dataConsentAdapter.getLink(consent.id)
            } returns response

            val now = consent.createdAt.plusMinutes(11)
            val result = withGivenDateTime(now) {
                dataConsentService.getConsent(
                    consentId = consent.id,
                )
            }

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe DataConsentStatus.UNAVAILABLE
            }

            val messageSlot = slot<OpenFinanceDataConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                dataConsentId shouldBe consent.id.value
                userAccountId shouldBe consent.userAccountId.value
                status shouldBe DataConsentStatus.UNAVAILABLE
            }

            with(dataConsentRepository.find(consent.id)) {
                this.id shouldBe response.dataConsentId
                this.userAccountId shouldBe consent.userAccountId
                this.status shouldBe DataConsentStatus.UNAVAILABLE
                this.createdAt.format(dateTimeFormat) shouldBe consent.createdAt.format(dateTimeFormat)
                this.updatedAt.format(dateTimeFormat) shouldBe now.format(dateTimeFormat)
            }
        }

        @ParameterizedTest
        @EnumSource(value = DataConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AVAILABLE", "UNAVAILABLE"])
        fun `nao deve publicar mensagem de estado atualizado quando o estado no repositorio eh o mesmo do iniciador`(consentStatus: DataConsentStatus) {
            val consent = setupConsent(consentStatus)

            val response = consent.toExternalLink()

            every {
                dataConsentAdapter.getLink(consent.id)
            } returns response

            val result = dataConsentService.getConsent(
                consentId = consent.id,
            )

            result.isRight() shouldBe true

            verify {
                publisher wasNot called
            }
        }

        private fun setupConsent(status: DataConsentStatus, createdAt: ZonedDateTime = getZonedDateTime()): DataConsent {
            val consent = DataConsent(
                userAccountId = userAccountId,
                status = status,
                createdAt = createdAt,
                updatedAt = getZonedDateTime(),
                id = DataConsentId(),
                requestId = DataConsentRequestId(),
                permissions = listOf(),
                permissionsGranted = listOf(),
                bankId = "bankId",
                participantId = ParticipantId("participantId"),
                bankName = "bankName",
            )
            return dataConsentRepository.save(
                consent,
            )
        }

        private fun DataConsent.toExternalLink(otherStatus: DataConsentStatus = status) = ExternalLink(
            dataConsentId = id,
            status = otherStatus,
            redirectUrl = "",
            requestConsentId = requestId,
            bankId = bankId,
            bankName = bankName,
            permissions = permissions,
            permissionsGranted = permissionsGranted,
        )
    }

    @DisplayName("ao revogar um consentimento")
    @Nested
    inner class RevokeConsentTest {
        @Test
        fun `deve atualizar status`() {
            val userAccountId = UserAccountId("userAccountId")
            val consentId = DataConsentId("consentId")
            val consent = DataConsent(
                userAccountId = userAccountId,
                id = consentId,
                status = DataConsentStatus.AVAILABLE,
                requestId = DataConsentRequestId(),
                permissions = listOf(DataConsentPermission.ACCOUNTS_ALL),
                bankId = "bankId",
                bankName = "bankName",
                permissionsGranted = listOf(DataConsentPermission.ACCOUNTS_ALL),
                participantId = ParticipantId("participantId"),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )
            dataConsentRepository.save(consent)
            every { dataConsentAdapter.revokeDataConsent(any()) } returns Unit.right()
            dataConsentService.revokeConsent(consentId)
                .getOrNull()!!
                .let {
                    it shouldBe DataConsentStatus.REVOKED
                }
            verify { publisher.sendMessage(any<OpenFinanceDataConsentStatusMessage>()) }
        }
    }
}