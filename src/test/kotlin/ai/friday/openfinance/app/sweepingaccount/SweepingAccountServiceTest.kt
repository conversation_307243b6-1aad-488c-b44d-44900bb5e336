package ai.friday.openfinance.app.sweepingaccount

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentLimitUsageDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingCreditorDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingCreditorDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDynamoDAO
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorSweepingAccountAdapter
import ai.friday.openfinance.adapters.sqs.SQSMessagePublisher
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.CheckSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.CreateSweepingPaymentAdapterError
import ai.friday.openfinance.app.integrations.GetSweepingPaymentStatusError
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.SweepingParticipantAdapter
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentExternalId
import ai.friday.openfinance.app.integrations.SweepingPaymentMessage
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.DayOfWeek
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

class SweepingAccountServiceTest {
    private val dynamoDB = setupDynamoDB()
    private val sweepingCreditorDynamoDAO = SweepingCreditorDynamoDAO(dynamoDB)

    private val sweepingCreditorDbRepository = SweepingCreditorDbRepository(sweepingCreditorDynamoDAO)
    private val sweepingConsentRepository = SweepingConsentDbRepository(SweepingConsentDynamoDAO(dynamoDB))
    private val sweepingConsentLimitUsageRepository = SweepingConsentLimitUsageDbRepository(dynamoDB)
    private val sweepingPaymentDbRepository = SweepingPaymentDbRepository(SweepingPaymentDynamoDAO(dynamoDB))
    private val sweepingParticipantAdapter = mockk<SweepingParticipantAdapter>()

    private val endToEnd = UUID.randomUUID().toString()

    val manualRiskSignals = ManualSweepingRiskSignals(
        deviceId = "deviceId",
    )
    val automaticSweepingRiskSignals = AutomaticSweepingRiskSignals(
        lastLoginDateTime = getZonedDateTime(),
    )

    private val sweepingAccountAdapter = mockk<IniciadorSweepingAccountAdapter> {
        every { createCreditor(any()) } answers {
            val requestedCreditor: CreateCreditorRequest = firstArg()

            CreateCreditorResponse(
                id = CreditorId(),
                type = requestedCreditor.type,
                userTaxId = requestedCreditor.userTaxId,
                creditorTaxId = requestedCreditor.creditorTaxId,
                name = requestedCreditor.name,
                ispb = requestedCreditor.ispb,
                issuer = requestedCreditor.issuer,
                number = requestedCreditor.number,
                accountType = requestedCreditor.accountType,
                participant = null,
                status = "ACTIVE",
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )
        }
        every { requestSweepingConsent(any()) } answers {
            val requestedConsent: CreateConsentRequest = firstArg()

            CreateConsentResponse(
                consentId = SweepingConsentId(),
                user = requestedConsent.user,
                businessEntity = requestedConsent.user,
                participant = SweepingParticipant(id = requestedConsent.participantId.value, name = "participant"),
                sweepingLimits = requestedConsent.sweepingLimits,
                status = ConsentStatus.AUTHORISED,
                authUrl = "https://auth.url",
                startDateTime = getZonedDateTime(),
                expirationDateTime = getZonedDateTime(),
                additionalInformation = null,
                statusUpdateDateTime = getZonedDateTime(),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
                debtor = null,
            )
        }
        every { createPayment(any()) } answers {
            val requestedPayment: CreateSweepingPaymentRequest = firstArg()

            SweepingPaymentResponse(
                externalId = SweepingPaymentExternalId("paymentId"),
                consentId = requestedPayment.consentId,
                amount = requestedPayment.amount,
                description = requestedPayment.description,
                status = SweepingPaymentStatus.PROCESSING,
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
                date = getLocalDate(),
                endToEndId = endToEnd,
                fee = 0,
                method = "",
                riskSignals = automaticSweepingRiskSignals,
                participantId = "1",
                businessEntityTaxId = "",
                userTaxId = "***********",
                requestId = requestedPayment.id,
            ).right()
        }
    }

    private val publisher = mockk<SQSMessagePublisher>(relaxed = true)
    private val sweepingAccountService = SweepingAccountService(
        sweepingPaymentDbRepository,
        sweepingCreditorRepository = sweepingCreditorDbRepository,
        sweepingAccountAdapter = sweepingAccountAdapter,
        sweepingConsentRepository = sweepingConsentRepository,
        sweepingConsentLimitUsageRepository = sweepingConsentLimitUsageRepository,
        publisher = publisher,
        sweepingParticipantAdapter = sweepingParticipantAdapter,
        calcLimitUsage = true,
    )

    private val userAccountId = UserAccountId("userAccountId")

    private val createCreditorRequest = CreateCreditorRequest(
        type = CreditorType.BANK_ACCOUNT,
        userTaxId = Document("***********"),
        creditorTaxId = Document("***********111"),
        name = "name",
        ispb = "ispb",
        issuer = "issuer",
        number = "number",
        accountType = CreditorAccountType.CACC,
    )

    private val sweepingLimits = SweepingLimits(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimits(
            day = PeriodicLimit(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = PeriodicLimit(
                quantityLimit = 50,
                transactionLimit = 5_567_00,
            ),
            month = PeriodicLimit(
                quantityLimit = 100,
                transactionLimit = 10_350_00,
            ),
            year = PeriodicLimit(
                quantityLimit = 300,
                transactionLimit = 725_000_00,
            ),
        ),
    )

    private val userConsent = UserConsent(
        taxId = Document("***********"),
        name = "",
    )

    private val participantId = ParticipantId("1")

    @DisplayName("ao criar um consentimento")
    @Nested
    inner class CreateConsentTest {
        @Test
        fun `deve criar um creditor e buscá-lo no banco`() {
            val creditor = sweepingAccountService.createCreditor(userAccountId = userAccountId, createCreditorRequest)

            with(creditor) {
                type shouldBe createCreditorRequest.type
                userTaxId shouldBe createCreditorRequest.userTaxId
                creditorTaxId shouldBe createCreditorRequest.creditorTaxId
                name shouldBe createCreditorRequest.name
                ispb shouldBe createCreditorRequest.ispb
                issuer shouldBe createCreditorRequest.issuer
                number shouldBe createCreditorRequest.number
                accountType shouldBe createCreditorRequest.accountType
                userAccountId shouldBe userAccountId
            }

            val findResult = sweepingCreditorDbRepository.find(creditor.id)
            findResult.isRight() shouldBe true
            findResult.map {
                it.shouldBeEqualToIgnoringFields(creditor, SweepingCreditor::createdAt, SweepingCreditor::updatedAt)
            }
        }

        @Test
        fun `deve criar um consentimento e buscar no banco`() {
            val result = sweepingAccountService.requestConsent(
                userAccountId = userAccountId,
                createCreditorRequest = createCreditorRequest,
                user = userConsent,
                participantId = participantId,
                sweepingLimits = sweepingLimits,
            )

            result.isRight() shouldBe true

            val slot = slot<CreateConsentRequest>()
            verify {
                sweepingAccountAdapter.requestSweepingConsent(capture(slot))
            }

            val consentId = result.map { consent ->
                consent.userAccountId shouldBe userAccountId
                consent.creditors shouldBe slot.captured.creditors
                consent.user shouldBe userConsent
                consent.businessEntity shouldBe userConsent
                consent.participant.id shouldBe participantId.value
                consent.sweepingLimits shouldBe sweepingLimits
                consent.authUrl shouldBe "https://auth.url"
                consent.additionalInformation shouldBe null
                consent.debtor shouldBe null

                val findResult = sweepingConsentRepository.find(consent.consentId)

                findResult.isRight() shouldBe true
                findResult.map {
                    it.shouldBeEqualToIgnoringFields(consent, SweepingConsent::startDateTime, SweepingConsent::expirationDateTime, SweepingConsent::updatedAt, SweepingConsent::statusUpdateDateTime, SweepingConsent::createdAt, SweepingConsent::businessEntity)
                }

                consent.consentId
            }.getOrElse { TODO("should not happen") }

            val messageSlot = slot<CheckSweepingConsentStatusMessage>()
            verify {
                publisher.sendMessage(capture(messageSlot))
            }
            messageSlot.captured.consentId shouldBe consentId.value
        }
    }

    @DisplayName("ao consultar um consentimento")
    @Nested
    inner class GetConsentTest {
        @ParameterizedTest
        @EnumSource(value = ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AWAITING_AUTHORISATION", "PARTIALLY_ACCEPTED"])
        fun `deve retornar o estado salvo quando eh final`(status: ConsentStatus) {
            val consent = setupConsent(status)

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = false,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe status
            }

            verify {
                sweepingAccountAdapter wasNot called
            }
        }

        @ParameterizedTest
        @EnumSource(value = ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AUTHORISED", "REJECTED", "REVOKED", "CONSUMED", "CANCELED"])
        fun `deve consultar o iniciador quando o estado salvo nao eh final`(consentStatus: ConsentStatus) {
            val consent = setupConsent(consentStatus)

            val response = consent.toCreateConsentResponse(otherStatus = ConsentStatus.AUTHORISED)

            every {
                sweepingAccountAdapter.getConsent(consent.consentId)
            } returns response

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = false,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe response.status
            }

            val messageSlot = slot<OpenFinanceSweepingConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                sweepingConsentId shouldBe consent.consentId.value
                status shouldBe response.status
                debtor?.ispb shouldBe response.debtor?.ispb
                debtor?.routingNumber shouldBe response.debtor?.issuer
                debtor?.accountNumber shouldBe response.debtor?.number
                debtor?.accountType shouldBe response.debtor?.accountType
                debtor?.bankName shouldBe response.participant.name
            }

            val findResult = sweepingConsentRepository.find(consent.consentId)
            findResult.isRight() shouldBe true
            findResult.map {
                it.userAccountId shouldBe consent.userAccountId
                it.consentId shouldBe response.consentId
                it.participant shouldBe response.participant
                it.status shouldBe response.status
                it.authUrl shouldBe response.authUrl
                it.user shouldBe response.user
                it.businessEntity shouldBe response.businessEntity
                it.creditors shouldBe consent.creditors
                it.startDateTime?.format(dateTimeFormat) shouldBe response.startDateTime?.format(dateTimeFormat)
                it.expirationDateTime?.format(dateTimeFormat) shouldBe response.expirationDateTime?.format(dateTimeFormat)
                it.additionalInformation shouldBe response.additionalInformation
                it.statusUpdateDateTime.format(dateTimeFormat) shouldBe response.statusUpdateDateTime.format(dateTimeFormat)
                it.createdAt.format(dateTimeFormat) shouldBe consent.createdAt.format(dateTimeFormat)
                it.sweepingLimits shouldBe response.sweepingLimits
                it.debtor shouldBe response.debtor
            }
        }

        @Test
        fun `deve criar o LimitUsage ao mudar de estado para AUTHORISED`() {
            val consent = setupConsent(ConsentStatus.AWAITING_AUTHORISATION)

            val response = consent.toCreateConsentResponse(otherStatus = ConsentStatus.AUTHORISED)

            every {
                sweepingAccountAdapter.getConsent(consent.consentId)
            } returns response

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = false,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe response.status
            }

            val limitUsage = sweepingConsentLimitUsageRepository.find(consent.consentId).getOrNull()
            limitUsage.shouldNotBeNull()
            limitUsage.dailyAmount shouldBe 0
            limitUsage.dailyQuantity shouldBe 0
            limitUsage.weeklyAmount shouldBe 0
            limitUsage.weeklyQuantity shouldBe 0
            limitUsage.monthlyAmount shouldBe 0
            limitUsage.monthlyQuantity shouldBe 0
            limitUsage.yearlyAmount shouldBe 0
            limitUsage.yearlyQuantity shouldBe 0
        }

        @ParameterizedTest
        @EnumSource(value = ConsentStatus::class)
        fun `deve consultar o iniciador quando forceFectch e verdadeiro`(consentStatus: ConsentStatus) {
            val consent = setupConsent(consentStatus)

            val response = consent.toCreateConsentResponse(otherStatus = ConsentStatus.entries.first { it != consentStatus })

            every {
                sweepingAccountAdapter.getConsent(consent.consentId)
            } returns response

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = true,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe response.status
            }

            val messageSlot = slot<OpenFinanceSweepingConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                sweepingConsentId shouldBe consent.consentId.value
                status shouldBe response.status
                debtor?.ispb shouldBe response.debtor?.ispb
                debtor?.routingNumber shouldBe response.debtor?.issuer
                debtor?.accountNumber shouldBe response.debtor?.number
                debtor?.accountType shouldBe response.debtor?.accountType
                debtor?.bankName shouldBe response.participant.name
            }

            val findResult = sweepingConsentRepository.find(consent.consentId)
            findResult.isRight() shouldBe true
            findResult.map {
                it.userAccountId shouldBe consent.userAccountId
                it.consentId shouldBe response.consentId
                it.participant shouldBe response.participant
                it.status shouldBe response.status
                it.authUrl shouldBe response.authUrl
                it.user shouldBe response.user
                it.businessEntity shouldBe response.businessEntity
                it.creditors shouldBe consent.creditors
                it.startDateTime?.format(dateTimeFormat) shouldBe response.startDateTime?.format(dateTimeFormat)
                it.expirationDateTime?.format(dateTimeFormat) shouldBe response.expirationDateTime?.format(dateTimeFormat)
                it.additionalInformation shouldBe response.additionalInformation
                it.statusUpdateDateTime.format(dateTimeFormat) shouldBe response.statusUpdateDateTime.format(dateTimeFormat)
                it.createdAt.format(dateTimeFormat) shouldBe consent.createdAt.format(dateTimeFormat)
                it.sweepingLimits shouldBe response.sweepingLimits
                it.debtor shouldBe response.debtor
            }
        }

        @ParameterizedTest
        @EnumSource(value = ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AUTHORISED", "REJECTED", "REVOKED", "CONSUMED", "CANCELED"])
        fun `deve cancelar um consentimento que nao chegou a um estado final ha mais de 10 minutos`(consentStatus: ConsentStatus) {
            val consent = setupConsent(consentStatus, getZonedDateTime().minusMinutes(11))

            val response = consent.toCreateConsentResponse()

            every {
                sweepingAccountAdapter.getConsent(consent.consentId)
            } returns response

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = false,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe ConsentStatus.CANCELED
            }

            val messageSlot = slot<OpenFinanceSweepingConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                sweepingConsentId shouldBe consent.consentId.value
                status shouldBe ConsentStatus.CANCELED
                debtor?.ispb shouldBe response.debtor?.ispb
                debtor?.routingNumber shouldBe response.debtor?.issuer
                debtor?.accountNumber shouldBe response.debtor?.number
                debtor?.accountType shouldBe response.debtor?.accountType
                debtor?.bankName shouldBe response.participant.name
            }

            val findResult = sweepingConsentRepository.find(consent.consentId)
            findResult.isRight() shouldBe true
            findResult.map {
                it.userAccountId shouldBe consent.userAccountId
                it.consentId shouldBe response.consentId
                it.participant shouldBe response.participant
                it.status shouldBe ConsentStatus.CANCELED
                it.authUrl shouldBe response.authUrl
                it.user shouldBe response.user
                it.businessEntity shouldBe response.businessEntity
                it.creditors shouldBe consent.creditors
                it.startDateTime?.format(dateTimeFormat) shouldBe response.startDateTime?.format(dateTimeFormat)
                it.expirationDateTime?.format(dateTimeFormat) shouldBe response.expirationDateTime?.format(dateTimeFormat)
                it.additionalInformation shouldBe response.additionalInformation
                it.statusUpdateDateTime.format(dateTimeFormat) shouldBe response.statusUpdateDateTime.format(dateTimeFormat)
                it.createdAt.format(dateTimeFormat) shouldBe consent.createdAt.format(dateTimeFormat)
                it.sweepingLimits shouldBe response.sweepingLimits
                it.debtor shouldBe response.debtor
            }
        }

        @ParameterizedTest
        @EnumSource(value = ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AUTHORISED", "REJECTED", "REVOKED", "CONSUMED", "CANCELED"])
        fun `nao deve publicar mensagem de estado atualizado quando o estado no repositorio eh o mesmo do iniciador`(consentStatus: ConsentStatus) {
            val consent = setupConsent(consentStatus)

            val response = consent.toCreateConsentResponse()

            every {
                sweepingAccountAdapter.getConsent(consent.consentId)
            } returns response

            val result = sweepingAccountService.getConsent(
                consentId = consent.consentId,
                forceFetch = false,
            )

            result.isRight() shouldBe true

            verify {
                publisher wasNot called
            }
        }
    }

    @DisplayName("ao revogar um consentimento")
    @Nested
    inner class RevokeConsentTest {
        @Test
        fun `deve atualizar status`() {
            val userAccountId = UserAccountId("userAccountId")
            val consentId = SweepingConsentId("consentId")
            val consent = SweepingConsent(
                userAccountId = userAccountId,
                consentId = consentId,
                participant = SweepingParticipant("1", name = "participant"),
                status = ConsentStatus.AUTHORISED,
                authUrl = "https://auth.url",
                user = UserConsent(
                    taxId = Document("***********"),
                    name = "",
                ),
                businessEntity = UserConsent(
                    taxId = Document("***********111"),
                    name = "",
                ),
                creditors = listOf(),
                startDateTime = getZonedDateTime(),
                expirationDateTime = getZonedDateTime(),
                additionalInformation = null,
                statusUpdateDateTime = getZonedDateTime(),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
                sweepingLimits = sweepingLimits,
                debtor = null,
            )
            sweepingConsentRepository.save(consent)
            every { sweepingAccountAdapter.revokeConsent(any()) } returns ConsentStatus.REVOKED
            sweepingAccountService.revokeConsent(consentId)
                .getOrNull()!!
                .let {
                    it shouldBe ConsentStatus.REVOKED
                }
            verify { publisher.sendMessage(any<OpenFinanceSweepingConsentStatusMessage>()) }
        }

        @Test
        @Disabled
        fun `deve retornar not found se o consentimento não estiver AUTHORISED`() {
            val userAccountId = UserAccountId("userAccountId")
            val consentId = SweepingConsentId("consentId")
            val consent = SweepingConsent(
                userAccountId = userAccountId,
                consentId = consentId,
                participant = SweepingParticipant("1", name = "participant"),
                status = ConsentStatus.AWAITING_AUTHORISATION,
                authUrl = "https://auth.url",
                user = UserConsent(
                    taxId = Document("***********"),
                    name = "",
                ),
                businessEntity = UserConsent(
                    taxId = Document("***********111"),
                    name = "",
                ),
                creditors = listOf(),
                startDateTime = getZonedDateTime(),
                expirationDateTime = getZonedDateTime(),
                additionalInformation = null,
                statusUpdateDateTime = getZonedDateTime(),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
                sweepingLimits = sweepingLimits,
                debtor = null,
            )
            sweepingConsentRepository.save(consent)
            val result = sweepingAccountService.revokeConsent(consentId)
            result.isLeft() shouldBe true
            result.onLeft {
                (it is SweepingAccountServiceError.ItemNotFound) shouldBe true
            }
        }
    }

    @DisplayName("ao criar um pagamento")
    @Nested
    inner class CreatePaymentTest {
        @Test
        fun `deve suportar riskSignals AUTOMATIC`() {
            val consent = sweepingAccountService.requestConsent(
                userAccountId = userAccountId,
                createCreditorRequest = createCreditorRequest,
                user = userConsent,
                participantId = participantId,
                sweepingLimits = sweepingLimits,
            ).getOrElse {
                throw it
            }

            val request = CreateSweepingPaymentRequest(
                id = SweepingPaymentRequestId(),
                consentId = consent.consentId,
                creditorId = consent.creditors.first(),
                amount = 100L,
                description = "description",
                riskSignals = automaticSweepingRiskSignals,
            )

            val result = sweepingAccountService.createPayment(request)

            result.isRight() shouldBe true
            result.map { payment ->
                payment.userAccountId shouldBe consent.userAccountId
                payment.consentId shouldBe request.consentId
                payment.creditorId shouldBe request.creditorId
                payment.amount shouldBe request.amount
                payment.requestId shouldBe request.id
                payment.status shouldBe SweepingPaymentStatus.PROCESSING
                payment.endToEndId shouldBe endToEnd
                payment.error shouldBe null

                val findResult = sweepingPaymentDbRepository.find(payment.requestId)

                findResult.isRight() shouldBe true
                findResult.map {
                    it.shouldBeEqualToIgnoringFields(payment, payment::createdAt, payment::updatedAt)
                }
            }

            val slot = slot<CreateSweepingPaymentRequest>()
            verify {
                sweepingAccountAdapter.createPayment(capture(slot))
            }

            with(slot.captured.riskSignals) {
                this.shouldBeTypeOf<AutomaticSweepingRiskSignals>()
                this shouldBe automaticSweepingRiskSignals
            }
        }

        @Test
        fun `deve suportar riskSignals MANUAL`() {
            val consent = sweepingAccountService.requestConsent(
                userAccountId = userAccountId,
                createCreditorRequest = createCreditorRequest,
                user = userConsent,
                participantId = participantId,
                sweepingLimits = sweepingLimits,
            ).getOrElse {
                throw it
            }

            val request = CreateSweepingPaymentRequest(
                id = SweepingPaymentRequestId(),
                consentId = consent.consentId,
                creditorId = consent.creditors.first(),
                amount = 100L,
                description = "description",
                riskSignals = manualRiskSignals,
            )

            val result = sweepingAccountService.createPayment(request)

            result.isRight() shouldBe true
            result.map { payment ->
                payment.userAccountId shouldBe consent.userAccountId
                payment.consentId shouldBe request.consentId
                payment.creditorId shouldBe request.creditorId
                payment.amount shouldBe request.amount
                payment.requestId shouldBe request.id
                payment.status shouldBe SweepingPaymentStatus.PROCESSING
                payment.endToEndId shouldBe endToEnd
                payment.error shouldBe null

                val findResult = sweepingPaymentDbRepository.find(payment.requestId)

                findResult.isRight() shouldBe true
                findResult.map {
                    it.shouldBeEqualToIgnoringFields(payment, payment::createdAt, payment::updatedAt)
                }
            }

            val slot = slot<CreateSweepingPaymentRequest>()
            verify {
                sweepingAccountAdapter.createPayment(capture(slot))
            }

            with(slot.captured.riskSignals) {
                this.shouldBeTypeOf<ManualSweepingRiskSignals>()
                this.shouldBeEqualToIgnoringFields(manualRiskSignals, manualRiskSignals::accountTenure)
                this.accountTenure shouldBe consent.createdAt.toLocalDate()
            }
        }

        @Test
        fun `deve salvar pagamento mesmo com erro no adapter`() {
            val consent = sweepingAccountService.requestConsent(
                userAccountId = userAccountId,
                createCreditorRequest = createCreditorRequest,
                user = userConsent,
                participantId = participantId,
                sweepingLimits = sweepingLimits,
            ).getOrElse {
                throw it
            }

            val request = CreateSweepingPaymentRequest(
                id = SweepingPaymentRequestId(),
                consentId = consent.consentId,
                creditorId = consent.creditors.first(),
                amount = 100L,
                description = "description",
                riskSignals = manualRiskSignals,
            )
            every { sweepingAccountAdapter.createPayment(any()) } returns CreateSweepingPaymentAdapterError.LimitExceededError(LimitType.DAILY).left()

            val result = sweepingAccountService.createPayment(request)
            result.isLeft() shouldBe true

            val savedResult = sweepingPaymentDbRepository.find(request.id)
            savedResult.isRight() shouldBe true
            savedResult.map {
                it.creditorId shouldBe request.creditorId
                it.consentId shouldBe request.consentId
                it.userAccountId shouldBe userAccountId
                it.amount shouldBe request.amount
                it.description shouldBe request.description
                it.riskSignals.shouldBeEqualToIgnoringFields(request.riskSignals, ManualSweepingRiskSignals::accountTenure)
                it.status shouldBe SweepingPaymentStatus.FAILED
                it.endToEndId shouldBe null
            }
        }

        @DisplayName("e o iniciador retornar erro no consentimento")
        @Nested
        inner class InvalidConsentStatusTest {
            @Test
            fun `deve consultar o estado do consentimento`() {
                val consent = sweepingAccountService.requestConsent(
                    userAccountId = userAccountId,
                    createCreditorRequest = createCreditorRequest,
                    user = userConsent,
                    participantId = participantId,
                    sweepingLimits = sweepingLimits,
                ).getOrElse {
                    throw it
                }
                clearMocks(publisher)

                val request = CreateSweepingPaymentRequest(
                    id = SweepingPaymentRequestId(),
                    consentId = consent.consentId,
                    creditorId = consent.creditors.first(),
                    amount = 100L,
                    description = "description",
                    riskSignals = manualRiskSignals,
                )

                every { sweepingAccountAdapter.createPayment(any()) } returns CreateSweepingPaymentAdapterError.InvalidConsentStatus.left()

                val result = sweepingAccountService.createPayment(request)
                result.isLeft() shouldBe true

                val savedResult = sweepingPaymentDbRepository.find(request.id)
                savedResult.isRight() shouldBe true
                savedResult.map {
                    it.creditorId shouldBe request.creditorId
                    it.consentId shouldBe request.consentId
                    it.userAccountId shouldBe userAccountId
                    it.amount shouldBe request.amount
                    it.description shouldBe request.description
                    it.riskSignals.shouldBeEqualToIgnoringFields(request.riskSignals, ManualSweepingRiskSignals::accountTenure)
                    it.status shouldBe SweepingPaymentStatus.FAILED
                    it.endToEndId shouldBe null
                }

                val slot = slot<CheckSweepingConsentStatusMessage>()
                verify {
                    publisher.sendMessage(capture(slot))
                }
                slot.captured.consentId shouldBe consent.consentId.value
                slot.captured.forceFetch shouldBe true
            }
        }

        @DisplayName("e o iniciador retornar um erro generico")
        @Nested
        inner class GenericErrorTest {
            @Test
            fun `deve consultar o pagamento e atualizar o seu estado se ele existir`() {
                val consent = sweepingAccountService.requestConsent(
                    userAccountId = userAccountId,
                    createCreditorRequest = createCreditorRequest,
                    user = userConsent,
                    participantId = participantId,
                    sweepingLimits = sweepingLimits,
                ).getOrElse {
                    throw it
                }

                val request = CreateSweepingPaymentRequest(
                    id = SweepingPaymentRequestId(),
                    consentId = consent.consentId,
                    creditorId = consent.creditors.first(),
                    amount = 100L,
                    description = "description",
                    riskSignals = manualRiskSignals,
                )

                every { sweepingAccountAdapter.createPayment(any()) } returns CreateSweepingPaymentAdapterError.GenericError(IllegalStateException()).left()

                every {
                    sweepingAccountAdapter.getPaymentStatus(any(), any())
                } returns SweepingPaymentStatusResponse(
                    requestId = request.id,
                    externalId = null,
                    status = SweepingPaymentStatus.FAILED,
                    error = SweepingPaymentStatusError(
                        code = "CODE",
                        title = "TITLE",
                        detail = "DETAIL",
                    ),
                    description = "vocibus",
                    date = null,
                    createdAt = getZonedDateTime(),
                    updatedAt = null,
                    consentId = consent.consentId,
                    endToEndId = endToEnd,
                    amount = request.amount,
                    fee = 0,
                    method = null,
                    participantId = "suscipiantur",
                    businessEntityTaxId = "",
                    userTaxId = null,

                ).right()

                val result = sweepingAccountService.createPayment(request)

                result.isRight() shouldBe true
                result.map { payment ->
                    payment.userAccountId shouldBe consent.userAccountId
                    payment.consentId shouldBe request.consentId
                    payment.creditorId shouldBe request.creditorId
                    payment.amount shouldBe request.amount
                    payment.requestId shouldBe request.id
                    payment.status shouldBe SweepingPaymentStatus.FAILED
                    payment.endToEndId shouldBe endToEnd
                    payment.error shouldBe null

                    val findResult = sweepingPaymentDbRepository.find(payment.requestId)

                    findResult.isRight() shouldBe true
                    findResult.map {
                        it.shouldBeEqualToIgnoringFields(payment, payment::createdAt, payment::updatedAt)
                    }
                }

                val slot = slot<CreateSweepingPaymentRequest>()
                verify {
                    sweepingAccountAdapter.createPayment(capture(slot))
                }

                with(slot.captured.riskSignals) {
                    this.shouldBeTypeOf<ManualSweepingRiskSignals>()
                    this.shouldBeEqualToIgnoringFields(manualRiskSignals, manualRiskSignals::accountTenure)
                    this.accountTenure shouldBe consent.createdAt.toLocalDate()
                }

                verify {
                    sweepingAccountAdapter.getPaymentStatus(consent.consentId, request.id)
                }
            }

            @Test
            fun `deve consultar o pagamento e retornar erro se ele não existir`() {
                val consent = sweepingAccountService.requestConsent(
                    userAccountId = userAccountId,
                    createCreditorRequest = createCreditorRequest,
                    user = userConsent,
                    participantId = participantId,
                    sweepingLimits = sweepingLimits,
                ).getOrElse {
                    throw it
                }

                val request = CreateSweepingPaymentRequest(
                    id = SweepingPaymentRequestId(),
                    consentId = consent.consentId,
                    creditorId = consent.creditors.first(),
                    amount = 100L,
                    description = "description",
                    riskSignals = manualRiskSignals,
                )

                every { sweepingAccountAdapter.createPayment(any()) } returns CreateSweepingPaymentAdapterError.GenericError(IllegalStateException()).left()

                every {
                    sweepingAccountAdapter.getPaymentStatus(any(), any())
                } returns GetSweepingPaymentStatusError.PaymentNotFound("").left()

                val result = sweepingAccountService.createPayment(request)

                result.isLeft() shouldBe true
                result.mapLeft {
                    it.shouldBeTypeOf<CreateSweepingPaymentServiceError.PaymentNotCreated>()
                }

                verify {
                    sweepingAccountAdapter.getPaymentStatus(consent.consentId, request.id)
                }

                val paymentResult = sweepingPaymentDbRepository.find(request.id)
                paymentResult.isRight() shouldBe true
                paymentResult.map { payment ->
                    payment.status shouldBe SweepingPaymentStatus.FAILED
                }
            }
        }
    }

    @DisplayName("ao consultar um pagamento")
    @Nested
    inner class GetPaymentTest {
        @ParameterizedTest
        @EnumSource(value = SweepingPaymentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED", "PROCESSING", "UNKNOWN"])
        fun `deve retornar o estado salvo quando eh final`(status: SweepingPaymentStatus) {
            val payment = buildPayment(status)

            sweepingPaymentDbRepository.save(
                payment,
            )

            val result = sweepingAccountService.getPaymentStatus(
                paymentRequestId = payment.requestId,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe status
            }

            verify {
                sweepingAccountAdapter wasNot called
            }
        }

        @ParameterizedTest
        @EnumSource(value = SweepingPaymentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["FAILED", "SUCCESS"])
        fun `deve consultar o iniciador quando o estado salvo nao eh final`(status: SweepingPaymentStatus) {
            val payment = buildPayment(status)

            sweepingPaymentDbRepository.save(
                payment,
            )

            val response = SweepingPaymentStatusResponse(
                requestId = payment.requestId,
                externalId = SweepingPaymentExternalId("EXTERNAL_ID"),
                status = SweepingPaymentStatus.FAILED,
                error = SweepingPaymentStatusError(
                    code = "code",
                    title = "title",
                    detail = "detail",
                ),
                description = "integer",
                date = null,
                createdAt = getZonedDateTime(),
                updatedAt = null,
                consentId = SweepingConsentId(value = "errem"),
                endToEndId = null,
                amount = 4567,
                fee = null,
                method = null,
                participantId = "quod",
                businessEntityTaxId = null,
                userTaxId = null,

            )

            every {
                sweepingAccountAdapter.getPaymentStatus(payment.consentId, payment.requestId)
            } returns response.right()

            val result = sweepingAccountService.getPaymentStatus(
                paymentRequestId = payment.requestId,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe response.status
                it.externalId shouldBe response.externalId
                it.error shouldBe response.error
            }

            val findResult = sweepingPaymentDbRepository.find(payment.requestId)
            findResult.isRight() shouldBe true
            findResult.map {
                it.status shouldBe response.status
                it.externalId shouldBe response.externalId
                it.error shouldBe response.error?.code
                it.errorDescription shouldBe response.error?.detail
            }
        }
    }

    @DisplayName("ao atualizar um pagamento")
    @Nested
    inner class UpdatePaymentTest {

        @Test
        fun `deve atualizar o limite utilizado ser for SUCCESS pela primeira vez e a flag calcLimitUsage estiver habilitada`() {
            val payment = buildPayment(SweepingPaymentStatus.CREATED)
            every { sweepingParticipantAdapter.getParticipants(any()) } returns mapOf("1" to "NomeDoBanco")
            sweepingPaymentDbRepository.save(payment)
            sweepingConsentLimitUsageRepository.save(sweepingAccountService.buildInitialLimitUsage(payment.consentId))

            val command = UpdatePaymentCommand(
                paymentRequestId = payment.requestId,
                status = SweepingPaymentStatus.SUCCESS,
                error = null,
                errorDescription = null,
            )

            val result = sweepingAccountService.updatePayment(command)

            result.isRight() shouldBe true

            val today = getLocalDate()
            val sweepingConsentLimitUsage = sweepingConsentLimitUsageRepository.find(payment.consentId).getOrNull()
            sweepingConsentLimitUsage.shouldNotBeNull()
            sweepingConsentLimitUsage.dailyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.dailyQuantity shouldBe 1
            sweepingConsentLimitUsage.dailyWindowStart shouldBe today
            sweepingConsentLimitUsage.dailyWindowEnd shouldBe today.plusDays(1)
            sweepingConsentLimitUsage.weeklyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.weeklyQuantity shouldBe 1
            sweepingConsentLimitUsage.weeklyWindowStart shouldBe today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY))
            sweepingConsentLimitUsage.weeklyWindowEnd shouldBe today.with(TemporalAdjusters.next(DayOfWeek.SUNDAY))
            sweepingConsentLimitUsage.monthlyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.monthlyQuantity shouldBe 1
            sweepingConsentLimitUsage.monthlyWindowStart shouldBe today.with(TemporalAdjusters.firstDayOfMonth())
            sweepingConsentLimitUsage.monthlyWindowEnd shouldBe today.with(TemporalAdjusters.firstDayOfNextMonth())
            sweepingConsentLimitUsage.yearlyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.yearlyQuantity shouldBe 1
            sweepingConsentLimitUsage.yearlyWindowStart shouldBe today.with(TemporalAdjusters.firstDayOfYear())
            sweepingConsentLimitUsage.yearlyWindowEnd shouldBe today.with(TemporalAdjusters.firstDayOfNextYear())
            sweepingConsentLimitUsage.totalAmount shouldBe payment.amount
        }

        @Test
        fun `não deve atualizar o limite utilizado ser for SUCCESS pela primeira vez mas a flag calcLimitUsage estiver desabilitada`() {
            val payment = buildPayment(SweepingPaymentStatus.CREATED)
            every { sweepingParticipantAdapter.getParticipants(any()) } returns mapOf("1" to "NomeDoBanco")
            sweepingPaymentDbRepository.save(payment)
            sweepingConsentLimitUsageRepository.save(sweepingAccountService.buildInitialLimitUsage(payment.consentId))
            val limitUsage = sweepingAccountService.buildInitialLimitUsage(payment.consentId)
            sweepingConsentLimitUsageRepository.save(limitUsage)

            val command = UpdatePaymentCommand(
                paymentRequestId = payment.requestId,
                status = SweepingPaymentStatus.SUCCESS,
                error = null,
                errorDescription = null,
            )

            val sweepingAccountServiceWithCalcLimitUsageDisabled = SweepingAccountService(
                sweepingPaymentDbRepository,
                sweepingCreditorRepository = sweepingCreditorDbRepository,
                sweepingAccountAdapter = sweepingAccountAdapter,
                sweepingConsentRepository = sweepingConsentRepository,
                sweepingConsentLimitUsageRepository = sweepingConsentLimitUsageRepository,
                publisher = publisher,
                sweepingParticipantAdapter = sweepingParticipantAdapter,
                calcLimitUsage = false,
            )

            val result = sweepingAccountServiceWithCalcLimitUsageDisabled.updatePayment(command)

            result.isRight() shouldBe true

            val sweepingConsentLimitUsage = sweepingConsentLimitUsageRepository.find(payment.consentId).getOrNull()
            sweepingConsentLimitUsage shouldBe limitUsage
        }

        @Test
        fun `não deve atualizar o limite utilizado ser for SUCCESS pela segunda vez`() {
            val payment = buildPayment(SweepingPaymentStatus.CREATED)
            every { sweepingParticipantAdapter.getParticipants(any()) } returns mapOf("1" to "NomeDoBanco")
            sweepingPaymentDbRepository.save(payment)
            sweepingConsentLimitUsageRepository.save(sweepingAccountService.buildInitialLimitUsage(payment.consentId))

            val command = UpdatePaymentCommand(
                paymentRequestId = payment.requestId,
                status = SweepingPaymentStatus.SUCCESS,
                error = null,
                errorDescription = null,
            )

            val firstResult = sweepingAccountService.updatePayment(command)
            firstResult.isRight() shouldBe true

            val secondResult = sweepingAccountService.updatePayment(command)
            secondResult.isRight() shouldBe true

            val today = getLocalDate()
            val sweepingConsentLimitUsage = sweepingConsentLimitUsageRepository.find(payment.consentId).getOrNull()
            sweepingConsentLimitUsage.shouldNotBeNull()
            sweepingConsentLimitUsage.dailyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.dailyQuantity shouldBe 1
            sweepingConsentLimitUsage.dailyWindowStart shouldBe today
            sweepingConsentLimitUsage.dailyWindowEnd shouldBe today.plusDays(1)
            sweepingConsentLimitUsage.weeklyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.weeklyQuantity shouldBe 1
            sweepingConsentLimitUsage.weeklyWindowStart shouldBe today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY))
            sweepingConsentLimitUsage.weeklyWindowEnd shouldBe today.with(TemporalAdjusters.next(DayOfWeek.SUNDAY))
            sweepingConsentLimitUsage.monthlyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.monthlyQuantity shouldBe 1
            sweepingConsentLimitUsage.monthlyWindowStart shouldBe today.with(TemporalAdjusters.firstDayOfMonth())
            sweepingConsentLimitUsage.monthlyWindowEnd shouldBe today.with(TemporalAdjusters.firstDayOfNextMonth())
            sweepingConsentLimitUsage.yearlyAmount shouldBe payment.amount
            sweepingConsentLimitUsage.yearlyQuantity shouldBe 1
            sweepingConsentLimitUsage.yearlyWindowStart shouldBe today.with(TemporalAdjusters.firstDayOfYear())
            sweepingConsentLimitUsage.yearlyWindowEnd shouldBe today.with(TemporalAdjusters.firstDayOfNextYear())
            sweepingConsentLimitUsage.totalAmount shouldBe payment.amount
        }

        @ParameterizedTest
        @EnumSource(value = SweepingPaymentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS"])
        fun `não deve atualizar o limite utilizado quando não for SUCCESS`(status: SweepingPaymentStatus) {
            val payment = buildPayment(SweepingPaymentStatus.CREATED)
            every { sweepingParticipantAdapter.getParticipants(any()) } returns mapOf("1" to "NomeDoBanco")
            sweepingPaymentDbRepository.save(payment)
            val limitUsage = sweepingAccountService.buildInitialLimitUsage(payment.consentId)
            sweepingConsentLimitUsageRepository.save(limitUsage)

            val command = UpdatePaymentCommand(
                paymentRequestId = payment.requestId,
                status = status,
                error = null,
                errorDescription = null,
            )

            val firstResult = sweepingAccountService.updatePayment(command)
            firstResult.isRight() shouldBe true

            val sweepingConsentLimitUsage = sweepingConsentLimitUsageRepository.find(payment.consentId).getOrNull()
            sweepingConsentLimitUsage shouldBe limitUsage
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "PROCESSING, null, null",
                "SUCCESS, null, null",
                "FAILED, consentimento, rejeitado",
            ],
            nullValues = ["null"],
        )
        fun `deve postar a atualização na fila`(expectedStatus: SweepingPaymentStatus, updatedError: String?, updatedErrorDescription: String?) {
            val payment = buildPayment(SweepingPaymentStatus.CREATED)
            every { sweepingParticipantAdapter.getParticipants(any()) } returns mapOf("1" to "NomeDoBanco")
            sweepingPaymentDbRepository.save(payment)
            sweepingConsentLimitUsageRepository.save(sweepingAccountService.buildInitialLimitUsage(payment.consentId))

            val command = UpdatePaymentCommand(
                paymentRequestId = payment.requestId,
                status = expectedStatus,
                error = updatedError,
                errorDescription = updatedErrorDescription,
            )

            val result = sweepingAccountService.updatePayment(command)

            result.isRight() shouldBe true
            result.map { updatedPayment ->
                updatedPayment.shouldBeEqualToIgnoringFields(payment, payment::createdAt, payment::updatedAt, payment::status, payment::error, payment::errorDescription)
                updatedPayment.status shouldBe expectedStatus
                updatedPayment.error shouldBe updatedError
                updatedPayment.errorDescription shouldBe updatedErrorDescription

                val findResult = sweepingPaymentDbRepository.find(payment.requestId)

                findResult.isRight() shouldBe true
                findResult.map {
                    it.shouldBeEqualToIgnoringFields(updatedPayment, updatedPayment::createdAt, updatedPayment::updatedAt)
                }
            }

            val slot = slot<SweepingPaymentMessage>()
            verify { publisher.sendMessage(capture(slot)) }
            with(slot.captured) {
                requestId shouldBe payment.requestId.value
                status shouldBe expectedStatus
                amount shouldBe payment.amount
                error shouldBe updatedError
                errorDescription shouldBe updatedErrorDescription
            }
        }
    }

    private fun buildPayment(status: SweepingPaymentStatus) = SweepingPayment(
        requestId = SweepingPaymentRequestId(),
        externalId = SweepingPaymentExternalId("paymentId"),
        userAccountId = userAccountId,
        consentId = SweepingConsentId(),
        date = getLocalDate(),
        description = "referrentur",
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        endToEndId = "test",
        status = status,
        amount = 1683,
        fee = null,
        method = null,
        riskSignals = automaticSweepingRiskSignals,
        participantId = "non",
        businessEntityTaxId = null,
        userTaxId = null,
        creditorId = CreditorId(),
        error = null,
        errorDescription = null,
    )

    private fun setupConsent(status: ConsentStatus, createdAt: ZonedDateTime = getZonedDateTime()): SweepingConsent {
        val consent = SweepingConsent(
            userAccountId = userAccountId,
            consentId = SweepingConsentId(),
            participant = SweepingParticipant("1", name = "participant"),
            status = status,
            authUrl = "https://auth.url",
            user = UserConsent(
                taxId = Document("***********"),
                name = "",
            ),
            businessEntity = UserConsent(
                taxId = Document("***********111"),
                name = "",
            ),
            creditors = listOf(),
            startDateTime = getZonedDateTime(),
            expirationDateTime = getZonedDateTime(),
            additionalInformation = null,
            statusUpdateDateTime = getZonedDateTime(),
            createdAt = createdAt,
            updatedAt = getZonedDateTime(),
            sweepingLimits = sweepingLimits,
            debtor = null,
        )
        return sweepingConsentRepository.save(
            consent,
        )
    }

    private fun SweepingConsent.toCreateConsentResponse(otherStatus: ConsentStatus = status) = CreateConsentResponse(
        consentId = consentId,
        participant = participant,
        status = otherStatus,
        authUrl = authUrl,
        user = user,
        businessEntity = businessEntity,
        startDateTime = startDateTime,
        expirationDateTime = expirationDateTime,
        additionalInformation = additionalInformation,
        statusUpdateDateTime = statusUpdateDateTime,
        createdAt = createdAt,
        updatedAt = updatedAt,
        sweepingLimits = sweepingLimits,
        debtor = SweepingDebtor(
            ispb = "ispb",
            issuer = "issuer",
            number = "number",
            accountType = "accountType",
        ),
    )
}