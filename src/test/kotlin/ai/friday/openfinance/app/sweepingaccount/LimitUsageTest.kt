package ai.friday.openfinance.app.sweepingaccount

import ai.friday.morning.date.brazilTimeZone
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class LimitUsageTest {
    fun mocckPayment(
        sweepingPaymentStatus: SweepingPaymentStatus,
        sweepingAmount: Long,
        sweepingCreatedAt: Long = 0,
    ) = mockk<SweepingPayment> {
        every {
            status
        } returns sweepingPaymentStatus
        every {
            amount
        } returns sweepingAmount
        every {
            createdAt
        } returns ZonedDateTime.of(2025, 7, 13, 1, 1, 1, 1, brazilTimeZone).plusDays(sweepingCreatedAt)
    }

    fun mocckPayment(
        sweepingPaymentStatus: SweepingPaymentStatus,
        sweepingAmount: Long,
        sweepingCreatedAt: ZonedDateTime,
    ) = mockk<SweepingPayment> {
        every {
            status
        } returns sweepingPaymentStatus
        every {
            amount
        } returns sweepingAmount
        every {
            createdAt
        } returns sweepingCreatedAt
    }

    private fun LimitUsage.check(periodicLimit: PeriodicLimit) = CheckResult(
        quantityExceeded = quantitySuccessful > periodicLimit.quantityLimit,
        amountExceeded = amountSuccessful > periodicLimit.transactionLimit,
    )

    @Test
    fun testDayLimit() {
        val periodicLimit = PeriodicLimit(
            transactionLimit = 3,
            quantityLimit = 2,
        )

        val limitUsage = LimitUsage.buildFrom(LimitType.DAILY)

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 1))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 4))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = 1))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }
    }

    @Test
    fun testWeekLimit() {
        val periodicLimit = PeriodicLimit(
            transactionLimit = 3,
            quantityLimit = 2,
        )

        val limitUsage = LimitUsage.buildFrom(LimitType.WEEKLY)

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 1))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 4))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = 6))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = 7))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }
    }

    @Test
    fun testMonthLimit() {
        val periodicLimit = PeriodicLimit(
            transactionLimit = 3,
            quantityLimit = 2,
        )
        val limitUsage = LimitUsage.buildFrom(LimitType.MONTHLY)

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 1))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 4))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = 18))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = -12))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = 19))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }
    }

    @Test
    fun testYearLimit() {
        val periodicLimit = PeriodicLimit(
            transactionLimit = 3,
            quantityLimit = 2,
        )
        val limitUsage = LimitUsage.buildFrom(LimitType.YEARLY)

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 1))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 4))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = ZonedDateTime.of(2025, 12, 31, 1, 1, 1, 1, brazilTimeZone)))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = ZonedDateTime.of(2025, 1, 1, 1, 1, 1, 1, brazilTimeZone)))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe true
            amountExceeded shouldBe true
        }

        limitUsage.add(mocckPayment(sweepingPaymentStatus = SweepingPaymentStatus.SUCCESS, sweepingAmount = 2, sweepingCreatedAt = ZonedDateTime.of(2026, 1, 1, 1, 1, 1, 1, brazilTimeZone)))

        with(limitUsage.check(periodicLimit)) {
            quantityExceeded shouldBe false
            amountExceeded shouldBe false
        }
    }
}