package ai.friday.openfinance.app.creditcard

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.openfinance.adapters.dynamodb.CreditCardDbRepository
import ai.friday.openfinance.adapters.dynamodb.CreditCardDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.CreditCardInvoiceDbRepository
import ai.friday.openfinance.adapters.dynamodb.CreditCardInvoiceDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.CreditCardTransactionDbRepository
import ai.friday.openfinance.adapters.dynamodb.CreditCardTransactionDynamoDAO
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.utils.toYearMonth
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CreditCardServiceIntegrationTest {

    private val userAccountId = UserAccountId("test-user-id")
    private val dataConsentId = DataConsentId("test-consent-id")
    private val creditCardId = CreditCardId("test-card-id")
    private val creditCardExternalId = CreditCardExternalId("test-card-external-id")
    private val invoiceId = CreditCardInvoiceId("test-invoice-id")
    private val invoiceExternalId = CreditCardInvoiceExternalId("test-invoice-external-id")
    private val dataConsent = mockk<DataConsent> {
        every { id } returns dataConsentId
        every { status } returns DataConsentStatus.AVAILABLE
    }

    private val dynamoDB = setupDynamoDB()
    private val creditCardRepository = CreditCardDbRepository(CreditCardDynamoDAO(dynamoDB))
    private val creditCardInvoiceRepository = CreditCardInvoiceDbRepository(CreditCardInvoiceDynamoDAO(dynamoDB))
    private val creditCardTransactionRepository = CreditCardTransactionDbRepository(CreditCardTransactionDynamoDAO(dynamoDB))
    private val dataConsentRepository = mockk<DataConsentRepository> {
        every {
            findByUserAccountId(any())
        } returns listOf(dataConsent)
    }

    private val creditCardService = CreditCardService(
        creditCardRepository = creditCardRepository,
        creditCardInvoiceRepository = creditCardInvoiceRepository,
        creditCardTransactionRepository = creditCardTransactionRepository,
        dataConsentRepository = dataConsentRepository,
        creditCardAdapter = mockk(),
        publisher = mockk(),
        refreshCreditCardHistoryEnabled = true,
    )

    @BeforeEach
    fun setup() {
        createCreditCard()
        createInvoices()
        createTransactions()
    }

    @Nested
    inner class CalculateUserDebtByMonthTest {
        @Test
        fun `should calculate user debt by month correctly`() {
            // Arrange
            val since = getLocalDate().minusMonths(3).toYearMonth()

            // Act
            val result = creditCardService.calculateUserDebtByMonth(userAccountId, since)

            // Assert
            result.shouldNotBeNull()
            result.size shouldBe 4

            result[getLocalDate().toYearMonth()]?.totalAmount shouldBe 100_00L
            result[getLocalDate().toYearMonth()]?.financedAmount shouldBe 0L

            result[getLocalDate().toYearMonth().minusMonths(1)]?.totalAmount shouldBe 200_00L
            result[getLocalDate().toYearMonth().minusMonths(1)]?.financedAmount shouldBe 0L

            result[getLocalDate().toYearMonth().minusMonths(2)]?.totalAmount shouldBe 180_00L
            result[getLocalDate().toYearMonth().minusMonths(2)]?.financedAmount shouldBe 0L

            result[getLocalDate().toYearMonth().minusMonths(3)]?.totalAmount shouldBe 150_00L
            result[getLocalDate().toYearMonth().minusMonths(3)]?.financedAmount shouldBe 5_00L
        }
    }

    @Nested
    inner class GetOpenTransactionsByCardTest {
        @Test
        fun `should get open transactions by card correctly`() {
            // Act
            val result = creditCardService.getOpenTransactionsByCard(userAccountId)

            // Assert
            result.shouldNotBeNull()
            result.size shouldBe 1
            result[0].creditCardId shouldBe creditCardId
            result[0].transactions.size shouldBe 2 // Assuming 2 open transactions
            result[0].totalAmount shouldBe 600_00
        }
    }

    @Nested
    inner class GetTransactionsByMonthTest {
        @Test
        fun `should get transactions by month correctly`() {
            // Act
            val result = creditCardService.getTransactionsByMonth(userAccountId, getLocalDate().toYearMonth())

            // Assert
            result.shouldNotBeNull()
            result.size shouldBe 1 // Assuming only one month of transactions
        }
    }

    private fun createCreditCard(): CreditCard {
        return CreditCard(
            creditCardId = creditCardId,
            creditCardExternalId = creditCardExternalId,
            dataConsentId = dataConsentId,
            brandName = "VISA",
            companyCnpj = "**************",
            name = "Cartão Teste 1",
            productType = "CREDIT_CARD",
            productAdditionalInfo = "Cartão Gold",
            creditCardNetwork = "VISA",
            networkAdditionalInfo = "Visa Gold",
            provider = OFProvider.INICIADOR,
        ).also {
            creditCardRepository.save(it, userAccountId)
        }
    }

    private fun createInvoices(): List<CreditCardInvoice> {
        val baseInvoice = CreditCardInvoice(
            invoiceId = invoiceId,
            invoiceExternalId = invoiceExternalId,
            dueDate = getLocalDate(),
            billTotalAmount = 100_00L,
            billMinimumAmount = 100L,
            billCurrency = "BRL",
            isInstalment = false,
            financeCharges = emptyList(),
            payments = emptyList(),
            provider = OFProvider.INICIADOR,
        )
        val invoices = listOf(
            baseInvoice,
            baseInvoice.copy(invoiceId = CreditCardInvoiceId(UUID.randomUUID().toString()), billTotalAmount = 200_00L, dueDate = getLocalDate().minusMonths(1)),
            baseInvoice.copy(invoiceId = CreditCardInvoiceId(UUID.randomUUID().toString()), billTotalAmount = 180_00L, dueDate = getLocalDate().minusMonths(2)),
            baseInvoice.copy(
                invoiceId = CreditCardInvoiceId(UUID.randomUUID().toString()),
                billTotalAmount = 150_00L,
                dueDate = getLocalDate().minusMonths(3),
                financeCharges = listOf(
                    CreditCardFinanceCharges(
                        type = "INTEREST",
                        additionalInfo = "Juros de atraso",
                        amount = 5_00L,
                        currency = "BRL",
                    ),
                ),
            ),
            baseInvoice.copy(invoiceId = CreditCardInvoiceId(UUID.randomUUID().toString()), billTotalAmount = 150_00L, dueDate = getLocalDate().minusMonths(4)),
        )

        return invoices.onEach { creditCardInvoiceRepository.save(it, dataConsentId, userAccountId, creditCardId) }
    }

    private fun createTransactions(): List<CreditCardTransaction> {
        val baseTransaction = CreditCardTransaction(
            dataConsentId = dataConsentId,
            transactionExternalId = CreditCardTransactionExternalId(UUID.randomUUID().toString()),
            identificationNumber = "123456",
            transactionName = "Compra Teste",
            invoiceId = invoiceId,
            creditDebitType = "CREDIT",
            transactionType = "PURCHASE",
            transactionalAdditionalInfo = "Compra em loja",
            paymentType = "CREDIT",
            feeType = "NONE",
            feeTypeAdditionalInfo = "Sem taxa",
            otherCreditsType = "NONE",
            otherCreditsAdditionalInfo = "Sem outros créditos",
            chargeIdentificator = "charge-1",
            chargeNumber = "charge-1",
            originalAmount = CreditCardBillAmount(1000L, "BRL"),
            amount = 10_00L,
            transactionDateTime = ZonedDateTime.parse("2024-03-20T10:00:00Z", DateTimeFormatter.ISO_DATE_TIME),
            billPostDate = "2024-03-20",
            payeeMCC = 5411,
            raw = "raw-data",
            provider = OFProvider.INICIADOR,
        )

        return listOf(
            baseTransaction,
            baseTransaction.copy(transactionExternalId = CreditCardTransactionExternalId(UUID.randomUUID().toString()), amount = 200_00L),
            baseTransaction.copy(transactionExternalId = CreditCardTransactionExternalId(UUID.randomUUID().toString()), amount = 250_00L, invoiceId = null),
            baseTransaction.copy(transactionExternalId = CreditCardTransactionExternalId(UUID.randomUUID().toString()), amount = 350_00L, invoiceId = null),
        ).onEach { transaction ->
            creditCardTransactionRepository.save(transaction, userAccountId, creditCardId)
        }
    }
}