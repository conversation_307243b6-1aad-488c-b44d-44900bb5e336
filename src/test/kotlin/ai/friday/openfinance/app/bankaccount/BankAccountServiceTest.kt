package ai.friday.openfinance.app.bankaccount

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.adapters.dynamodb.BankAccountDbRepository
import ai.friday.openfinance.adapters.dynamodb.BankAccountDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.BankTransactionDbRepository
import ai.friday.openfinance.adapters.dynamodb.BankTransactionDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.DataConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.DataConsentDynamoDAO
import ai.friday.openfinance.adapters.iniciador.OFBankAccountAmountTO
import ai.friday.openfinance.adapters.iniciador.OFBankAccountDataTO
import ai.friday.openfinance.adapters.iniciador.OFBankAccountTO
import ai.friday.openfinance.adapters.iniciador.OFBankAccountTransactionTO
import ai.friday.openfinance.adapters.sqs.SQSMessagePublisher
import ai.friday.openfinance.app.AccountSubType
import ai.friday.openfinance.app.AccountType
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.BankAccountMeta
import ai.friday.openfinance.app.CompletedAuthorisedPaymentType
import ai.friday.openfinance.app.CreditDebitType
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.TransactionPersonType
import ai.friday.openfinance.app.TransactionType
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.BankAccountAdapter
import ai.friday.openfinance.app.integrations.SyncBankAccountEventTO
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.netty.util.internal.StringUtil.EMPTY_STRING
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class BankAccountServiceTest {
    private val dynamoDB = setupDynamoDB()
    private val dataConsentDynamoDAO = DataConsentDynamoDAO(dynamoDB)
    private val bankAccountDynamoDAO = BankAccountDynamoDAO(dynamoDB)
    private val bankTransactionDynamoDAO = BankTransactionDynamoDAO(dynamoDB)

    private val openFinanceConnectionRepository = DataConsentDbRepository(dataConsentDynamoDAO)
    private val dataConsentService: DataConsentService = mockk()
    private val bankAccountRepository = spyk(BankAccountDbRepository(bankAccountDynamoDAO))
    private val bankTransactionRepository = BankTransactionDbRepository(bankTransactionDynamoDAO)
    private val bankAccountAdapter = mockk<BankAccountAdapter>()

    private val publisher = mockk<SQSMessagePublisher>(relaxed = true)
    private val bankAccountService = spyk(
        BankAccountService(
            bankAccountRepository = bankAccountRepository,
            bankAccountAdapter = bankAccountAdapter,
            publisher = publisher,
            syncBankAccountTransactionsQueueName = "queue-name",
            transactionRepository = bankTransactionRepository,
            kmsService = mockk(relaxed = true),
            dataConsentService = dataConsentService,
        ),
    )
    val bankAccountId_1 = BankAccountId(value = "BANK-123")
    val bankAccountId_2 = BankAccountId(value = "BANK-456")
    val bankAccountId_3 = BankAccountId(value = "BANK-789")
    val userAccountId_1 = UserAccountId(value = "ACCOUNT-123")
    val userAccountId_2 = UserAccountId(value = "ACCOUNT-456")
    val dataConsentId_1 = DataConsentId("CONSENT-123")
    val dataConsentId_2 = DataConsentId("CONSENT-456")
    val dataConsentId_3 = DataConsentId("CONSENT-789")
    val requestConsentId_1 = DataConsentRequestId("REQUEST-CONSENT--123")
    val requestConsentId_2 = DataConsentRequestId("REQUEST-CONSENT--456")
    val requestConsentId_3 = DataConsentRequestId("REQUEST-CONSENT--789")

    @DisplayName("ao sincronizar as transacoes de uma conta bancaria")
    @Nested
    inner class SyncBankTransactionsTest {
        @Test
        fun `deve sincronizar as transacoes de uma conta bancaria`() {
            val bankAccount = BankAccountMeta(
                dataConsentId = dataConsentId_1,
                userAccountId = userAccountId_1,
                bankAccountId = bankAccountId_1,
                bankCode = "001",
                raw = EMPTY_STRING,
            )
            bankAccountRepository.save(bankAccount)
            val transactionList = listOf(
                OFBankAccountTransactionTO(completedAuthorisedPaymentType = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA, partieBranchCode = "012", transactionName = "test1", transactionId = "1", partieCnpjCpf = "***********", type = TransactionType.PIX, transactionDateTime = "2024-06-03T17:20:05.09Z", partieCompeCode = "001", transactionAmount = OFBankAccountAmountTO(amount = "0.01", currency = "BRL"), partiePersonType = TransactionPersonType.PESSOA_NATURAL, creditDebitType = CreditDebitType.CREDITO, partieNumber = "0001", partieCheckDigit = "01"),
                OFBankAccountTransactionTO(completedAuthorisedPaymentType = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA, partieBranchCode = "012", transactionName = "test1", transactionId = "2", partieCnpjCpf = "***********", type = TransactionType.PIX, transactionDateTime = "2024-06-01T11:28:44.578Z", partieCompeCode = "002", transactionAmount = OFBankAccountAmountTO(amount = "0.02", currency = "BRL"), partiePersonType = TransactionPersonType.PESSOA_NATURAL, creditDebitType = CreditDebitType.CREDITO, partieNumber = "0002", partieCheckDigit = "01"),
                OFBankAccountTransactionTO(completedAuthorisedPaymentType = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA, partieBranchCode = "012", transactionName = "test1", transactionId = "3", partieCnpjCpf = "***********", type = TransactionType.PIX, transactionDateTime = "2024-06-01 11:28:44", partieCompeCode = "002", transactionAmount = OFBankAccountAmountTO(amount = "0.03", currency = "BRL"), partiePersonType = TransactionPersonType.PESSOA_NATURAL, creditDebitType = CreditDebitType.CREDITO, partieNumber = "0002", partieCheckDigit = "01"),
            )
            every { bankAccountAdapter.getBankAccountTransactions(any(), any(), any(), any(), any()) } returns transactionList.map { it.toDomain() }.right()

            bankAccountService.syncBankTransactions(false, userAccountId_1, dataConsentId_1, bankAccountId_1)

            val transactions = bankAccountService.findBankTransactions(userAccountId_1, bankAccountId_1).getOrNull()

            transactions?.size shouldBe 3
            verify(exactly = 3) {
                publisher.sendMessage(any<OpenFinanceBankTransactionMessage>())
            }
        }

        @Test
        fun `deve processar apenas as transacoes que ainda nao foram processadas`() {
            val bankAccount = BankAccountMeta(
                dataConsentId = dataConsentId_1,
                userAccountId = userAccountId_1,
                bankAccountId = bankAccountId_1,
                bankCode = "001",
                raw = EMPTY_STRING,
            )

            val transactionList = listOf(
                OFBankAccountTransactionTO(completedAuthorisedPaymentType = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA, partieBranchCode = "012", transactionName = "test1", transactionId = "1", partieCnpjCpf = "***********", type = TransactionType.PIX, transactionDateTime = "2024-05-01T02:59:00Z", partieCompeCode = "001", transactionAmount = OFBankAccountAmountTO(amount = "0.01", currency = "BRL"), partiePersonType = TransactionPersonType.PESSOA_NATURAL, creditDebitType = CreditDebitType.CREDITO, partieNumber = "0001", partieCheckDigit = "01"),
                OFBankAccountTransactionTO(completedAuthorisedPaymentType = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA, partieBranchCode = "012", transactionName = "test2", transactionId = "2", partieCnpjCpf = "***********", type = TransactionType.PIX, transactionDateTime = "2024-06-29T02:59:00.000Z", partieCompeCode = "002", transactionAmount = OFBankAccountAmountTO(amount = "0.02", currency = "BRL"), partiePersonType = TransactionPersonType.PESSOA_NATURAL, creditDebitType = CreditDebitType.CREDITO, partieNumber = "0002", partieCheckDigit = "01"),
            )
            every { bankAccountAdapter.getBankAccountTransactions(any(), any(), any(), any(), any()) } returns transactionList.map { it.toDomain() }.right()

            bankAccountRepository.save(bankAccount)
            bankTransactionRepository.save(transactionList.first().toDomain().toBankTransactionMeta(bankAccountId_1))
            bankTransactionRepository.find(transactionList.first().transactionId, bankAccountId_1).isRight() shouldBe true

            bankAccountService.syncBankTransactions(false, userAccountId_1, dataConsentId_1, bankAccountId_1)

            val transactions = bankAccountService.findBankTransactions(userAccountId_1, bankAccountId_1).getOrNull()

            transactions?.size shouldBe 2
            verify(exactly = 1) {
                publisher.sendMessage(any<OpenFinanceBankTransactionMessage>())
            }
        }

        @Test
        fun `deve sincronizar todas as transacoes recentes de um usuario`() {
            val dataConsents = listOf(
                DataConsent(
                    userAccountId = userAccountId_1,
                    id = dataConsentId_1,
                    requestId = requestConsentId_1,
                    status = DataConsentStatus.AVAILABLE,
                    permissions = listOf(
                        DataConsentPermission.ACCOUNTS_ALL,
                    ),
                    bankId = "bankId",
                    bankName = "bankName",
                    permissionsGranted = listOf(),
                    participantId = ParticipantId("participantId"),
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
                DataConsent(
                    userAccountId = userAccountId_1,
                    id = dataConsentId_2,
                    requestId = requestConsentId_2,
                    status = DataConsentStatus.AVAILABLE,
                    permissions = listOf(
                        DataConsentPermission.ACCOUNTS_ALL,
                    ),
                    bankId = "bankId",
                    bankName = "bankName",
                    permissionsGranted = listOf(),
                    participantId = ParticipantId("participantId"),
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
                DataConsent(
                    userAccountId = userAccountId_2,
                    id = dataConsentId_3,
                    requestId = requestConsentId_3,
                    status = DataConsentStatus.UNAVAILABLE,
                    permissions = listOf(
                        DataConsentPermission.ACCOUNTS_ALL,
                    ),
                    bankId = "bankId",
                    bankName = "bankName",
                    permissionsGranted = listOf(),
                    participantId = ParticipantId("participantId"),
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
            )
            every {
                dataConsentService.findAllByStatus(any())
            } answers {
                dataConsents.filter { it.status == firstArg() }
            }

            bankAccountRepository.save(BankAccountMeta(userAccountId = userAccountId_1, dataConsentId = dataConsentId_2, bankAccountId = bankAccountId_2, bankCode = "tale", createdAt = getZonedDateTime(), updatedAt = getZonedDateTime(), raw = EMPTY_STRING))
            bankAccountRepository.save(BankAccountMeta(userAccountId = userAccountId_1, dataConsentId = dataConsentId_1, bankAccountId = bankAccountId_1, bankCode = "tale", createdAt = getZonedDateTime(), updatedAt = getZonedDateTime(), raw = EMPTY_STRING))
            bankAccountRepository.save(BankAccountMeta(userAccountId = userAccountId_2, dataConsentId = dataConsentId_3, bankAccountId = bankAccountId_3, bankCode = "tale", createdAt = getZonedDateTime(), updatedAt = getZonedDateTime(), raw = EMPTY_STRING))

            bankAccountService.syncBankTransactionsCurrent()

            val slot = mutableListOf<SyncBankAccountEventTO>()
            verify(exactly = 2) {
                publisher.sendMessage(any(), capture(slot), any())
            }

            slot.shouldContainExactlyInAnyOrder(
                SyncBankAccountEventTO(
                    consentId = dataConsentId_1.value,
                    userAccountId = userAccountId_1.value,
                    bankAccountId = bankAccountId_1.value,
                    recentTransactions = true,
                ),
                SyncBankAccountEventTO(
                    consentId = dataConsentId_2.value,
                    userAccountId = userAccountId_1.value,
                    bankAccountId = bankAccountId_2.value,
                    recentTransactions = true,
                ),
            )

            verify(exactly = 0) {
                publisher.sendMessage(
                    any(),
                    SyncBankAccountEventTO(
                        dataConsentId_3.value,
                        userAccountId_2.value,
                        bankAccountId_3.value,
                        recentTransactions = true,
                    ),
                    any(),
                )
            }
        }
    }

    @DisplayName("ao processar uma conexao disponivel")
    @Nested
    inner class ProcessAvailableConnectionTest {

        @ParameterizedTest
        @EnumSource(value = DataConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AVAILABLE"])
        fun `nao deve processar a conexao quando nao estiver com status AVAILABLE`(consentStatus: DataConsentStatus) {
            val dataConsent = setupConsent(consentStatus)

            val result = bankAccountService.processAvailableConnection(dataConsent)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable>()
            }

            verify(exactly = 0) {
                bankAccountService.createBankAccounts(any())
            }
        }

        @Test
        fun `nao deve processar a conexao quando nao receber as permissoes necessarias`() {
            val dataConsent = setupConsent(DataConsentStatus.AVAILABLE)

            val result = bankAccountService.processAvailableConnection(dataConsent)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<OpenFinanceConnectionError.PermissionsNotGranted>()
            }

            verify(exactly = 0) {
                bankAccountService.createBankAccounts(any())
            }
        }

        @Test
        fun `deve processar a conexao quando o link status for AVAILABLE`() {
            val dataConsent = setupConsent(DataConsentStatus.AVAILABLE, listOf(DataConsentPermission.ACCOUNTS_ALL))

            every { dataConsentService.getConsent(any()) } returns dataConsent.right()

            every { bankAccountAdapter.getBankAccounts(any()) } returns listOf(
                OFBankAccountTO(
                    type = AccountType.CONTA_PAGAMENTO_PRE_PAGA,
                    compeCode = "mentitum",
                    branchCode = "civibus",
                    number = "platonem",
                    checkDigit = "nonumy",
                    brandName = "Cedric Davenport",
                    accountId = bankAccountId_1.value,
                    companyCnpj = "consetetur",
                ).toDomain(),
            ).right()

            every { bankAccountAdapter.getBankAccountData(any(), any()) } answers {
                OFBankAccountDataTO(
                    type = AccountType.CONTA_PAGAMENTO_PRE_PAGA,
                    compeCode = "ante",
                    branchCode = "agam",
                    number = "gloriatur",
                    checkDigit = "ocurreret",
                    subtype = AccountSubType.CONJUNTA_SOLIDARIA,
                    currency = "quidam",
                ).toDomain(secondArg()).right()
            }

            val result = bankAccountService.processAvailableConnection(dataConsent)

            result.isRight() shouldBe true

            val slot = slot<DataConsent>()
            verify(exactly = 1) {
                bankAccountService.createBankAccounts(capture(slot))
            }

            slot.captured.status shouldBe DataConsentStatus.AVAILABLE
        }

        private fun setupConsent(consentStatus: DataConsentStatus, permissionsGranted: List<DataConsentPermission> = listOf()): DataConsent {
            val dataConsent = DataConsent(
                userAccountId = userAccountId_1,
                id = dataConsentId_1,
                requestId = requestConsentId_1,
                status = consentStatus,
                permissions = listOf(
                    DataConsentPermission.ACCOUNTS_ALL,
                ),
                bankId = "bankId",
                bankName = "bankName",
                permissionsGranted = permissionsGranted,
                participantId = ParticipantId("participantId"),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )
            every { dataConsentService.getConsent(any()) } returns dataConsent.right()

            return dataConsent
        }
    }

    @DisplayName("ao criar uma conta bancaria")
    @Nested
    inner class CreateBankAccountsTest {

        @Test
        fun `nao deve criar as contas bancarias quando o link status nao for autorizado`() {
            val openFinanceConnection = DataConsent(
                userAccountId = userAccountId_1,
                id = dataConsentId_1,
                requestId = requestConsentId_1,
                status = DataConsentStatus.UNAVAILABLE,
                permissions = listOf(
                    DataConsentPermission.ACCOUNTS_ALL,
                ),
                bankId = "bankId",
                bankName = "bankName",
                permissionsGranted = listOf(),
                participantId = ParticipantId("participantId"),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )

            val result = bankAccountService.createBankAccounts(openFinanceConnection)

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable
            }

            verify(exactly = 0) {
                bankAccountAdapter.getBankAccounts(dataConsentId_1)
                bankAccountAdapter.getBankAccountData(dataConsentId_1, any())
                bankAccountRepository.save(any())
                publisher.sendMessage(any<OpenFinanceBankAccountMessage>())
                publisher.sendMessage(any(), any<SyncBankAccountEventTO>(), any())
            }
        }

        @Test
        fun `nao deve criar as contas bancarias quando nao for possivel buscar as contas no iniciador`() {
            val openFinanceConnection = DataConsent(
                userAccountId = userAccountId_1,
                id = dataConsentId_1,
                requestId = requestConsentId_1,
                status = DataConsentStatus.AVAILABLE,
                permissions = listOf(
                    DataConsentPermission.ACCOUNTS_ALL,
                ),
                bankId = "bankId",
                bankName = "bankName",
                permissionsGranted = listOf(),
                participantId = ParticipantId("participantId"),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )

            every { bankAccountAdapter.getBankAccounts(dataConsentId_1) } returns Exception().left()

            val result = bankAccountService.createBankAccounts(openFinanceConnection)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeInstanceOf<OpenFinanceConnectionError.ServerError>()
            }

            verify(exactly = 1) {
                bankAccountAdapter.getBankAccounts(dataConsentId_1)
            }

            verify(exactly = 0) {
                bankAccountAdapter.getBankAccountData(dataConsentId_1, any())
                bankAccountRepository.save(any())
                publisher.sendMessage(any<OpenFinanceBankAccountMessage>())
                publisher.sendMessage(any(), any<SyncBankAccountEventTO>(), any())
            }
        }

        @Test
        fun `deve criar as contas bancarias quando for possivel buscar as contas no iniciador`() {
            val openFinanceConnection = DataConsent(
                userAccountId = userAccountId_1,
                id = dataConsentId_1,
                requestId = requestConsentId_1,
                status = DataConsentStatus.AVAILABLE,
                permissions = listOf(
                    DataConsentPermission.ACCOUNTS_ALL,
                ),
                bankId = "bankId",
                bankName = "bankName",
                permissionsGranted = listOf(),
                participantId = ParticipantId("participantId"),
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
            )

            every { bankAccountAdapter.getBankAccounts(any()) } returns listOf(
                OFBankAccountTO(
                    type = AccountType.CONTA_PAGAMENTO_PRE_PAGA,
                    compeCode = "mentitum",
                    branchCode = "civibus",
                    number = "platonem",
                    checkDigit = "nonumy",
                    brandName = "Cedric Davenport",
                    accountId = bankAccountId_1.value,
                    companyCnpj = "consetetur",
                ).toDomain(),
            ).right()

            every { bankAccountAdapter.getBankAccountData(any(), any()) } answers {
                OFBankAccountDataTO(
                    type = AccountType.CONTA_PAGAMENTO_PRE_PAGA,
                    compeCode = "ante",
                    branchCode = "agam",
                    number = "gloriatur",
                    checkDigit = "ocurreret",
                    subtype = AccountSubType.CONJUNTA_SOLIDARIA,
                    currency = "quidam",
                ).toDomain(secondArg()).right()
            }

            val result = bankAccountService.createBankAccounts(openFinanceConnection)

            result.isRight() shouldBe true

            val slot = slot<BankAccountMeta>()

            verify(exactly = 1) {
                bankAccountAdapter.getBankAccounts(dataConsentId_1)
                bankAccountAdapter.getBankAccountData(dataConsentId_1, bankAccountId_1)
                bankAccountRepository.save(capture(slot))
                publisher.sendMessage(any<OpenFinanceBankAccountMessage>())
                publisher.sendMessage(any(), any<SyncBankAccountEventTO>(), any())
            }

            slot.captured.bankAccountId.value shouldBe bankAccountId_1.value
            slot.captured.dataConsentId.value shouldBe dataConsentId_1.value

            val bankAccount = bankAccountRepository.find(bankAccountId_1).getOrNull()
        }
    }
}