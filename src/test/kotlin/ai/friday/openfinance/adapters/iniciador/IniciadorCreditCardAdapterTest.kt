package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.date.dateFormat
import ai.friday.morning.json.getObjectMapper
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.creditcard.CreditCardExternalId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceExternalId
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.coEvery
import io.mockk.mockk
import java.net.URL
import java.time.LocalDate
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

@Disabled
class IniciadorCreditCardAdapterTest {
    private val clientId = ""
    private val clientSecret = ""

    private val dataConsentId = DataConsentId("")
    private val creditCardExternalId = CreditCardExternalId("")
    private val invoiceExternalId = CreditCardInvoiceExternalId("")

    private val httpClient = RxHttpClient.create(URL("https://data.iniciador.com.br"))
    private val configuration = IniciadorConfiguration(
        accessTokenPath = "/v1/data/auth",
        clientId = clientId,
        clientSecret = clientSecret,
    )

    val authenticationManager = IniciadorAuthenticationManager(
        httpClient = httpClient,
        configuration = configuration,
    )

    val iniciadorLinkAdapter = IniciadorDataConsentAdapter(
        authenticationManager,
        httpClient = httpClient,
    )

    val iniciadorCreditCardAdapter = IniciadorCreditCardAdapter(
        authenticationManager,
        httpClient = httpClient,
        mockk {
            coEvery {
                encryptData(any())
            } answers {
                firstArg<String>()
            }
        },
    )

    @Test
    fun `get resources`() {
        val resources = iniciadorLinkAdapter.getResources(
            dataConsentId,
        )
        println(resources)
    }

    @Test
    fun `get creditCards`() {
        iniciadorCreditCardAdapter.getCreditCards(dataConsentId).fold(
            ifRight = { println(getObjectMapper().writeValueAsString(it)) },
            ifLeft = { fail(it) },
        )
    }

    @Test
    fun `get creditCardInvoices`() {
        iniciadorCreditCardAdapter.getCreditCardInvoices(
            dataConsentId,
            creditCardExternalId,
        ).fold(
            ifRight = { println(getObjectMapper().writeValueAsString(it)) },
            ifLeft = { fail(it) },
        )
    }

    @Test
    fun `get creditCardInvoiceDetails`() {
        iniciadorCreditCardAdapter.getCreditCardInvoiceTransactions(
            dataConsentId,
            creditCardExternalId,
            invoiceExternalId,
        ).fold(
            ifRight = { println(getObjectMapper().writeValueAsString(it)) },
            ifLeft = { fail(it) },
        )
    }

    @Test
    fun `get creditCardTransactions`() {
        iniciadorCreditCardAdapter.getCreditCardTransactions(
            dataConsentId,
            creditCardExternalId,
            LocalDate.parse("2025-04-01", dateFormat),
            LocalDate.parse("2025-04-30", dateFormat),
        ).fold(
            ifRight = { println(getObjectMapper().writeValueAsString(it)) },
            ifLeft = { fail(it) },
        )
    }
}