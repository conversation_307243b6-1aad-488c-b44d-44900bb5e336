package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.json.getObjectMapper
import ai.friday.openfinance.adapters.api.toCreateCreditorRequest
import ai.friday.openfinance.adapters.iniciador.sweeping.ConsentConfigurationTO
import ai.friday.openfinance.adapters.iniciador.sweeping.DayPeriodicLimitTO
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorSweepingAccountAdapter
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorSweepingParticipantAdapter
import ai.friday.openfinance.adapters.iniciador.sweeping.MonthPeriodicLimitTO
import ai.friday.openfinance.adapters.iniciador.sweeping.PeriodicLimitsTO
import ai.friday.openfinance.adapters.iniciador.sweeping.WeekPeriodicLimitTO
import ai.friday.openfinance.adapters.iniciador.sweeping.YearPeriodicLimitTO
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.sweepingaccount.CreateConsentRequest
import ai.friday.openfinance.app.sweepingaccount.CreditorAccountType
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.CreditorType
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimit
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingCreditor
import ai.friday.openfinance.app.sweepingaccount.SweepingLimits
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.mockk
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class IniciadorSweepingAdapterTest {
    private val httpClient = RxHttpClient.create(URL("https://consumer.dev.inic.dev"))
    private val configuration = IniciadorSweepingConfiguration(
        accessTokenPath = "/v1/sweeping/token",
        clientId = "4c19e330-e5f9-4981-99eb-9d3a4a8cf429",
        clientSecret = "uwtG12!EAQQYA%99KgV*WVQ%qFU%4Cd9vWYsyv\$g",
    )
    val authenticationManager = IniciadorSweepingAuthenticationManager(
        httpClient = httpClient,
        configuration = configuration,
    )
    val sweepingLimitsConfiguration = ConsentConfigurationTO(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimitsTO(
            day = DayPeriodicLimitTO(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = WeekPeriodicLimitTO(
                quantityLimit = 50,
                transactionLimit = 5_000_00,
            ),
            month = MonthPeriodicLimitTO(
                quantityLimit = 100,
                transactionLimit = 10_000_00,
            ),
            year = YearPeriodicLimitTO(
                quantityLimit = 300,
                transactionLimit = 300_000_00,
            ),
        ),
    )

    val sweepingParticipantAdapter = mockk<IniciadorSweepingParticipantAdapter>()

    val iniciadorSweepingAccountAdapter = IniciadorSweepingAccountAdapter(
        authenticationManager = authenticationManager,
        httpClient = httpClient,
        iniciadorSweepingParticipantAdapter = sweepingParticipantAdapter,
    ).apply {
        this.triggerId = ""
        this.sweepingAdditionalInfo = ""
    }

    @Test
    fun `get creditors`() {
        val creditors = iniciadorSweepingAccountAdapter.getCreditors()
        println(creditors)
    }

    @Test
    fun `get triggers`() {
        val triggers = iniciadorSweepingAccountAdapter.getRecurringTriggers()
        println(getObjectMapper().writeValueAsString(triggers))
    }

    @Test
    fun `create creditor`() {
        val creditor = SweepingCreditor(
            type = CreditorType.BANK_ACCOUNT,
            userTaxId = Document("***********"),
            creditorTaxId = Document("**************"),
            name = "Rafael",
            ispb = "********",
            issuer = "4321",
            number = "1234",
            accountType = CreditorAccountType.CACC,
            userAccountId = UserAccountId("teste"),
        )

        val createdCreditor = iniciadorSweepingAccountAdapter.createCreditor(creditor.toCreateCreditorRequest())

        println(createdCreditor)
    }

    @Test
    fun `get single creditor`() {
        val creditor = iniciadorSweepingAccountAdapter.getCreditor("af3f6538-9f3f-406e-a11f-d042fea07e5e")
        println(creditor)
    }

    @Test
    fun `create consent`() {
        val sweepingLimits = SweepingLimits(
            totalAllowedAmount = 1000_00,
            transactionLimit = 1000_00,
            periodicLimits = PeriodicLimits(
                day = PeriodicLimit(
                    quantityLimit = 10,
                    transactionLimit = 1_000_00,
                ),
                week = PeriodicLimit(
                    quantityLimit = 50,
                    transactionLimit = 5_567_00,
                ),
                month = PeriodicLimit(
                    quantityLimit = 100,
                    transactionLimit = 10_350_00,
                ),
                year = PeriodicLimit(
                    quantityLimit = 300,
                    transactionLimit = 725_000_00,
                ),
            ),
        )

        val createdConsent = iniciadorSweepingAccountAdapter.requestSweepingConsent(
            CreateConsentRequest(
                user = UserConsent(name = "Rafael Haertel", taxId = Document("***********")),
                participantId = ParticipantId("0b919e9b-bee0-4549-baa3-bb6d003575ce"),
                creditors = listOf(CreditorId("d0cbff6b-ea5a-491d-86b2-5b6cf1025689")),
                sweepingLimits = sweepingLimits,
            ),
        )
        println(createdConsent)
    }

    @Test
    fun `get payments`() {
        val authId = "urn:Iniciador:********-e9c2-4317-b708-757926853eaf"
        val payments = iniciadorSweepingAccountAdapter.getPayments(authId)
        println(payments)
    }
}