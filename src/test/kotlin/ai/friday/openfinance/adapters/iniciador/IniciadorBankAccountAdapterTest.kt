package ai.friday.openfinance.adapters.iniciador

import ai.friday.openfinance.app.AccountSubType
import ai.friday.openfinance.app.AccountType
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.DataConsentId
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class IniciadorBankAccountAdapterTest {

    private val mockClient: RxHttpClient = mockk()

    private val configuration = IniciadorConfiguration(
        accessTokenPath = "/v1/data/auth",
        clientId = "6ab959d1-24ee-4282-86b2-b22ceb5cca18",
        clientSecret = "KaBE3H/XZPT869h4bZv0PmvuJr0alZNt0G+j04ssFfSvKPDtHKlI8pPQX3t+RmCw",
    )

    val authenticationManager = IniciadorAuthenticationManager(
        httpClient = mockClient,
        configuration = configuration,
    )
    val iniciadorBankAccountAdapter = IniciadorBankAccountAdapter(
        authenticationManager,
        httpClient = mockClient,
        kmsService = mockk(relaxed = true),
    )

    val dataConsentId = DataConsentId("CONSENT-123")
    val bankAccountId = BankAccountId("a803d5bc-29f4-3f14-8c91-fe8a1e79e3a0")

    private fun setupIniciadorTokenResponse() {
        every {
            mockClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    expiresIn = 365,
                ),
            )
        }
    }

    private fun setupIniciadorSuccessResponse(data: String) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.of(IniciadorResponseTO::class.java),
            )
        } answers {
            Flowable.just(
                IniciadorResponseTO(
                    data = listOf(
                        jacksonObjectMapper().readerFor(Map::class.java)
                            .readValue(data),
                    ),
                    links = LinksTO(
                        self = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                        first = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                    ),
                    meta = MetaTO(
                        totalPages = 2,
                        requestDateTime = "2024-06-20T19:45:24.888Z",
                    ),
                ),
            )
        }
    }

    private fun setupIniciadorSuccessResponseList(data: String, nextPage: String? = null) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.of(IniciadorResponseTO::class.java),
            )
        } answers {
            Flowable.just(
                IniciadorResponseTO(
                    data = jacksonObjectMapper().readerFor(List::class.java)
                        .readValue(data),
                    links = LinksTO(
                        self = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                        first = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                        next = nextPage,
                    ),
                    meta = MetaTO(
                        requestDateTime = "2024-06-20T19:45:24.888Z",
                    ),
                ),
            )
        } andThenAnswer {
            Flowable.just(
                IniciadorResponseTO(
                    data = jacksonObjectMapper().readerFor(List::class.java)
                        .readValue(data),
                    links = LinksTO(
                        self = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                        first = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=1&page-size=25",
                    ),
                    meta = MetaTO(
                        requestDateTime = "2024-06-20T19:45:24.888Z",
                    ),
                ),
            )
        }
    }

    @Test
    fun `deveria retornar sucesso quando buscar todas as contas`() {
        setupIniciadorTokenResponse()
        setupIniciadorSuccessResponseList(
            data = "[\n" +
                "        {\n" +
                "            \"brandName\": \"Organização A\",\n" +
                "            \"companyCnpj\": \"**************\",\n" +
                "            \"type\": \"CONTA_DEPOSITO_A_VISTA\",\n" +
                "            \"compeCode\": \"999\",\n" +
                "            \"branchCode\": \"9759\",\n" +
                "            \"number\": \"********\",\n" +
                "            \"checkDigit\": \"4\",\n" +
                "            \"accountId\": \"ec7907f1-1ec0-4dac-8a39-1d876ca96bd6\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"brandName\": \"Organização A\",\n" +
                "            \"companyCnpj\": \"**************\",\n" +
                "            \"type\": \"CONTA_POUPANCA\",\n" +
                "            \"compeCode\": \"999\",\n" +
                "            \"branchCode\": \"5271\",\n" +
                "            \"number\": \"********\",\n" +
                "            \"checkDigit\": \"7\",\n" +
                "            \"accountId\": \"2c357469-017f-4180-911b-0150d366dc27\"\n" +
                "        }\n" +
                "    ]",
        )

        val response = iniciadorBankAccountAdapter.getBankAccounts(dataConsentId = dataConsentId)

        response.isRight() shouldBe true
        response.map {
            it.size shouldBe 2
            it[0].brandName shouldBe "Organização A"
            it[0].companyCnpj shouldBe "**************"
        }
    }

    @Test
    fun `deveria retornar sucesso quando buscar todas as contas com paginação`() {
        setupIniciadorTokenResponse()
        setupIniciadorSuccessResponseList(
            data = "[\n" +
                "        {\n" +
                "            \"brandName\": \"Organização A\",\n" +
                "            \"companyCnpj\": \"**************\",\n" +
                "            \"type\": \"CONTA_DEPOSITO_A_VISTA\",\n" +
                "            \"compeCode\": \"999\",\n" +
                "            \"branchCode\": \"9759\",\n" +
                "            \"number\": \"********\",\n" +
                "            \"checkDigit\": \"4\",\n" +
                "            \"accountId\": \"ec7907f1-1ec0-4dac-8a39-1d876ca96bd6\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"brandName\": \"Organização A\",\n" +
                "            \"companyCnpj\": \"**************\",\n" +
                "            \"type\": \"CONTA_POUPANCA\",\n" +
                "            \"compeCode\": \"999\",\n" +
                "            \"branchCode\": \"5271\",\n" +
                "            \"number\": \"********\",\n" +
                "            \"checkDigit\": \"7\",\n" +
                "            \"accountId\": \"2c357469-017f-4180-911b-0150d366dc27\"\n" +
                "        }\n" +
                "    ]",
            nextPage = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=2&page-size=25",
        )

        val response = iniciadorBankAccountAdapter.getBankAccounts(dataConsentId = dataConsentId)

        response.isRight() shouldBe true
        response.map {
            it.size shouldBe 4
            it[0].brandName shouldBe "Organização A"
            it[0].companyCnpj shouldBe "**************"
        }
    }

    @Test
    fun `deveria retornar sucesso quando buscar uma conta`() {
        setupIniciadorTokenResponse()
        setupIniciadorSuccessResponse(
            data = "{\n" +
                "    \"compeCode\": \"001\",\n" +
                "    \"branchCode\": \"6272\",\n" +
                "    \"number\": \"********\",\n" +
                "    \"checkDigit\": \"4\",\n" +
                "    \"type\": \"CONTA_DEPOSITO_A_VISTA\",\n" +
                "    \"subtype\": \"INDIVIDUAL\",\n" +
                "    \"currency\": \"BRL\"\n" +
                "  }",
        )

        val response = iniciadorBankAccountAdapter.getBankAccountData(dataConsentId = dataConsentId, bankAccountId = bankAccountId)

        response.isRight() shouldBe true
        response.map {
            it.compeCode shouldBe "001"
            it.number shouldBe "********"
            it.subtype shouldBe AccountSubType.INDIVIDUAL
            it.type shouldBe AccountType.CONTA_DEPOSITO_A_VISTA
        }
    }

    @Test
    fun `deveria retornar sucesso quando buscar as transações`() {
        setupIniciadorTokenResponse()
        setupIniciadorSuccessResponseList(
            data = "[\n" +
                "    {\n" +
                "      \"transactionId\": \"123\",\n" +
                "      \"completedAuthorisedPaymentType\": \"TRANSACAO_EFETIVADA\",\n" +
                "      \"creditDebitType\": \"DEBITO\",\n" +
                "      \"transactionName\": \"TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8\",\n" +
                "      \"type\": \"PIX\",\n" +
                "      \"transactionAmount\": {\n" +
                "        \"amount\": \"1000.0400\",\n" +
                "        \"currency\": \"BRL\"\n" +
                "      },\n" +
                "      \"transactionDateTime\": \"2016-01-29T12:29:03.374Z\",\n" +
                "      \"partieCnpjCpf\": \"***********\",\n" +
                "      \"partiePersonType\": \"PESSOA_NATURAL\",\n" +
                "      \"partieCompeCode\": \"001\",\n" +
                "      \"partieBranchCode\": \"6272\",\n" +
                "      \"partieNumber\": \"***********\",\n" +
                "      \"partieCheckDigit\": \"4\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"transactionId\": \"456\",\n" +
                "      \"completedAuthorisedPaymentType\": \"TRANSACAO_EFETIVADA\",\n" +
                "      \"creditDebitType\": \"DEBITO\",\n" +
                "      \"transactionName\": \"TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8\",\n" +
                "      \"type\": \"PIX\",\n" +
                "      \"transactionAmount\": {\n" +
                "        \"amount\": \"1000.0400\",\n" +
                "        \"currency\": \"BRL\"\n" +
                "      },\n" +
                "      \"transactionDateTime\": \"2016-01-29T12:29:03.374Z\",\n" +
                "      \"partieCnpjCpf\": \"***********\",\n" +
                "      \"partiePersonType\": \"PESSOA_NATURAL\",\n" +
                "      \"partieCompeCode\": \"001\",\n" +
                "      \"partieBranchCode\": \"6272\",\n" +
                "      \"partieNumber\": \"***********\",\n" +
                "      \"partieCheckDigit\": \"4\"\n" +
                "    }\n" +
                "  ]",
        )

        val response = iniciadorBankAccountAdapter.getBankAccountTransactions(
            dataConsentId = dataConsentId,
            recentTransactions = false,
            bankAccountId = bankAccountId,
        )

        response.isRight() shouldBe true
        response.map {
            it.size shouldBe 2
            it[0].transactionId shouldBe "123"
            it[0].transactionName shouldBe "TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8"
        }
    }

    @Test
    fun `deveria retornar sucesso quando buscar as transações com paginação`() {
        setupIniciadorTokenResponse()
        setupIniciadorSuccessResponseList(
            data = "[\n" +
                "    {\n" +
                "      \"transactionId\": \"123\",\n" +
                "      \"completedAuthorisedPaymentType\": \"TRANSACAO_EFETIVADA\",\n" +
                "      \"creditDebitType\": \"DEBITO\",\n" +
                "      \"transactionName\": \"TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8\",\n" +
                "      \"type\": \"PIX\",\n" +
                "      \"transactionAmount\": {\n" +
                "        \"amount\": \"1000.0400\",\n" +
                "        \"currency\": \"BRL\"\n" +
                "      },\n" +
                "      \"transactionDateTime\": \"2016-01-29T12:29:03.374Z\",\n" +
                "      \"partieCnpjCpf\": \"***********\",\n" +
                "      \"partiePersonType\": \"PESSOA_NATURAL\",\n" +
                "      \"partieCompeCode\": \"001\",\n" +
                "      \"partieBranchCode\": \"6272\",\n" +
                "      \"partieNumber\": \"***********\",\n" +
                "      \"partieCheckDigit\": \"4\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"transactionId\": \"456\",\n" +
                "      \"completedAuthorisedPaymentType\": \"TRANSACAO_EFETIVADA\",\n" +
                "      \"creditDebitType\": \"DEBITO\",\n" +
                "      \"transactionName\": \"TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8\",\n" +
                "      \"type\": \"PIX\",\n" +
                "      \"transactionAmount\": {\n" +
                "        \"amount\": \"1000.0400\",\n" +
                "        \"currency\": \"BRL\"\n" +
                "      },\n" +
                "      \"transactionDateTime\": \"2016-01-29T12:29:03.374Z\",\n" +
                "      \"partieCnpjCpf\": \"***********\",\n" +
                "      \"partiePersonType\": \"PESSOA_NATURAL\",\n" +
                "      \"partieCompeCode\": \"001\",\n" +
                "      \"partieBranchCode\": \"6272\",\n" +
                "      \"partieNumber\": \"***********\",\n" +
                "      \"partieCheckDigit\": \"4\"\n" +
                "    }\n" +
                "  ]",
            nextPage = "https://data.sandbox.iniciador.com.br/v1/data/links/6e0b54b8-ab7b-457d-bf6e-1848660adbc5/data/accounts?page=2&page-size=25",
        )

        val response = iniciadorBankAccountAdapter.getBankAccountTransactions(
            dataConsentId = dataConsentId,
            recentTransactions = false,
            bankAccountId = bankAccountId,
        )

        response.isRight() shouldBe true
        response.map {
            it.size shouldBe 4
            it[0].transactionId shouldBe "123"
            it[0].transactionName shouldBe "TRANSFCWAR5TXHCX5I9IDBHML8082N8NEO30M6LNNG7ANAYIJYRM00ZBZPU8"
        }
    }

    @Disabled
    @Nested
    inner class CallRealApi {

        private val httpClient = RxHttpClient.create(URL("https://data.sandbox.iniciador.com.br"))

        private val dataConsentId = DataConsentId("a1829854-f355-49fb-a361-5e37e2377719")
        private val bankAccountId = BankAccountId("a803d5bc-29f4-3f14-8c91-fe8a1e79e3a0")

        private val authenticationManager = IniciadorAuthenticationManager(
            httpClient = httpClient,
            configuration = configuration,
        )

        private val iniciadorLinkAdapter = IniciadorDataConsentAdapter(
            authenticationManager,
            httpClient = httpClient,
        )

        private val iniciadorBankAccountAdapter = IniciadorBankAccountAdapter(
            authenticationManager,
            httpClient = httpClient,
            kmsService = mockk(relaxed = true),
        )

        @Test
        fun `get link`() {
            val link = iniciadorLinkAdapter.getLink(dataConsentId)
            println(link)
        }

        @Test
        fun `get resources`() {
            val resources = iniciadorLinkAdapter.getResources(dataConsentId)
            println(resources)
        }

        @Test
        fun getBankAccounts() {
            val bankAccounts = iniciadorBankAccountAdapter.getBankAccounts(dataConsentId)
            println(bankAccounts)
        }

        @Test
        fun getBankAccount() {
            val bankAccount = iniciadorBankAccountAdapter.getBankAccountData(dataConsentId = dataConsentId, bankAccountId = bankAccountId)
            println(bankAccount)
        }

        @Test
        fun getBankAccountBalance() {
            val bankAccountBalance = iniciadorBankAccountAdapter.getBankAccountBalance(dataConsentId = dataConsentId, bankAccountId = bankAccountId)
            println(bankAccountBalance)
        }

        @Test
        fun getBankAccountLimits() {
            val bankAccountLimits = iniciadorBankAccountAdapter.getBankAccountLimits(dataConsentId = dataConsentId, bankAccountId = bankAccountId)
            println(bankAccountLimits)
        }

        @Test
        fun getBankAccountTransactions() {
            val bankAccountTransactions = iniciadorBankAccountAdapter.getBankAccountTransactions(
                dataConsentId = dataConsentId,
                bankAccountId = bankAccountId,
                recentTransactions = true,
            )
            println(bankAccountTransactions)
        }
    }
}