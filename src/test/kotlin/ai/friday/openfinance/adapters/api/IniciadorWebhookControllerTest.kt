package ai.friday.openfinance.adapters.api

import ai.friday.openfinance.app.integrations.MessagePublisher
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

class IniciadorWebhookControllerTest {

    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    private val controller = IniciadorWebhookController(
        messagePublisher = messagePublisher,
        availableConnectionQueueName = "available-connection-queue-name",
        sweepingWebhookQueueName = "sweeping-webhook-queue-name",
    )

    @Test
    fun `deve publicar a mensagem recebida na fila`() {
        val request = SweepingWebhookTO(
            authorizationId = null,
            id = null,
            date = null,
            description = null,
            createdAt = null,
            consentId = null,
            paymentId = null,
            updatedAt = "2025-01-06T17:46:19.635Z",
            endToEndId = null,
            status = "status",
            amount = null,
            externalId = "PAYMENT_EXTERNAL_ID",
            error = null,
            errors = listOf(),
        )

        val result = controller.sweepingWebhook(request)

        result.status shouldBe HttpStatus.OK

        val slot = slot<SweepingWebhookTO>()
        verify {
            messagePublisher.sendMessage(
                queueName = "sweeping-webhook-queue-name",
                body = capture(slot),
            )
        }
        slot.captured shouldBe request
    }
}