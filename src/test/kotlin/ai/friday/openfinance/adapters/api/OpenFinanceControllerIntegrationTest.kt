package ai.friday.openfinance.adapters.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.adapters.dynamodb.DataConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.DataConsentDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingCreditorDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingCreditorDynamoDAO
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingPaymentDynamoDAO
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorSweepingAccountAdapter
import ai.friday.openfinance.adapters.sqs.SQSMessagePublisher
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentLink
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.DataConsentAdapter
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentExternalId
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.sweepingaccount.AutomaticSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.CreateConsentResponse
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorRequest
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorResponse
import ai.friday.openfinance.app.sweepingaccount.CreditorAccountType
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.CreditorType
import ai.friday.openfinance.app.sweepingaccount.ParticipantCreditor
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingCreditor
import ai.friday.openfinance.app.sweepingaccount.SweepingParticipant
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentStatusResponse
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.equality.shouldBeEqualUsingFields
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

@MicronautTest
class OpenFinanceControllerIntegrationTest(private val embeddedServer: EmbeddedServer) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val dynamoDB = setupDynamoDB()

    private val dataConsentAdapter: DataConsentAdapter = mockk {
        every { requestDataConsent(any(), Document("***********"), any(), any()) } returns Exception().left()
        every { requestDataConsent(any(), Document("***********"), any(), any()) } returns DataConsentLink(DataConsentId("test-consent-id"), "http://localhost").right()
    }

    private val sweepingCreditor = SweepingCreditor(
        userAccountId = UserAccountId("e6247171-650c-4218-b723-30c9eed6b044"),
        id = CreditorId("test-creditor-id"),
        type = CreditorType.BANK_ACCOUNT,
        userTaxId = Document(value = "***********"),
        creditorTaxId = Document(value = "***********"),
        name = "Itau",
        ispb = "********",
        issuer = "19",
        number = "**********",
        accountType = CreditorAccountType.CACC,
        participant = ParticipantCreditor("Nome do Participant"),
        status = "ACTIVE",
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
    )

    private val sweepingLimitsConfiguration = SweepingLimitsTO(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimitsTO(
            day = PeriodicLimitTO(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = PeriodicLimitTO(
                quantityLimit = 50,
                transactionLimit = 5_000_00,
            ),
            month = PeriodicLimitTO(
                quantityLimit = 100,
                transactionLimit = 10_000_00,
            ),
            year = PeriodicLimitTO(
                quantityLimit = 300,
                transactionLimit = 300_000_00,
            ),
        ),
    )

    private val sweepingConsent = SweepingConsent(
        userAccountId = UserAccountId("e6247171-650c-4218-b723-30c9eed6b044"), consentId = SweepingConsentId(value = "test-consent-id"),
        participant = SweepingParticipant(
            id = "***************",
            name = "participant",
            avatar = null,
        ),
        status = ConsentStatus.AWAITING_AUTHORISATION,
        authUrl = "http://localhost",
        user = UserConsent(
            taxId = Document(value = "***********"),
            name = "Morgan Hanson",
        ),
        businessEntity = UserConsent(
            taxId = Document(
                value = "**************",
            ),
            name = "Business Entity",
        ),
        creditors = listOf(CreditorId("test-creditor-id")),
        startDateTime = getZonedDateTime(),
        expirationDateTime = getZonedDateTime().plusYears(2),
        additionalInformation = "",
        statusUpdateDateTime = getZonedDateTime(),
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        sweepingLimits = sweepingLimitsConfiguration.toSweepingLimits(),
        debtor = null,
    )

    private val sweepingPayment = SweepingPayment(
        externalId = SweepingPaymentExternalId("test-payment-id"),
        consentId = SweepingConsentId("test-consent-id"),
        creditorId = CreditorId("test-creditor-id"),
        amount = 1000,
        description = "Test payment",
        status = SweepingPaymentStatus.CREATED,
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        userAccountId = UserAccountId("e6247171-650c-4218-b723-30c9eed6b044"),
        date = getLocalDate(),
        endToEndId = "test",
        fee = 0,
        method = "PIX_MANU",
        riskSignals = AutomaticSweepingRiskSignals(lastLoginDateTime = getZonedDateTime()),
        participantId = "***************",
        businessEntityTaxId = "**************",
        userTaxId = "***********",
        requestId = SweepingPaymentRequestId(),
    )

    private val sweepingStatusResponse = SweepingPaymentStatusResponse(
        requestId = sweepingPayment.requestId,
        externalId = sweepingPayment.externalId,
        status = sweepingPayment.status,
        error = null,
        description = "pulvinar",
        date = null,
        createdAt = getZonedDateTime(),
        updatedAt = null,
        consentId = SweepingConsentId(value = "rhoncus"),
        endToEndId = null,
        amount = 5842,
        fee = null,
        method = null,
        participantId = "est",
        businessEntityTaxId = null,
        userTaxId = null,
    )

    private val dataConsentDynamoDAO = DataConsentDynamoDAO(dynamoDB)
    private val openFinanceConnectionRepository = DataConsentDbRepository(dataConsentDynamoDAO)
    private val sweepingCreditorDbRepository = SweepingCreditorDbRepository(SweepingCreditorDynamoDAO(dynamoDB))
    private val sweepingPaymentDbRepository = SweepingPaymentDbRepository(SweepingPaymentDynamoDAO(dynamoDB))
    private val sweepingConsentDbRepository = SweepingConsentDbRepository(SweepingConsentDynamoDAO(dynamoDB))
    private val sweepingAccountAdapter: IniciadorSweepingAccountAdapter = mockk {
        every { createCreditor(any()) } returns sweepingCreditor.toCreateCreditorResponse()
        every { requestSweepingConsent(any()) } returns sweepingConsent.toCreateConsentResponse()
        every { createPayment(any()) } returns sweepingPayment.toCreateTransactionResponse().right()
        every { getPaymentStatus(any(), any()) } returns sweepingStatusResponse.right()
    }

    private val createPaymentRequestTO = CreatePaymentRequestTO(
        requestId = "request-id",
        consentId = "test-consent-id",
        creditorId = "test-creditor-id",
        amount = 1000,
        description = "Test payment",
    )

    private val createCreditorConsent = CreateCreditorConsentTO(
        "e6247171-650c-4218-b723-30c9eed6b044",
        user = UserConsentTO(
            taxId = "***********",
            name = "Morgan Hanson",
        ),
        businessEntity = UserConsentTO(
            taxId = "**************",
            name = "Business Entity",
        ),
        participantId = "***************",
        creditorConsentTO = SweepingCreditorRequestTO(
            type = CreditorType.BANK_ACCOUNT,
            name = "Itau",
            ispb = "********",
            issuer = "19",
            number = "**********",
            accountType = CreditorAccountType.CACC,
        ),
        sweepingLimits = sweepingLimitsConfiguration,
    )

    private val messagePublisher = mockk<SQSMessagePublisher>(relaxed = true)

    @BeforeEach
    fun setup() {
        embeddedServer.applicationContext.registerSingleton(DataConsentAdapter::class.java, dataConsentAdapter)
        embeddedServer.applicationContext.registerSingleton(DynamoDbEnhancedClient::class.java, dynamoDB)
        embeddedServer.applicationContext.registerSingleton(IniciadorSweepingAccountAdapter::class.java, sweepingAccountAdapter)
        embeddedServer.applicationContext.registerSingleton(SQSMessagePublisher::class.java, messagePublisher)
    }

    @Test
    fun `deve retornar erro de cpf invalido`() {
        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().retrieve(
                HttpRequest.POST("/openFinance/data/consent", CreateDataConsentRequestTO("e6247171-650c-4218-b723-30c9eed6b044", "*********", "participantId"))
                    .basicAuth("clientid", "clientsecret"),
                CreateDataConsentResponseTO::class.java,
            )
        }
        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar erro de gerar link`() {
        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().retrieve(
                HttpRequest.POST("/openFinance/data/consent", CreateDataConsentRequestTO("e6247171-650c-4218-b723-30c9eed6b044", "***********", "participantId"))
                    .basicAuth("clientid", "clientsecret"),
                CreateDataConsentResponseTO::class.java,
            )
        }
        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `deve retornar o link gerado e salvar o externalId`() {
        val userAccountId = "e6247171-650c-4218-b723-30c9eed6b044"
        val response = client.toBlocking().retrieve(
            HttpRequest.POST("/openFinance/data/consent", CreateDataConsentRequestTO(userAccountId, "***********", "participantId"))
                .basicAuth("clientid", "clientsecret"),
            CreateDataConsentResponseTO::class.java,
        )

        response.link shouldBe "http://localhost"
        val connection = openFinanceConnectionRepository.find(DataConsentId("test-consent-id"))
        connection.userAccountId.value shouldBe userAccountId
        connection.id shouldBe DataConsentId("test-consent-id")
        connection.participantId.value shouldBe "participantId"
    }

    @Test
    fun `deve criar um consentimento de crédito`() {
        val createCreditorConsentTO = createCreditorConsent
        val response = client.toBlocking().retrieve(
            HttpRequest.POST("/openFinance/sweeping/consent", createCreditorConsentTO)
                .basicAuth("clientid", "clientsecret"),
            SweepingAccountConsentResponseTO::class.java,
        )

        val creditor = sweepingCreditorDbRepository.find(CreditorId("test-creditor-id"))
        creditor.isRight() shouldBe true
        creditor.getOrNull()!!.status shouldBe "ACTIVE"
        val result = sweepingConsentDbRepository.find(SweepingConsentId("test-consent-id"))
        result.isRight() shouldBe true
        result.getOrNull()!!.status shouldBe ConsentStatus.AWAITING_AUTHORISATION
        result.getOrNull()!!.creditors shouldBe listOf(CreditorId("test-creditor-id"))
        result.getOrNull()!!.sweepingLimits!! shouldBeEqualUsingFields sweepingLimitsConfiguration.toSweepingLimits()!!
        response.consentUrl shouldBe "http://localhost"
    }

    @Test
    fun `deve criar um pagamento`() {
        val createCreditorConsentTO = createCreditorConsent
        val creditor = client.toBlocking().retrieve(
            HttpRequest.POST("/openFinance/sweeping/consent", createCreditorConsentTO)
                .basicAuth("clientid", "clientsecret"),
            SweepingAccountConsentResponseTO::class.java,
        )

        val response = client.toBlocking().retrieve(
            HttpRequest.POST("/openFinance/sweeping/payment", createPaymentRequestTO)
                .basicAuth("clientid", "clientsecret"),
            CreatePaymentResponseTO::class.java,
        )

        val transactionEither = sweepingPaymentDbRepository.find(SweepingPaymentRequestId(response.requestId))
        transactionEither.isRight() shouldBe true
        transactionEither.map {
            it.status shouldBe SweepingPaymentStatus.CREATED
            it.amount shouldBe 1000
            it.description shouldBe "Test payment"
            it.userAccountId shouldBe UserAccountId(createCreditorConsent.userAccountId)
            it.requestId shouldBe SweepingPaymentRequestId(response.requestId)

            val uri = "/openFinance/sweeping/payment/${it.requestId.value}"

            val httpRequest = HttpRequest.GET<String>(uri)
                .basicAuth("clientid", "clientsecret")
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val getPaymentResponse = client.toBlocking().retrieve(httpRequest, Argument.of(SweepingPaymentStatusResponseTO::class.java))
            getPaymentResponse.status shouldBe sweepingPayment.status
            getPaymentResponse.error shouldBe null
            getPaymentResponse.errorDescription shouldBe null
        }
    }
}

fun SweepingCreditor.toCreateCreditorResponse() = CreateCreditorResponse(
    id = id,
    type = type,
    userTaxId = userTaxId,
    creditorTaxId = creditorTaxId,
    name = name,
    ispb = ispb,
    issuer = issuer,
    number = number,
    accountType = accountType,
    participant = participant,
    status = status!!,
    createdAt = createdAt!!,
    updatedAt = updatedAt!!,
)

fun SweepingConsent.toCreateConsentResponse() = CreateConsentResponse(
    consentId = consentId,
    participant = participant,
    status = status,
    authUrl = authUrl,
    user = user,
    businessEntity = businessEntity,
    startDateTime = startDateTime,
    expirationDateTime = expirationDateTime,
    additionalInformation = additionalInformation,
    statusUpdateDateTime = statusUpdateDateTime,
    createdAt = createdAt,
    updatedAt = updatedAt,
    sweepingLimits = sweepingLimits,
    debtor = debtor,
)

fun SweepingCreditor.toCreateCreditorRequest() = CreateCreditorRequest(
    type = type,
    userTaxId = userTaxId,
    creditorTaxId = creditorTaxId,
    name = name,
    ispb = ispb,
    issuer = issuer,
    number = number,
    accountType = accountType,
)

fun SweepingPayment.toCreateTransactionResponse() = SweepingPaymentResponse(
    externalId = externalId!!,
    date = date!!,
    description = description,
    createdAt = createdAt,
    consentId = consentId,
    updatedAt = updatedAt,
    endToEndId = endToEndId,
    amount = amount,
    fee = fee,
    method = method,
    riskSignals = riskSignals,
    participantId = participantId,
    businessEntityTaxId = businessEntityTaxId,
    userTaxId = userTaxId,
    requestId = requestId,
    status = status,
)