package ai.friday.openfinance.adapters.api

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.sweepingaccount.CreateSweepingPaymentRequest
import ai.friday.openfinance.app.sweepingaccount.CreateSweepingPaymentServiceError
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.ManualSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class OpenFinanceControllerTest {

    private val sweepingAccountService: SweepingAccountService = mockk()

    private val controller = OpenFinanceController(
        dataConsentService = mockk(),
        sweepingAccountService = sweepingAccountService,
        bankAccountService = mockk(),
    )

    @DisplayName("ao criar um pagamento")
    @Nested
    inner class CreatePaymentTest {
        val endToEnd = UUID.randomUUID().toString()

        val request = CreatePaymentRequestTO(
            requestId = "requestId",
            consentId = "consentId",
            creditorId = "creditorId",
            amount = 1,
            description = "description",
            riskSignals = null,
        )

        @Test
        fun `deve retornar OK em caso de sucesso`() {
            every {
                sweepingAccountService.createPayment(any())
            } answers {
                val request: CreateSweepingPaymentRequest = firstArg()

                request.toSweepingPayment().right()
            }

            val response = controller.createSweepingPayment(request)

            response.status shouldBe HttpStatus.OK
            with(response.getBody(CreatePaymentResponseTO::class.java).get()) {
                this.requestId shouldBe request.requestId
                this.endToEnd shouldBe endToEnd
            }
        }

        @Test
        fun `deve retornar 4001 quando não sabe o estado do pagamento`() {
            every {
                sweepingAccountService.createPayment(any())
            } returns CreateSweepingPaymentServiceError.GenericError("message").left()

            val response = controller.createSweepingPayment(request)

            response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
            with(response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4001"
            }
        }

        @Test
        fun `deve retornar 4001 quando não encontrou o consentimento`() {
            every {
                sweepingAccountService.createPayment(any())
            } returns CreateSweepingPaymentServiceError.ConsentNotFound(SweepingConsentId()).left()

            val response = controller.createSweepingPayment(request)

            response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
            with(response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4001"
            }
        }

        @ParameterizedTest
        @CsvSource(
            "DAILY,      4002",
            "WEEKLY,     4003",
            "MONTHLY,    4004",
            "YEARLY,     4005",
            "GLOBAL,     4006",
            "TRANSACTION,4007",
            "UNKNOWN,    4008",
        )
        fun `deve retornar o codigo correto quando excedeu o limite`(limitType: LimitType, expectedErrorCode: String) {
            every {
                sweepingAccountService.createPayment(any())
            } returns CreateSweepingPaymentServiceError.LimitExceededError(limitType).left()

            val response = controller.createSweepingPayment(request)

            response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
            with(response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe expectedErrorCode
            }
        }

        @Test
        fun `deve retornar 4100 quando não conseguiu criar o pagamento`() {
            every {
                sweepingAccountService.createPayment(any())
            } returns CreateSweepingPaymentServiceError.PaymentNotCreated("message").left()

            val response = controller.createSweepingPayment(request)

            response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
            with(response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4100"
            }
        }

        private fun CreateSweepingPaymentRequest.toSweepingPayment() = SweepingPayment(
            requestId = id,
            externalId = null,
            userAccountId = UserAccountId(value = "viris"),
            consentId = SweepingConsentId(value = "fastidii"),
            date = null,
            description = "liber",
            createdAt = getZonedDateTime(),
            updatedAt = null,
            endToEndId = endToEnd,
            status = SweepingPaymentStatus.PROCESSING,
            amount = 8397,
            fee = null,
            method = null,
            riskSignals = ManualSweepingRiskSignals(deviceId = "deviceId"),
            participantId = "iaculis",
            businessEntityTaxId = null,
            userTaxId = null,
            creditorId = CreditorId(value = "veniam"),
            error = null,
            errorDescription = null,
        )
    }
}