package ai.friday.openfinance.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCardFinanceCharges
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoice
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceExternalId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardPayment
import ai.friday.openfinance.app.utils.RepositoryError
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import java.time.LocalDate
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CreditCardInvoiceDbRepositoryTest {
    private val dynamoDB = setupDynamoDB()
    private val creditCardInvoiceDynamoDAO = CreditCardInvoiceDynamoDAO(dynamoDB)
    private val creditCardInvoiceRepository = CreditCardInvoiceDbRepository(creditCardInvoiceDynamoDAO)

    private val userAccountId = UserAccountId("test-user-id")
    private val dataConsentId = DataConsentId("test-consent-id")
    private val creditCardId = CreditCardId("test-card-id")
    private val invoiceId = CreditCardInvoiceId("test-invoice-id")
    private val invoiceExternalId = CreditCardInvoiceExternalId("test-invoice-external-id")

    @DisplayName("ao salvar uma fatura")
    @Nested
    inner class SaveInvoice {
        @Test
        fun `deve salvar a fatura com sucesso`() {
            // given
            val invoice = CreditCardInvoice(
                invoiceId = invoiceId,
                invoiceExternalId = invoiceExternalId,
                dueDate = LocalDate.now(),
                billTotalAmount = 1000L,
                billMinimumAmount = 100L,
                billCurrency = "BRL",
                isInstalment = false,
                financeCharges = listOf(
                    CreditCardFinanceCharges(
                        type = "INTEREST",
                        additionalInfo = "Juros de atraso",
                        amount = 50L,
                        currency = "BRL",
                    ),
                ),
                payments = listOf(
                    CreditCardPayment(
                        valueType = "PAYMENT",
                        paymentDate = LocalDate.now(),
                        paymentMode = "ONLINE",
                        amount = 1000L,
                        currency = "BRL",
                    ),
                ),
                provider = OFProvider.INICIADOR,
            )

            // when
            val savedInvoice = creditCardInvoiceRepository.save(invoice, dataConsentId, userAccountId, creditCardId)

            // then
            savedInvoice shouldBe invoice
        }
    }

    @DisplayName("ao buscar uma fatura")
    @Nested
    inner class FindInvoice {
        @Test
        fun `deve retornar a fatura quando encontrada`() {
            // given
            val invoice = CreditCardInvoice(
                invoiceId = invoiceId,
                invoiceExternalId = invoiceExternalId,
                dueDate = LocalDate.now(),
                billTotalAmount = 1000L,
                billMinimumAmount = 100L,
                billCurrency = "BRL",
                isInstalment = false,
                financeCharges = listOf(
                    CreditCardFinanceCharges(
                        type = "INTEREST",
                        additionalInfo = "Juros de atraso",
                        amount = 50L,
                        currency = "BRL",
                    ),
                ),
                payments = listOf(
                    CreditCardPayment(
                        valueType = "PAYMENT",
                        paymentDate = LocalDate.now(),
                        paymentMode = "ONLINE",
                        amount = 1000L,
                        currency = "BRL",
                    ),
                ),
                provider = OFProvider.INICIADOR,
            )
            creditCardInvoiceRepository.save(invoice, dataConsentId, userAccountId, creditCardId)

            // when
            val result = creditCardInvoiceRepository.find(invoiceId)

            // then
            result.fold(
                ifLeft = { throw AssertionError("Não deveria retornar erro") },
                ifRight = { foundInvoice ->
                    foundInvoice shouldBe invoice
                },
            )
        }

        @Test
        fun `deve retornar erro quando fatura não encontrada`() {
            // when
            val result = creditCardInvoiceRepository.find(invoiceId)

            // then
            result.fold(
                ifLeft = { error ->
                    error.shouldBeInstanceOf<RepositoryError.ItemNotFound>()
                },
                ifRight = { throw AssertionError("Não deveria encontrar a fatura") },
            )
        }
    }

    @DisplayName("ao buscar faturas por cartão")
    @Nested
    inner class FindByCreditCardId {
        @Test
        fun `deve retornar lista de faturas quando encontradas`() {
            // given
            val invoice1 = CreditCardInvoice(
                invoiceId = invoiceId,
                invoiceExternalId = invoiceExternalId,
                dueDate = LocalDate.now(),
                billTotalAmount = 1000L,
                billMinimumAmount = 100L,
                billCurrency = "BRL",
                isInstalment = false,
                financeCharges = listOf(
                    CreditCardFinanceCharges(
                        type = "INTEREST",
                        additionalInfo = "Juros de atraso",
                        amount = 50L,
                        currency = "BRL",
                    ),
                ),
                payments = listOf(
                    CreditCardPayment(
                        valueType = "PAYMENT",
                        paymentDate = LocalDate.now(),
                        paymentMode = "ONLINE",
                        amount = 1000L,
                        currency = "BRL",
                    ),
                ),
                provider = OFProvider.INICIADOR,
            )
            val invoice2 = CreditCardInvoice(
                invoiceId = CreditCardInvoiceId("test-invoice-id-2"),
                invoiceExternalId = CreditCardInvoiceExternalId("test-invoice-external-id-2"),
                dueDate = LocalDate.now().plusMonths(1),
                billTotalAmount = 2000L,
                billMinimumAmount = 200L,
                billCurrency = "BRL",
                isInstalment = false,
                financeCharges = emptyList(),
                payments = emptyList(),
                provider = OFProvider.INICIADOR,
            )
            creditCardInvoiceRepository.save(invoice1, dataConsentId, userAccountId, creditCardId)
            creditCardInvoiceRepository.save(invoice2, dataConsentId, userAccountId, creditCardId)

            // when
            val result = creditCardInvoiceRepository.findByCreditCardId(creditCardId)

            // then
            result.size shouldBe 2
            result shouldBe listOf(invoice1, invoice2)
        }

        @Test
        fun `deve retornar lista vazia quando nenhuma fatura encontrada`() {
            // when
            val result = creditCardInvoiceRepository.findByCreditCardId(creditCardId)

            // then
            result shouldBe emptyList()
        }
    }
}