package ai.friday.openfinance.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCardBillAmount
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardTransaction
import ai.friday.openfinance.app.creditcard.CreditCardTransactionExternalId
import ai.friday.openfinance.app.utils.RepositoryError
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import java.time.YearMonth
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CreditCardTransactionDbRepositoryTest {
    private val dynamoDB = setupDynamoDB()
    private val creditCardTransactionDynamoDAO = CreditCardTransactionDynamoDAO(dynamoDB)
    private val creditCardTransactionRepository = CreditCardTransactionDbRepository(creditCardTransactionDynamoDAO)

    private val userAccountId = UserAccountId("test-user-id")
    private val dataConsentId = DataConsentId("test-consent-id")
    private val creditCardId = CreditCardId("test-card-id")
    private val transactionExternalId = CreditCardTransactionExternalId("test-transaction-external-id")
    private val invoiceId = CreditCardInvoiceId("test-invoice-id")

    @DisplayName("ao salvar uma transação")
    @Nested
    inner class SaveTransaction {
        @Test
        fun `deve salvar a transação com sucesso`() {
            // given
            val transaction = CreditCardTransaction(
                dataConsentId = dataConsentId,
                transactionExternalId = transactionExternalId,
                identificationNumber = "123456",
                transactionName = "Compra Teste",
                invoiceId = invoiceId,
                creditDebitType = "CREDIT",
                transactionType = "PURCHASE",
                transactionalAdditionalInfo = "Compra em loja",
                paymentType = "CREDIT",
                feeType = "NONE",
                feeTypeAdditionalInfo = "Sem taxa",
                otherCreditsType = "NONE",
                otherCreditsAdditionalInfo = "Sem outros créditos",
                chargeIdentificator = "charge-1",
                chargeNumber = "charge-1",
                originalAmount = CreditCardBillAmount(1000L, "BRL"),
                amount = 1000L,
                transactionDateTime = ZonedDateTime.parse("2024-03-20T11:00:00Z", DateTimeFormatter.ISO_DATE_TIME),
                billPostDate = "2024-03-20",
                payeeMCC = 5411,
                raw = "raw-data",
                provider = OFProvider.INICIADOR,
            )

            // when
            val savedTransaction = creditCardTransactionRepository.save(transaction, userAccountId, creditCardId)

            // then
            savedTransaction shouldBe transaction
        }
    }

    @DisplayName("ao buscar uma transação")
    @Nested
    inner class FindTransaction {
        @Test
        fun `deve retornar a transação quando encontrada`() {
            // given
            val transaction = CreditCardTransaction(
                dataConsentId = dataConsentId,
                transactionExternalId = transactionExternalId,
                identificationNumber = "123456",
                transactionName = "Compra Teste",
                invoiceId = invoiceId,
                creditDebitType = "CREDIT",
                transactionType = "PURCHASE",
                transactionalAdditionalInfo = "Compra em loja",
                paymentType = "CREDIT",
                feeType = "NONE",
                feeTypeAdditionalInfo = "Sem taxa",
                otherCreditsType = "NONE",
                otherCreditsAdditionalInfo = "Sem outros créditos",
                chargeIdentificator = "charge-1",
                chargeNumber = "charge-1",
                originalAmount = CreditCardBillAmount(1000L, "BRL"),
                amount = 1000L,
                transactionDateTime = ZonedDateTime.parse("2024-03-20T11:00:00Z", DateTimeFormatter.ISO_DATE_TIME),
                billPostDate = "2024-03-20",
                payeeMCC = 5411,
                raw = "raw-data",
                provider = OFProvider.INICIADOR,
            )
            creditCardTransactionRepository.save(transaction, userAccountId, creditCardId)

            // when
            val result = creditCardTransactionRepository.find(transactionExternalId, creditCardId)

            // then
            result.fold(
                ifLeft = { throw AssertionError("Não deveria retornar erro") },
                ifRight = { foundTransaction ->
                    foundTransaction shouldBe transaction
                },
            )
        }

        @Test
        fun `deve retornar erro quando transação não encontrada`() {
            // when
            val result = creditCardTransactionRepository.find(transactionExternalId, creditCardId)

            // then
            result.fold(
                ifLeft = { error ->
                    error.shouldBeInstanceOf<RepositoryError.ItemNotFound>()
                },
                ifRight = { throw AssertionError("Não deveria encontrar a transação") },
            )
        }
    }

    @DisplayName("ao buscar transações por cartão")
    @Nested
    inner class FindByCreditCardId {

        val transaction1 = CreditCardTransaction(
            dataConsentId = dataConsentId,
            transactionExternalId = transactionExternalId,
            identificationNumber = "123456",
            transactionName = "Compra Teste 1",
            invoiceId = invoiceId,
            creditDebitType = "CREDIT",
            transactionType = "PURCHASE",
            transactionalAdditionalInfo = "Compra em loja 1",
            paymentType = "CREDIT",
            feeType = "NONE",
            feeTypeAdditionalInfo = "Sem taxa",
            otherCreditsType = "NONE",
            otherCreditsAdditionalInfo = "Sem outros créditos",
            chargeIdentificator = "charge-1",
            chargeNumber = "charge-1",
            originalAmount = CreditCardBillAmount(1000L, "BRL"),
            amount = 1000L,
            transactionDateTime = ZonedDateTime.parse("2024-03-20T11:00:00Z", DateTimeFormatter.ISO_DATE_TIME),
            billPostDate = "2024-03-20",
            payeeMCC = 5411,
            raw = "raw-data-1",
            provider = OFProvider.INICIADOR,
        )
        val transaction2 = CreditCardTransaction(
            dataConsentId = dataConsentId,
            transactionExternalId = CreditCardTransactionExternalId("test-transaction-id-2"),
            identificationNumber = "789012",
            transactionName = "Compra Teste 2",
            invoiceId = invoiceId,
            creditDebitType = "CREDIT",
            transactionType = "PURCHASE",
            transactionalAdditionalInfo = "Compra em loja 2",
            paymentType = "CREDIT",
            feeType = "NONE",
            feeTypeAdditionalInfo = "Sem taxa",
            otherCreditsType = "NONE",
            otherCreditsAdditionalInfo = "Sem outros créditos",
            chargeIdentificator = "charge-2",
            chargeNumber = "charge-2",
            originalAmount = CreditCardBillAmount(2000L, "BRL"),
            amount = 2000L,
            transactionDateTime = ZonedDateTime.parse("2024-11-20T11:00:00Z", DateTimeFormatter.ISO_DATE_TIME),
            billPostDate = "2024-03-20",
            payeeMCC = 5411,
            raw = "raw-data-2",
            provider = OFProvider.INICIADOR,
        )

        @BeforeEach
        fun setup() {
            creditCardTransactionRepository.save(transaction1, userAccountId, creditCardId)
            creditCardTransactionRepository.save(transaction2, userAccountId, creditCardId)
        }

        @Test
        fun `deve retornar lista de transações quando encontradas`() {
            // when
            val result = creditCardTransactionRepository.findByCreditCardId(creditCardId, null)

            // then
            result.size shouldBe 2
            result shouldContainExactlyInAnyOrder listOf(transaction1, transaction2)
        }

        @Test
        fun `deve retornar lista de transações quando encontradas no mes 03`() {
            // when
            val result = creditCardTransactionRepository.findByCreditCardId(creditCardId, YearMonth.of(2024, 3))

            // then
            result.size shouldBe 1
            result shouldContainExactlyInAnyOrder listOf(transaction1)
        }

        @Test
        fun `deve retornar lista de transações quando encontradas no mes 11`() {
            // when
            val result = creditCardTransactionRepository.findByCreditCardId(creditCardId, YearMonth.of(2024, 11))

            // then
            result.size shouldBe 1
            result shouldContainExactlyInAnyOrder listOf(transaction2)
        }

        @Test
        fun `deve retornar lista vazia quando nenhuma transação encontrada no mes`() {
            // when
            val result = creditCardTransactionRepository.findByCreditCardId(CreditCardId("test-card-id2"), YearMonth.of(2024, 4))

            // then
            result shouldBe emptyList()
        }

        @Test
        fun `deve retornar lista vazia quando nenhuma transação encontrada`() {
            // when
            val result = creditCardTransactionRepository.findByCreditCardId(CreditCardId("test-card-id2"), null)

            // then
            result shouldBe emptyList()
        }
    }
}