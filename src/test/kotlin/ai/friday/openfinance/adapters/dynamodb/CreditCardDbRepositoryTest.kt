package ai.friday.openfinance.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCard
import ai.friday.openfinance.app.creditcard.CreditCardExternalId
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.utils.RepositoryError
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CreditCardDbRepositoryTest {
    private val dynamoDB = setupDynamoDB()
    private val creditCardDynamoDAO = CreditCardDynamoDAO(dynamoDB)
    private val creditCardRepository = CreditCardDbRepository(creditCardDynamoDAO)

    private val userAccountId = UserAccountId("test-user-id")
    private val dataConsentId = DataConsentId("test-consent-id")
    private val creditCardId = CreditCardId("test-card-id")
    private val creditCardExternalId = CreditCardExternalId("test-card-external-id")

    @DisplayName("ao salvar um cartão de crédito")
    @Nested
    inner class SaveCreditCard {
        @Test
        fun `deve salvar o cartão com sucesso`() {
            // given
            val creditCard = CreditCard(
                creditCardId = creditCardId,
                creditCardExternalId = creditCardExternalId,
                dataConsentId = dataConsentId,
                brandName = "VISA",
                companyCnpj = "**************",
                name = "Cartão Teste",
                productType = "CREDIT_CARD",
                productAdditionalInfo = "Cartão Gold",
                creditCardNetwork = "VISA",
                networkAdditionalInfo = "Visa Gold",
                provider = OFProvider.INICIADOR,
            )

            val savedCreditCard = creditCardRepository.save(creditCard, userAccountId)

            savedCreditCard shouldBe creditCard
        }
    }

    @DisplayName("ao buscar um cartão de crédito")
    @Nested
    inner class FindCreditCard {
        @Test
        fun `deve retornar o cartão quando encontrado`() {
            // given
            val creditCard = CreditCard(
                creditCardId = creditCardId,
                creditCardExternalId = creditCardExternalId,
                dataConsentId = dataConsentId,
                brandName = "VISA",
                companyCnpj = "**************",
                name = "Cartão Teste",
                productType = "CREDIT_CARD",
                productAdditionalInfo = "Cartão Gold",
                creditCardNetwork = "VISA",
                networkAdditionalInfo = "Visa Gold",
                provider = OFProvider.INICIADOR,
            )
            creditCardRepository.save(creditCard, userAccountId)

            // when
            val result = creditCardRepository.find(creditCardId)

            // then
            result.fold(
                ifLeft = { throw AssertionError("Não deveria retornar erro") },
                ifRight = { foundCreditCard ->
                    foundCreditCard shouldBe creditCard
                },
            )
        }

        @Test
        fun `deve retornar erro quando cartão não encontrado`() {
            // when
            val result = creditCardRepository.find(creditCardId)

            // then
            result.fold(
                ifLeft = { error ->
                    error.shouldBeInstanceOf<RepositoryError.ItemNotFound>()
                },
                ifRight = { throw AssertionError("Não deveria encontrar o cartão") },
            )
        }
    }

    @DisplayName("ao buscar cartões por consentimento")
    @Nested
    inner class FindByConsentId {
        @Test
        fun `deve retornar lista de cartões quando encontrados`() {
            // given
            val creditCard1 = CreditCard(
                creditCardId = creditCardId,
                creditCardExternalId = creditCardExternalId,
                dataConsentId = dataConsentId,
                brandName = "VISA",
                companyCnpj = "**************",
                name = "Cartão Teste 1",
                productType = "CREDIT_CARD",
                productAdditionalInfo = "Cartão Gold",
                creditCardNetwork = "VISA",
                networkAdditionalInfo = "Visa Gold",
                provider = OFProvider.INICIADOR,
            )
            val creditCard2 = CreditCard(
                creditCardId = CreditCardId("test-card-id-2"),
                creditCardExternalId = CreditCardExternalId("value-2"),
                dataConsentId = dataConsentId,
                brandName = "MASTERCARD",
                companyCnpj = "**************",
                name = "Cartão Teste 2",
                productType = "CREDIT_CARD",
                productAdditionalInfo = "Cartão Platinum",
                creditCardNetwork = "MASTERCARD",
                networkAdditionalInfo = "Mastercard Platinum",
                provider = OFProvider.INICIADOR,
            )
            creditCardRepository.save(creditCard1, userAccountId)
            creditCardRepository.save(creditCard2, userAccountId)

            // when
            val result = creditCardRepository.findByConsentId(dataConsentId)

            // then
            result.size shouldBe 2
            result shouldContainExactlyInAnyOrder listOf(creditCard1, creditCard2)
        }

        @Test
        fun `deve retornar lista vazia quando nenhum cartão encontrado`() {
            // when
            val result = creditCardRepository.findByConsentId(dataConsentId)

            // then
            result shouldBe emptyList()
        }
    }
}