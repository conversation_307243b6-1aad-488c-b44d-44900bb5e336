package ai.friday.openfinance.adapters.messaging

import DynamoDBUtils.setupDynamoDB
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.api.toCreateConsentResponse
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDbRepository
import ai.friday.openfinance.adapters.dynamodb.SweepingConsentDynamoDAO
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.adapters.sqs.SQSMessagePublisher
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.CheckSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.SweepingAccountAdapter
import ai.friday.openfinance.app.integrations.SweepingParticipantAdapter
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimit
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingDebtor
import ai.friday.openfinance.app.sweepingaccount.SweepingLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingParticipant
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import software.amazon.awssdk.services.sqs.model.Message

class CheckSweepingConsentStatusHandlerTest {
    private val dynamoDB = setupDynamoDB()
    private val sweepingConsentRepository = SweepingConsentDbRepository(SweepingConsentDynamoDAO(dynamoDB))

    private val publisher = mockk<SQSMessagePublisher>(relaxed = true)

    private val sweepingAccountAdapter = mockk<SweepingAccountAdapter>()
    private val sweepingParticipantAdapter = mockk<SweepingParticipantAdapter>()
    private val sweepingAccountService = SweepingAccountService(
        sweepingPaymentRepository = mockk(),
        sweepingCreditorRepository = mockk(),
        sweepingConsentRepository = sweepingConsentRepository,
        sweepingConsentLimitUsageRepository = mockk(relaxUnitFun = true),
        sweepingAccountAdapter = sweepingAccountAdapter,
        publisher = publisher,
        sweepingParticipantAdapter = sweepingParticipantAdapter,
        calcLimitUsage = true,
    )
    private val handler = CheckSweepingConsentStatusHandler(
        sweepingAccountService = sweepingAccountService,
    )

    private val userAccountId = UserAccountId("userAccountId")
    private val consentId = SweepingConsentId("consentId")
    private val sweepingLimits = SweepingLimits(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimits(
            day = PeriodicLimit(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = PeriodicLimit(
                quantityLimit = 50,
                transactionLimit = 5_567_00,
            ),
            month = PeriodicLimit(
                quantityLimit = 100,
                transactionLimit = 10_350_00,
            ),
            year = PeriodicLimit(
                quantityLimit = 300,
                transactionLimit = 725_000_00,
            ),
        ),
    )
    private val consent = SweepingConsent(
        userAccountId = userAccountId,
        consentId = consentId,
        participant = SweepingParticipant(
            id = "",
            name = "Itau",
            avatar = null,
        ),
        status = ConsentStatus.AWAITING_AUTHORISATION,
        authUrl = "https://auth.url",
        user = UserConsent(
            taxId = Document(
                value = "***********",
            ),
            name = "",
        ),
        businessEntity = UserConsent(
            taxId = Document(
                value = "**************",
            ),
            name = "",
        ),
        creditors = listOf(),
        startDateTime = getZonedDateTime(),
        expirationDateTime = getZonedDateTime(),
        additionalInformation = null,
        statusUpdateDateTime = getZonedDateTime(),
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        sweepingLimits = sweepingLimits,
        debtor = SweepingDebtor(
            issuer = "0001",
            number = "010101",
            ispb = "000000",
            accountType = "CACC",
        ),
    )

    @BeforeEach
    fun setup() {
        sweepingConsentRepository.save(consent)
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve atualizar o status do consentimento`(forceFetch: Boolean) {
        val consentTO = CheckSweepingConsentStatusMessage(
            consentId = consentId.value,
            forceFetch = forceFetch,
        )

        every { sweepingAccountAdapter.getConsentStatus(consentId) } returns ConsentStatus.AUTHORISED

        every { sweepingAccountAdapter.getConsent(consentId) } returns consent.copy(status = ConsentStatus.AUTHORISED).toCreateConsentResponse()

        val message = getObjectMapper().writeValueAsString(consentTO)

        val result = handler.handleMessage(Message.builder().body(message).build())
        result shouldBe MessageHandlerResponse.delete()

        verify { publisher.sendMessage(OpenFinanceSweepingConsentStatusMessage(sweepingConsentId = consentId.value, status = ConsentStatus.AUTHORISED, debtor = consent.debtor!!.toSweepingDebtorTO(consent.participant.name))) }

        val updatedConsent = sweepingConsentRepository.find(consentId).getOrNull()

        updatedConsent!!.status shouldBe ConsentStatus.AUTHORISED
        updatedConsent.debtor shouldNotBe null
        updatedConsent.debtor!!.issuer shouldBe consent.debtor!!.issuer
        updatedConsent.debtor!!.number shouldBe consent.debtor!!.number
    }

    @Test
    fun `deve expirar o consentimento caso esteja vencido`() {
        val consentTO = CheckSweepingConsentStatusMessage(
            consentId = consentId.value,
        )

        every { sweepingAccountAdapter.getConsent(consentId) } returns consent.copy(status = ConsentStatus.AWAITING_AUTHORISATION).toCreateConsentResponse()

        withGivenDateTime(consent.createdAt.plusMinutes(11)) {
            val message = getObjectMapper().writeValueAsString(consentTO)

            val result = handler.handleMessage(Message.builder().body(message).build())
            result shouldBe MessageHandlerResponse.delete()

            val messageSlot = slot<OpenFinanceSweepingConsentStatusMessage>()
            verify { publisher.sendMessage(capture(messageSlot)) }
            with(messageSlot.captured) {
                sweepingConsentId shouldBe consentId.value
                status shouldBe ConsentStatus.CANCELED
                debtor?.ispb shouldBe consent.debtor?.ispb
                debtor?.routingNumber shouldBe consent.debtor?.issuer
                debtor?.accountNumber shouldBe consent.debtor?.number
                debtor?.accountType shouldBe consent.debtor?.accountType
                debtor?.bankName shouldBe consent.participant.name
            }

            val updatedConsent = sweepingConsentRepository.find(consentId).getOrNull()

            updatedConsent!!.status shouldBe ConsentStatus.CANCELED
        }
    }
}