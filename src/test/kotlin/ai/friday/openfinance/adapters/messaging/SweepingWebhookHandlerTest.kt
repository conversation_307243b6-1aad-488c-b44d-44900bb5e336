package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.api.SweepingWebhookErrorTO
import ai.friday.openfinance.adapters.api.SweepingWebhookTO
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorPaymentStatus
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountServiceError
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.UpdatePaymentCommand
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

class SweepingWebhookHandlerTest {

    private val sweepingAccountService: SweepingAccountService = mockk()

    private val handler = SweepingWebhookHandler(
        sweepingAccountService = sweepingAccountService,
        sweepingParticipantAdapter = mockk(relaxed = true),
    )

    @ParameterizedTest
    @CsvSource(
        value = [
            "STARTED, PROCESSING, null, null",
            "ENQUEUED, PROCESSING, null, null",
            "CONSENT_AWAITING_AUTHORIZATION, PROCESSING, null, null",
            "CONSENT_AUTHORIZED, PROCESSING, null, null",
            "CONSENT_REJECTED, FAILED, consentimento, rejeitado",
            "PAYMENT_PENDING, PROCESSING, null, null",
            "PAYMENT_PARTIALLY_ACCEPTED, PROCESSING, null, null",
            "PAYMENT_SETTLEMENT_PROCESSING, PROCESSING, null, null",
            "PAYMENT_SETTLEMENT_DEBTOR_ACCOUNT, PROCESSING, null, null",
            "PAYMENT_COMPLETED, SUCCESS, null, null",
            "PAYMENT_REJECTED, FAILED, pagamento, rejeitado",
            "CANCELED, FAILED, pagamento, cancelado",
            "ERROR, FAILED, erro, desconhecido",
            "EXPIRED, FAILED, pagamento, expirado",
            "PAYMENT_SCHEDULED, PROCESSING, null, null",
        ],
        nullValues = ["null"],
    )
    fun `deve atualizar o pagamento quando é uma mensagem SEM authorizationId`(externalStatus: IniciadorPaymentStatus, expectedStatus: SweepingPaymentStatus, updatedError: String?, updatedErrorDescription: String?) {
        every {
            sweepingAccountService.updatePayment(any())
        } returns mockk<SweepingPayment>(relaxed = true).right()

        val request = SweepingWebhookTO(
            authorizationId = null,
            id = null,
            date = null,
            description = null,
            createdAt = null,
            consentId = null,
            paymentId = null,
            updatedAt = "2025-01-06T17:46:19.635Z",
            endToEndId = null,
            status = externalStatus.name,
            amount = null,
            externalId = "PAYMENT_EXTERNAL_ID",
            error = updatedError.let {
                SweepingWebhookErrorTO(it, null, updatedErrorDescription)
            },
            errors = listOf(),
        )

        val result = handler.handleMessage(request.toMessage())

        result shouldBe MessageHandlerResponse.delete()

        val slot = slot<UpdatePaymentCommand>()
        verify {
            sweepingAccountService.updatePayment(capture(slot))
        }
        with(slot.captured) {
            paymentRequestId.value shouldBe request.externalId
            status shouldBe expectedStatus
            error shouldBe updatedError
            errorDescription shouldBe updatedErrorDescription
        }
    }

    @Test
    fun `deve manter a mensagem na fila se não conseguir atualizar o pagamento quando é uma mensagem SEM authorizationId`() {
        every {
            sweepingAccountService.updatePayment(any())
        } returns SweepingAccountServiceError.ServerError(IllegalStateException()).left()

        val request = SweepingWebhookTO(
            authorizationId = null,
            id = null,
            date = null,
            description = null,
            createdAt = null,
            consentId = null,
            paymentId = null,
            updatedAt = "2025-01-06T17:46:19.635Z",
            endToEndId = null,
            status = IniciadorPaymentStatus.CANCELED.name,
            amount = null,
            externalId = "PAYMENT_EXTERNAL_ID",
            error = null,
            errors = listOf(),
        )

        val result = handler.handleMessage(request.toMessage())

        result shouldBe MessageHandlerResponse.keep()
    }

    private fun SweepingWebhookTO.toMessage() = Message.builder().body(
        getObjectMapper().writeValueAsString(this),
    ).build()

    @ParameterizedTest
    @EnumSource(ConsentStatus::class)
    fun `deve atualizar o consentimento quando é uma mensagem COM authorizationId`(status: ConsentStatus) {
        every {
            sweepingAccountService.getConsent(any(), forceFetch = true)
        } returns mockk<SweepingConsent>().right()

        val request = SweepingWebhookTO(
            authorizationId = "AUTHORIZATION_ID",
            id = null,
            date = null,
            description = null,
            createdAt = null,
            consentId = null,
            paymentId = null,
            updatedAt = null,
            endToEndId = null,
            status = status.name,
            amount = null,
            externalId = null,
            error = null,
            errors = listOf(),
        )

        val result = handler.handleMessage(request.toMessage())

        result shouldBe MessageHandlerResponse.delete()

        verify {
            sweepingAccountService.getConsent(SweepingConsentId("AUTHORIZATION_ID"), forceFetch = true)
        }
    }

    @Test
    fun `deve manter a mensagem na fila se não conseguir atualizar o consentimento quando é uma mensagem COM authorizationId`() {
        every {
            sweepingAccountService.getConsent(any(), forceFetch = true)
        } returns SweepingAccountServiceError.ServerError(IllegalStateException()).left()

        val request = SweepingWebhookTO(
            authorizationId = "AUTHORIZATION_ID",
            id = null,
            date = null,
            description = null,
            createdAt = null,
            consentId = null,
            paymentId = null,
            updatedAt = null,
            endToEndId = null,
            status = ConsentStatus.AUTHORISED.name,
            amount = null,
            externalId = null,
            error = null,
            errors = listOf(),
        )

        val result = handler.handleMessage(request.toMessage())

        result shouldBe MessageHandlerResponse.keep()
    }
}