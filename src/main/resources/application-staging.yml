micronaut:
  application:
    name: open-finance-service
  metrics:
    enabled: false
    export:
      statsd:
        enabled: false

integrations:
  iniciador:
    host: https://data.sandbox.iniciador.com.br
    accessTokenPath: /v1/data/auth
    clientId: FROM_AWS_SECRETS
    clientSecret: FROM_AWS_SECRETS
    link:
      permissions:
        - REGISTRATION_ALL
        - ACCOUNTS_ALL
        - CREDIT_CARDS_ALL
    sweeping:
      host: https://consumer.sandbox.inic.dev
      clientId: ac44e919-85ec-4097-a8ff-f642e885ebba
      clientSecret: D5H$Aw%tUW*e$mGYxPdYyumY6zvesqzBYCrhggb4
      accessTokenPath: /v1/sweeping/token

open-finance-auth:
  clientid: OPEN_FINANCE_CLIENT_ID-5daed27f-daf1-49ae-93cd-67195a758064
  clientsecret: OPEN_FINANCE_CLIENT_SECRET-9c87d947-e943-4154-82d9-37286cac2816

aws:
  kms:
    keyId: 43764d7e-efd9-48f7-8fe0-20375978f17e
schedules:
  syncBankAccountTransactions:
    cron: "0,30 * * * *"