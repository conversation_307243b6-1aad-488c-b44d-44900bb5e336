micronaut:
  application:
    name: open-finance-service
  metrics:
    enabled: true
  netty:
    event-loops:
      default:
        num-threads: 10
      custom-http-client:
        num-threads: 10
      custom-worker:
        num-threads: 10
  http:
    client:
      read-timeout: 30s
      connection-pool-idle-timeout: 10s
      event-loop-group: custom-http-client
    server:
      read-timeout: 1m
    services:
      iniciadorSweeping:
        url: ${integrations.iniciador.sweeping.host}
        read-timeout: 30s
        http-version: HTTP_1_1
        log-level: DEBUG
        event-loop-group: custom-http-client
        connection-pool-idle-timeout: 1s
  server:
    ssl:
      enabled: true
      buildSelfSigned: true
    netty:
      listeners:
        httpsListener:
          port: 8443
          ssl: true
    worker:
      event-loop-group: custom-worker
  worker:
    event-loop-group: custom-worker
    http2:
      header-table-size: 16384
      max-header-list-size: 16384
    max-request-size: 10485760 #1024L * 1024 * 10
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 10485760 #1024L * 1024 * 10
    http-version: HTTP_2_0
  security:
    enabled: true
  caches:
    participants:
      charset: 'UTF-8'
      expire-after-write: 1m

tracing:
  opentelemetry:
    logging:
      enabled: true
      exporters:
        logging:
          enabled: true
    propagation:
      enabled: true
    instrumentation:
      enabled: true
      annotations:
        enabled: true

otel:
  traces:
    exporter: logging

endpoints:
  health:
    enabled: true
    sensitive: false
    details-visible: ANONYMOUS
    disk-space:
      enabled: false
  metrics:
    enabled: true
    sensitive: false

integrations:
  iniciador:
    host: https://data.iniciador.com.br
    accessTokenPath: /v1/data/auth
    clientId: FROM_AWS_SECRETS
    clientSecret: FROM_AWS_SECRETS
    link:
      permissions:
        - ACCOUNTS_ALL
        - CREDIT_CARDS_ALL
        # - REGISTRATION_ALL
        # - INVESTMENTS_ALL
        # - CREDIT_OPERATIONS_ALL
        # - EXCHANGES_ALL
    sweeping:
      host: https://consumer.iniciador.com.br
      clientId: FROM_AWS_SECRETS
      clientSecret: FROM_AWS_SECRETS
      accessTokenPath: /v1/sweeping/token
      triggerId: 806b1349-e01c-45a7-b14a-0c3105ec28b0

sweeping:
  additionalInformation: ""
  redirectUrl: https://use.friday.ai/app/open-finance/sweeping-connected
data:
  redirectUrl: https://use.friday.ai/app/open-finance/data-connected

open-finance-auth:
  clientid: OPEN_FINANCE_CLIENT_ID-ed2f3006-74c2-47c6-b9ed-8024badc0144
  clientsecret: OPEN_FINANCE_pwd_prod_!@#_to_be_replaced

open-finance-backoffice-auth:
  clientid: OPEN_FINANCE_CLIENT_ID-fb72981c-8892-410c-83f2-9c2ac2e23cc5
  clientsecret: OPEN_FINANCE_pwd_prod_!@#_to_be_replaced

aws:
  region: us-east-1
  dynamodb:
    region: us-east-1
  kms:
    keyId: 46769f9d-d5fe-49d5-9e05-b371399ee37c

friday.morning:
  messaging:
    defaults.consumer:
      corePoolSize: 2
      maximumPoolSize: 10
      keepAliveTime: 60s
      blockingDequeCapacity: 100
      awaitTerminationTime: 110s
      waitTime: 20s
      coolDownTime: 10s
      maxNumberOfMessages: 10
      visibilityTimeout: 300s
      maxReceiveCount: 60
    consumer:
      available-connection:
        queueName: 'available-connection-queue'
      sync-bank-account-transactions:
        queueName: 'sync-bank-account-transactions-queue'
      sync-credit-card:
        queueName: 'sync-credit-card-queue'
      sweeping-webhook:
        queueName: 'sweeping-webhook-queue'
      check-data-consent:
        queueName: 'check-data-consent-queue'
      check-sweeping-consent:
        queueName: 'check-sweeping-consent-queue'
    publisher:
      open-finance:
        queueName: 'open-finance-queue'


shedlock:
  defaults:
    lock-at-most-for: 30m

lock:
  tableName: "Shedlock"

schedules:
  syncBankAccountTransactions:
    cron: "0 10 * * *"
  refreshCreditCardHistory:
    cron: "15 5 20 * *"

log:
  reject-paths:
    - "/health"
    - "/health/"
    - "/metrics/"
    - "/management/health/"
    - "/management/metrics/"

features:
  syncBankAccountTransactions: false
  refreshCreditCardHistory: false
  calcLimitUsage: true