package ai.friday.openfinance.app.bankaccount

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.app.AccountSubType
import ai.friday.openfinance.app.AccountType
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.BankAccountMeta
import ai.friday.openfinance.app.BankTransaction
import ai.friday.openfinance.app.BankTransactionMeta
import ai.friday.openfinance.app.CompletedAuthorisedPaymentType
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.OpenFinanceResourceType
import ai.friday.openfinance.app.TransactionId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.BankAccountAdapter
import ai.friday.openfinance.app.integrations.BankAccountRepository
import ai.friday.openfinance.app.integrations.BankTransactionRepository
import ai.friday.openfinance.app.integrations.KmsService
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.integrations.OFBankAccount
import ai.friday.openfinance.app.integrations.OFBankAccountBalance
import ai.friday.openfinance.app.integrations.OFBankAccountData
import ai.friday.openfinance.app.integrations.OFBankAccountTransaction
import ai.friday.openfinance.app.integrations.OpenFinanceMessage
import ai.friday.openfinance.app.integrations.SyncBankAccountEventTO
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.utils.OpenFinanceDateTimeSupplier
import ai.friday.openfinance.app.utils.PrintableSealedClass
import ai.friday.openfinance.app.utils.RepositoryError
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class BankAccountService(
    private val bankAccountRepository: BankAccountRepository,
    private val bankAccountAdapter: BankAccountAdapter,
    private val dataConsentService: DataConsentService,
    private val transactionRepository: BankTransactionRepository,
    private val publisher: MessagePublisher,
    @Property(name = "friday.morning.messaging.consumer.sync-bank-account-transactions.queueName") private val syncBankAccountTransactionsQueueName: String,
    private val kmsService: KmsService,
) {

    fun findBankTransactions(userAccountId: UserAccountId, bankAccountId: BankAccountId): Either<OpenFinanceConnectionError, List<BankTransactionMeta>> {
        val logName = "bankAccountService#findBankTransactions"
        val markers = Markers.append("accountId", userAccountId).andAppend("bankAccountId", bankAccountId)
        val bankAccount = bankAccountRepository.find(bankAccountId).getOrElse { error ->
            markers.andAppend("error", error)
            logger.error(markers, logName)
            return OpenFinanceConnectionError.ItemNotFound(error.message).left()
        }

        markers.andAppend("bankAccount", bankAccount)

        if (bankAccount.userAccountId != userAccountId) {
            val error = OpenFinanceConnectionError.Unauthorized
            logger.error(markers.andAppend("reason", error.message), logName)
            return error.left()
        }

        val transactions = transactionRepository.findBankAccountTransactions(bankAccountId)

        logger.info(markers.andAppend("transactions", transactions), logName)
        return transactions.right()
    }

    fun processAvailableConnection(dataConsent: DataConsent): Either<OpenFinanceConnectionError, Unit> {
        val logName = "bankAccountService#processAvailableConnection"
        val markers = Markers.append("consentId", dataConsent.id.value)

        markers.andAppend("dataConsent", dataConsent)

        return when {
            dataConsent.status != DataConsentStatus.AVAILABLE -> OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable.left()
            dataConsent.permissionsGranted.none { it == DataConsentPermission.ACCOUNTS_ALL } -> OpenFinanceConnectionError.PermissionsNotGranted.left()
            else -> createBankAccounts(dataConsent).map { Unit }
        }.also {
            it.onRight {
                logger.info(markers, logName)
            }.onLeft {
                markers.andAppend("error", it)
                logger.error(markers, logName)
            }
        }
    }

    fun syncBankTransactionsCurrent() {
        dataConsentService.findAllByStatus(DataConsentStatus.AVAILABLE).forEach { connection ->
            val consentId = connection.id
            val userAccountId = connection.userAccountId
            val bankAccounts = bankAccountRepository.findByConsentId(consentId)

            bankAccounts.forEach { bankAccount ->
                publisher.sendMessage(
                    syncBankAccountTransactionsQueueName,
                    SyncBankAccountEventTO(
                        consentId = consentId.value,
                        userAccountId = userAccountId.value,
                        bankAccountId = bankAccount.bankAccountId.value,
                        recentTransactions = true,
                    ),
                )
            }
        }
    }

    fun syncBankTransactions(recentTransactions: Boolean, userAccountId: UserAccountId, dataConsentId: DataConsentId, bankAccountId: BankAccountId): List<BankTransactionMeta> {
        val logName = "BankAccountService#syncBankTransactions"
        val markers = Markers.append("consentId", dataConsentId.value)
            .andAppend("userAccountId", userAccountId.value)
            .andAppend("bankAccountId", bankAccountId.value)
            .andAppend("recentTransactions", recentTransactions)

        val result = bankAccountAdapter.getBankAccountTransactions(
            recentTransactions = recentTransactions,
            dataConsentId = dataConsentId,
            bankAccountId = bankAccountId,
            startDate = null,
        ).getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return emptyList()
        }

        logger.info(
            markers
                .andAppend("fetchedTransactions", result.groupBy(keySelector = { it.completedAuthorisedPaymentType }, valueTransform = { it.transactionId }))
                .andAppend("fetchedTransactionsSize", result.size),
            logName,
        )

        return result.filter {
            it.completedAuthorisedPaymentType == CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA &&
                (transactionRepository.find(it.transactionId, bankAccountId).leftOrNull() is RepositoryError.ItemNotFound)
        }.map { bankAccountTransactionTO ->

            val meta = try {
                bankAccountTransactionTO.toBankTransactionMeta(bankAccountId)
            } catch (error: Exception) {
                logger.warn(markers.andAppend("failedTransactionMeta", error.message), logName)
                return@map null
            }

            val transactionMeta = transactionRepository.save(meta)

            val transaction = try {
                bankAccountTransactionTO.toBankTransaction(bankAccountId)
            } catch (error: Exception) {
                logger.warn(markers.andAppend("failedTransactionConversion", error.message), logName)
                return@map null
            }
            val encryptData = runBlocking {
                kmsService.encryptData(getObjectMapper().writeValueAsString(transaction))
            }
            publisher.sendMessage(
                OpenFinanceBankTransactionMessage(
                    userAccountId = userAccountId.value,
                    bankAccountId = bankAccountId.value,
                    transactionId = transaction.transactionId.value,
                    transactionDate = transaction.transactionDate.format(dateTimeFormat),
                    data = encryptData,
                ),
            )
            transactionMeta
        }.filterNotNull().also { savedTransactions ->
            logger.info(
                markers
                    .andAppend("savedTransactions", savedTransactions.groupBy(keySelector = { it.transactionStatus.name }, valueTransform = { it.transactionId.value }))
                    .andAppend("savedTransactionsSize", savedTransactions.size),
                logName,
            )
        }
    }

    fun createBankAccounts(connection: DataConsent): Either<OpenFinanceConnectionError, List<BankAccountMeta>> {
        val logName = "BankAccountService#createBankAccounts"
        val consentId = connection.id
        val userAccountId = connection.userAccountId

        val markers = Markers.append("consentId", consentId.value)
            .andAppend("userAccountId", userAccountId.value)
            .andAppend("openFinanceConnectionStatus", connection.status.name)

        if (connection.status != DataConsentStatus.AVAILABLE) {
            markers.andAppend("error", "Connection is not available")
            logger.error(markers, logName)
            return OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable.left()
        }

        val bankAccounts = bankAccountAdapter.getBankAccounts(consentId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return OpenFinanceConnectionError.ServerError(it).left()
        }

        return bankAccounts.map { ofBankAccount ->
            createBankAccount(
                userAccountId = connection.userAccountId,
                consentId = connection.id,
                ofBankAccount = ofBankAccount,
            ).map {
                it
            }.getOrElse { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)
                return error.left()
            }
        }.right()
    }

    private fun createBankAccount(userAccountId: UserAccountId, consentId: DataConsentId, ofBankAccount: OFBankAccount): Either<OpenFinanceConnectionError.ServerError, BankAccountMeta> {
        val logName = "BankAccountService#createBankAccounts"
        val markers = Markers.append("userAccountId", userAccountId.value)
            .andAppend("consentId", consentId.value)
            .andAppend("ofBankAccount", ofBankAccount)

        val ofBankAccountData = bankAccountAdapter.getBankAccountData(consentId, ofBankAccount.bankAccountId).getOrElse { error ->
            logger.error(markers, logName, error)
            return OpenFinanceConnectionError.ServerError(error).left()
        }

        val bankAccountMeta = ofBankAccountData.toBankAccountMeta(
            userAccountId = userAccountId,
            dataConsentId = consentId,
        )
        bankAccountRepository.save(bankAccountMeta)

        publisher.sendMessage(
            OpenFinanceBankAccountMessage(
                userAccountId = userAccountId.value,
                dataConsentId = consentId.value,
                bankAccountId = ofBankAccount.bankAccountId.value,
                bankCode = ofBankAccount.compeCode,
                brandName = ofBankAccount.brandName,
                ispb = ofBankAccount.companyCnpj.take(8),
                routingNumber = ofBankAccount.branchCode,
                accountNumber = ofBankAccount.number,
                accountDv = ofBankAccount.checkDigit,
                accountType = ofBankAccount.type,
                accountSubType = ofBankAccountData.subtype,
                currency = ofBankAccountData.currency,
            ),
        )
        publisher.sendMessage(
            queueName = syncBankAccountTransactionsQueueName,
            body = SyncBankAccountEventTO(
                consentId = consentId.value,
                userAccountId = userAccountId.value,
                bankAccountId = ofBankAccount.bankAccountId.value,
                recentTransactions = false,
            ),
        )
        markers.andAppend("bankAccountId", ofBankAccount.bankAccountId.value)
            .andAppend("bankCode", bankAccountMeta.bankCode)
        logger.info(markers, logName)

        return bankAccountMeta.right()
    }

    fun getBankAccountBalance(
        dataConsentId: DataConsentId,
        bankAccountId: BankAccountId,
    ): Either<Exception, OFBankAccountBalance> = bankAccountAdapter.getBankAccountBalance(
        dataConsentId = dataConsentId,
        bankAccountId = bankAccountId,
    )

    companion object {
        val logger = LoggerFactory.getLogger(BankAccountService::class.java)
    }
}

data class OpenFinanceSweepingConsentStatusMessage(
    val sweepingConsentId: String,
    val status: ConsentStatus,
    val debtor: SweepingDebtorTO? = null,
) : OpenFinanceMessage {
    override val resourceType = OpenFinanceResourceType.SWEEPING_ACCOUNT
}

data class OpenFinanceDataConsentStatusMessage(
    val dataConsentId: String,
    val userAccountId: String,
    val status: DataConsentStatus,
) : OpenFinanceMessage {
    override val resourceType = OpenFinanceResourceType.DATA_CONSENT
}

data class SweepingDebtorTO(
    val ispb: String,
    val routingNumber: String,
    val accountNumber: String,
    val accountType: String,
    val bankName: String?,
)

data class OpenFinanceBankAccountMessage(
    val userAccountId: String,
    val dataConsentId: String,
    val bankAccountId: String,
    val bankCode: String,

    val brandName: String,
    val ispb: String,

    val routingNumber: String,
    val accountNumber: String,
    val accountDv: String,
    val accountType: AccountType,
    val accountSubType: AccountSubType,
    val currency: String,
) : OpenFinanceMessage {
    override val resourceType = OpenFinanceResourceType.BANK_ACCOUNT
}

data class OpenFinanceBankTransactionMessage(
    val userAccountId: String,
    val bankAccountId: String,
    val transactionId: String,
    val transactionDate: String,
    val createdAt: String = getZonedDateTime().format(dateFormatBR),
    val updatedAt: String = getZonedDateTime().format(dateFormatBR),
    val data: String,
) : OpenFinanceMessage {
    override val resourceType = OpenFinanceResourceType.BANK_TRANSACTION
}

sealed class OpenFinanceConnectionError(open val message: String) : PrintableSealedClass() {
    object PermissionsNotGranted : OpenFinanceConnectionError("Permissions not granted")
    object OpenFinanceConnectionNotAvailable : OpenFinanceConnectionError("Open finance connection not available")
    object Unauthorized : OpenFinanceConnectionError("User is not authorized to access this bank account")
    class ItemNotFound(override val message: String) : OpenFinanceConnectionError(message)
    class ServerError(exception: Throwable) : OpenFinanceConnectionError(exception.localizedMessage.orEmpty())
}

fun OFBankAccountData.toBankAccountMeta(dataConsentId: DataConsentId, userAccountId: UserAccountId): BankAccountMeta {
    return BankAccountMeta(
        userAccountId = userAccountId,
        dataConsentId = dataConsentId,
        bankAccountId = this.bankAccountId,
        bankCode = this.compeCode,
        raw = raw,
    )
}

fun OFBankAccountTransaction.toBankTransaction(bankAccountId: BankAccountId): BankTransaction {
    val transactionDate = OpenFinanceDateTimeSupplier.tryParseZonedDateTime(this.transactionDateTime).getOrElse { e ->
        val marker = Markers.append("transactionId", this.transactionId).andAppend("transactionDateTime", this.transactionDateTime).andAppend("transactionDateError", e.message)
        BankAccountService.logger.warn(marker, "toBankTransactionMeta")
        throw e
    }
    return BankTransaction(
        transactionStatus = this.completedAuthorisedPaymentType,
        agencyNumber = this.partieBranchCode,
        transactionName = this.transactionName,
        transactionId = TransactionId(this.transactionId),
        document = this.partieCnpjCpf?.let { Document(it) },
        type = this.type,
        transactionDate = transactionDate,
        bankCode = this.partieCompeCode,
        amount = this.transactionAmount,
        personType = this.partiePersonType,
        creditDebitType = this.creditDebitType,
        accountNumber = this.partieNumber,
        accountDv = this.partieCheckDigit,
        bankAccountId = bankAccountId,
    )
}

fun OFBankAccountTransaction.toBankTransactionMeta(bankAccountId: BankAccountId): BankTransactionMeta {
    val transactionDate = OpenFinanceDateTimeSupplier.tryParseZonedDateTime(this.transactionDateTime).getOrElse { e ->
        val marker = Markers.append("transactionId", this.transactionId).andAppend("transactionDateTime", this.transactionDateTime).andAppend("transactionDateError", e.message)
        BankAccountService.logger.warn(marker, "toBankTransactionMeta")
        throw e
    }
    return BankTransactionMeta(
        transactionStatus = this.completedAuthorisedPaymentType,
        bankAccountId = bankAccountId,
        transactionId = TransactionId(this.transactionId),
        transactionDate = transactionDate,
        raw = this.raw,
    )
}