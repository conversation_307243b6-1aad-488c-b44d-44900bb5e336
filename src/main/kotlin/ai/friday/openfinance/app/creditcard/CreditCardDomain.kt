package ai.friday.openfinance.app.creditcard

import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

data class CreditCardId(val value: String = "CREDIT-CARD-${UUID.randomUUID()}")
data class CreditCardExternalId(val value: String) // Id do cartão no parceiro

data class CreditCard(
    val creditCardId: CreditCardId,
    val creditCardExternalId: CreditCardExternalId,
    val dataConsentId: DataConsentId,
    val brandName: String,
    val companyCnpj: String,
    val name: String,
    val productType: String,
    val productAdditionalInfo: String?,
    val creditCardNetwork: String,
    val networkAdditionalInfo: String?,
    val provider: OFProvider,
)

data class CreditCardTransactionExternalId(val value: String)

data class CreditCardTransaction(
    val dataConsentId: DataConsentId,
    val transactionExternalId: CreditCardTransactionExternalId,
    val identificationNumber: String,
    val transactionName: String,
    val invoiceId: CreditCardInvoiceId?,
    val creditDebitType: String,
    val transactionType: String,
    val transactionalAdditionalInfo: String?,
    val paymentType: String?,
    val feeType: String?,
    val feeTypeAdditionalInfo: String?,
    val otherCreditsType: String?,
    val otherCreditsAdditionalInfo: String?,
    val chargeIdentificator: String?,
    val chargeNumber: String?,
    val originalAmount: CreditCardBillAmount,
    val amount: Long,
    val transactionDateTime: ZonedDateTime,
    val billPostDate: String,
    val payeeMCC: Int,
    val raw: String?,
    val provider: OFProvider,
)

data class CreditCardInvoiceId(val value: String = "CREDIT-CARD-INVOICE-${UUID.randomUUID()}")
data class CreditCardInvoiceExternalId(val value: String)

data class CreditCardInvoice(
    val invoiceId: CreditCardInvoiceId,
    val invoiceExternalId: CreditCardInvoiceExternalId,
    val dueDate: LocalDate,
    val billTotalAmount: Long,
    val billMinimumAmount: Long,
    val billCurrency: String,
    val isInstalment: Boolean,
    val financeCharges: List<CreditCardFinanceCharges>,
    val payments: List<CreditCardPayment>,
    val provider: OFProvider,
)

data class CreditCardBillAmount(
    val amount: Long,
    val currency: String,
)

data class CreditCardFinanceCharges(
    val type: String,
    val additionalInfo: String?,
    val amount: Long,
    val currency: String,
)

data class CreditCardPayment(
    val valueType: String,
    val paymentDate: LocalDate,
    val paymentMode: String,
    val amount: Long,
    val currency: String,
)