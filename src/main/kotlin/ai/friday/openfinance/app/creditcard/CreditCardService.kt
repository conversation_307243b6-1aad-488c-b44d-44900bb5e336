package ai.friday.openfinance.app.creditcard

import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceConnectionError
import ai.friday.openfinance.app.integrations.CreditCardInvoiceRepository
import ai.friday.openfinance.app.integrations.CreditCardRepository
import ai.friday.openfinance.app.integrations.CreditCardTransactionRepository
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.integrations.OpenFinanceCreditCardAdapter
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessage
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessageEventType
import ai.friday.openfinance.app.utils.andAppend
import ai.friday.openfinance.app.utils.toYearMonth
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.YearMonth
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class CreditCardService(
    private val creditCardAdapter: OpenFinanceCreditCardAdapter,
    private val creditCardRepository: CreditCardRepository,
    private val creditCardInvoiceRepository: CreditCardInvoiceRepository,
    private val creditCardTransactionRepository: CreditCardTransactionRepository,
    private val dataConsentRepository: DataConsentRepository,
    private val publisher: MessagePublisher,
    @Property(name = "features.refreshCreditCardHistory") private val refreshCreditCardHistoryEnabled: Boolean,
) {
    private val logger = LoggerFactory.getLogger(CreditCardService::class.java)

    data class MonthlyDebt(
        val totalAmount: Long,
        val financedAmount: Long,
    )

    data class CreditCardOpenTransactions(
        val creditCardId: CreditCardId,
        val totalAmount: Long,
        val transactions: List<CreditCardTransaction>,
    )

    data class CreditCardMonthlyTransactions(
        val creditCardId: CreditCardId,
        val yearMonth: YearMonth,
        val totalAmount: Long,
        val transactions: List<CreditCardTransaction>,
    )

    data class CreditCardInvoices(
        val creditCardId: CreditCardId,
        val invoices: List<CreditCardInvoice>,
    )

    fun processAvailableConnection(dataConsent: DataConsent): Either<OpenFinanceConnectionError, Unit> {
        if (!refreshCreditCardHistoryEnabled) {
            return Unit.right()
        }

        val logName = "CreditCardService#processAvailableConnection"
        val markers = Markers.append("consentId", dataConsent.id.value)
            .andAppend("dataConsent", dataConsent)

        return when {
            dataConsent.status != DataConsentStatus.AVAILABLE -> OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable.left()
            dataConsent.permissionsGranted.none { it == DataConsentPermission.CREDIT_CARDS_ALL } -> OpenFinanceConnectionError.PermissionsNotGranted.left()
            else -> {
                publisher.sendMessage(
                    OpenFinanceSyncCreditCardMessage(
                        eventType = OpenFinanceSyncCreditCardMessageEventType.CARD_REFRESH,
                        userAccountId = dataConsent.userAccountId.value,
                        dataConsentId = dataConsent.id.value,
                        creditCardId = null,
                        invoiceId = null,
                    ),
                )
                Unit.right()
            }
        }.also {
            it.onRight {
                logger.info(markers, logName)
            }.onLeft { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)
            }
        }
    }

    fun refreshCreditCards(dataConsent: DataConsent, force: Boolean = false): Either<OpenFinanceConnectionError, List<CreditCard>> {
        val logName = "CreditCardService#refreshCreditCards"
        val markers = Markers.append("consentId", dataConsent.id.value)
            .andAppend("userAccountId", dataConsent.userAccountId.value)

        if (dataConsent.status != DataConsentStatus.AVAILABLE) {
            markers.andAppend("error", "Connection is not available")
            logger.error(markers, logName)
            return OpenFinanceConnectionError.OpenFinanceConnectionNotAvailable.left()
        }

        // Busca os cartões atuais do usuário
        val storedDatabaseCards = creditCardRepository.findByConsentId(dataConsent.id)

        if (!force && storedDatabaseCards.isEmpty() && !refreshCreditCardHistoryEnabled) {
            return emptyList<CreditCard>().right().also {
                logger.info(markers.andAppend("context", "No current credit cards found and refreshCreditCardHistory is disabled."), logName)
            }
        }

        // Busca os cartões atualizados do Open Finance
        val remoteReceivedCards = creditCardAdapter.getCreditCards(dataConsent.id).getOrElse {
            logger.error(markers, logName, it)
            return OpenFinanceConnectionError.ServerError(it).left()
        }

        // Atualiza os cartões existentes e adiciona os novos
        return remoteReceivedCards.map { remoteReceivedCard ->
            val existingCard = storedDatabaseCards.find { it.creditCardExternalId == remoteReceivedCard.creditCardExternalId }

            if (existingCard != null) {
                publisher.sendMessage(
                    OpenFinanceSyncCreditCardMessage(
                        eventType = OpenFinanceSyncCreditCardMessageEventType.INVOICE_REFRESH,
                        userAccountId = dataConsent.userAccountId.value,
                        dataConsentId = existingCard.dataConsentId.value,
                        creditCardId = existingCard.creditCardId.value,
                        invoiceId = null,
                    ),
                )
                existingCard
            } else {
                // Salva o novo cartão
                creditCardRepository.save(remoteReceivedCard, dataConsent.userAccountId).also {
                    publisher.sendMessage(
                        OpenFinanceSyncCreditCardMessage(
                            eventType = OpenFinanceSyncCreditCardMessageEventType.CARD_CREATED,
                            userAccountId = dataConsent.userAccountId.value,
                            dataConsentId = remoteReceivedCard.dataConsentId.value,
                            creditCardId = remoteReceivedCard.creditCardId.value,
                            invoiceId = null,
                        ),
                    )
                }
            }
        }.right().also {
            logger.info(markers, logName)
        }
    }

    fun retrieveAndStoreInvoices(dataConsent: DataConsent, creditCardId: CreditCardId): Either<Exception, List<CreditCardInvoice>> {
        val logName = "CreditCardService#retrieveAndStoreInvoices"
        val markers = Markers.append("consentId", dataConsent.id.value)
            .andAppend("creditCardId", creditCardId.value)
            .andAppend("userAccountId", dataConsent.userAccountId.value)

        val creditCard = creditCardRepository.find(creditCardId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return Exception(it.message).left()
        }

        val storedDatabaseInvoices = creditCardInvoiceRepository.findByCreditCardId(creditCardId)

        val remoteReceivedInvoices = creditCardAdapter.getCreditCardInvoices(dataConsent.id, creditCard.creditCardExternalId).getOrElse { error ->
            logger.error(markers, logName, error)
            return error.left()
        }

        return remoteReceivedInvoices.map { remoteReceivedInvoice ->
            val existingInvoice = storedDatabaseInvoices.find { it.invoiceExternalId == remoteReceivedInvoice.invoiceExternalId }
            if (existingInvoice != null) {
                val remoteReceivedInvoiceWithFixedId = remoteReceivedInvoice.copy(invoiceId = existingInvoice.invoiceId)
                if (remoteReceivedInvoiceWithFixedId != existingInvoice) {
                    creditCardInvoiceRepository.save(remoteReceivedInvoiceWithFixedId, dataConsent.id, dataConsent.userAccountId, creditCardId)
                    publisher.sendMessage(
                        OpenFinanceSyncCreditCardMessage(
                            eventType = OpenFinanceSyncCreditCardMessageEventType.INVOICE_UPDATED,
                            userAccountId = dataConsent.userAccountId.value,
                            dataConsentId = dataConsent.id.value,
                            creditCardId = creditCardId.value,
                            invoiceId = remoteReceivedInvoice.invoiceId.value,
                        ),
                    )
                }
                remoteReceivedInvoiceWithFixedId
            } else {
                creditCardInvoiceRepository.save(invoice = remoteReceivedInvoice, dataConsentId = dataConsent.id, userAccountId = dataConsent.userAccountId, creditCardId).also {
                    publisher.sendMessage(
                        OpenFinanceSyncCreditCardMessage(
                            eventType = OpenFinanceSyncCreditCardMessageEventType.INVOICE_CREATED,
                            userAccountId = dataConsent.userAccountId.value,
                            dataConsentId = dataConsent.id.value,
                            creditCardId = creditCardId.value,
                            invoiceId = remoteReceivedInvoice.invoiceId.value,
                        ),
                    )
                }
            }
        }.right().also {
            logger.info(markers, logName)
        }
    }

    fun retrieveAndStoreInvoicesTransactions(dataConsent: DataConsent, userAccountId: UserAccountId, creditCardId: CreditCardId, invoiceId: CreditCardInvoiceId): Either<Exception, List<CreditCardTransaction>> {
        val logName = "CreditCardService#retrieveAndStoreInvoicesTransactions"
        val markers = Markers.append("consentId", dataConsent.id.value)
            .andAppend("creditCardId", creditCardId.value)
            .andAppend("invoiceId", invoiceId.value)
            .andAppend("userAccountId", dataConsent.userAccountId.value)

        val creditCard = creditCardRepository.find(creditCardId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return Exception(it.message).left()
        }

        val invoice = creditCardInvoiceRepository.find(invoiceId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return Exception(it.message).left()
        }

        return creditCardAdapter.getCreditCardInvoiceTransactions(dataConsent.id, creditCard.creditCardExternalId, invoice.invoiceExternalId).map { transactions ->
            transactions.forEach { transaction ->
                creditCardTransactionRepository.find(transactionExternalId = transaction.transactionExternalId, creditCardId = creditCard.creditCardId).map { currentTransaction ->
                    if (currentTransaction != transaction) {
                        creditCardTransactionRepository.save(transaction, userAccountId, creditCard.creditCardId)
                    }
                }.getOrElse {
                    creditCardTransactionRepository.save(transaction, userAccountId, creditCard.creditCardId)
                }
            }
            transactions
        }.getOrElse { error ->
            logger.error(markers, logName, error)
            return error.left()
        }.right().also {
            logger.info(markers, logName)
        }
    }

    fun retrieveAndStoreTransactions(dataConsent: DataConsent, creditCardId: CreditCardId, startDate: LocalDate, endDate: LocalDate): Either<Exception, List<CreditCardTransaction>> {
        val logName = "CreditCardService#retrieveAndStoreTransactions"
        val markers = Markers.append("consentId", dataConsent.id.value)
            .andAppend("creditCardId", creditCardId.value)
            .andAppend("startDate", startDate)
            .andAppend("endDate", endDate)
            .andAppend("userAccountId", dataConsent.userAccountId.value)

        val creditCard = creditCardRepository.find(creditCardId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return Exception(it.message).left()
        }

        return creditCardAdapter.getCreditCardTransactions(dataConsent.id, creditCard.creditCardExternalId, startDate, endDate).map { transactions ->
            transactions.forEach { transaction ->
                creditCardTransactionRepository.save(transaction, dataConsent.userAccountId, creditCard.creditCardId)
            }
            transactions
        }.getOrElse { error ->
            logger.error(markers, logName, error)
            return error.left()
        }.right().also {
            logger.info(markers, logName)
        }
    }

    fun calculateUserDebtByMonth(userAccountId: UserAccountId, since: YearMonth): Map<YearMonth, MonthlyDebt> {
        val creditCards = findAllAvailableCreditCards(userAccountId)
        val allInvoices = creditCards.flatMap { creditCard ->
            creditCardInvoiceRepository.findByCreditCardId(creditCard.creditCardId).filter { it.dueDate >= since.atDay(1) }
        }

        return allInvoices
            .groupBy { invoice ->
                invoice.dueDate.toYearMonth()
            }
            .mapValues { (_, invoices) ->
                invoices.fold(MonthlyDebt(0, 0)) { acc, invoice ->
                    val financeChargesTotal = invoice.financeCharges.sumOf { charge -> charge.amount }
                    MonthlyDebt(
                        totalAmount = acc.totalAmount + invoice.billTotalAmount,
                        financedAmount = acc.financedAmount + financeChargesTotal,
                    )
                }
            }
    }

    fun getOpenTransactionsByCard(userAccountId: UserAccountId): List<CreditCardOpenTransactions> {
        val creditCards = findAllAvailableCreditCards(userAccountId)

        return creditCards.map { creditCard ->
            val transactions = creditCardTransactionRepository
                .findByCreditCardId(creditCard.creditCardId, null)
                .filter { it.invoiceId == null }

            CreditCardOpenTransactions(
                creditCardId = creditCard.creditCardId,
                totalAmount = transactions.sumOf { it.amount },
                transactions = transactions,
            )
        }
    }

    fun getTransactionsByMonth(userAccountId: UserAccountId, yearMonth: YearMonth): List<CreditCardMonthlyTransactions> {
        val creditCards = findAllAvailableCreditCards(userAccountId)

        return creditCards.map { creditCard ->
            val transactions = creditCardTransactionRepository.findByCreditCardId(creditCard.creditCardId, yearMonth)

            CreditCardMonthlyTransactions(
                creditCardId = creditCard.creditCardId,
                yearMonth = yearMonth,
                totalAmount = transactions.sumOf { it.amount },
                transactions = transactions,
            )
        }
    }

    fun getAllInvoices(userAccountId: UserAccountId): List<CreditCardInvoices> {
        val creditCards = findAllAvailableCreditCards(userAccountId)

        return creditCards.map { creditCard ->
            val invoices = creditCardInvoiceRepository.findByCreditCardId(creditCard.creditCardId)

            CreditCardInvoices(
                creditCardId = creditCard.creditCardId,
                invoices = invoices,
            )
        }
    }

    private fun findAllAvailableCreditCards(userAccountId: UserAccountId): List<CreditCard> {
        val activeUserConsents = dataConsentRepository.findByUserAccountId(userAccountId).filter { it.status == DataConsentStatus.AVAILABLE }
        return activeUserConsents.flatMap {
            creditCardRepository.findByConsentId(it.id)
        }
    }
}