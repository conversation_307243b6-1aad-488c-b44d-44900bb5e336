package ai.friday.openfinance.app

const val CPF_SIZE = 11
const val CNPJ_SIZE = 14

private val invalidCpfs = listOf(
    "00000000000",
    "11111111111",
    "22222222222",
    "33333333333",
    "44444444444",
    "55555555555",
    "66666666666",
    "77777777777",
    "88888888888",
    "99999999999",
    "12345678909",
)

data class Document(val value: String) {
    init {
        // val (digits, notDigits) = value.partition { it.isDigit() }
        // require(value.length == CPF_SIZE || value.length == CNPJ_SIZE && notDigits.isEmpty())
    }

    val type: DocumentType = when (value.length) {
        CPF_SIZE -> DocumentType.CPF
        CNPJ_SIZE -> DocumentType.CNPJ // FIXME
        else -> DocumentType.OTHER
    }

    val issuerRegion: String = when (value.length) {
        CPF_SIZE -> {
            when (value[6]) {
                '1' -> "DF-GO-MS-MT-TO"
                '2' -> "AC-AM-AP-PA-RO-RR"
                '3' -> "CE-MA-PI"
                '4' -> "AL-PB-PE-RN"
                '5' -> "BA-SE"
                '6' -> "MG"
                '7' -> "ES-RJ"
                '8' -> "SP"
                '9' -> "PR-SC"
                '0' -> "RS"
                else -> "UNKNOWN"
            }
        }

        else -> "UNKNOWN"
    }

    val issuer = "SRF"
}

fun String.isValidCpf(): Boolean {
    if (length != CPF_SIZE || this in invalidCpfs) {
        return false
    }

    var numbers = substring(0, 9)

    numbers += verifierDigits(numbers)
    numbers += verifierDigits(numbers)

    return numbers.takeLast(2) == takeLast(2)
}

private fun verifierDigits(digits: String): Number {
    val numbers = digits.map {
        it.digitToInt()
    }

    val modulus = numbers.size + 1
    val multiplied = numbers.mapIndexed { index, number ->
        number * (modulus - index)
    }
    val mod = multiplied.sum() % 11

    return if (mod < 2) 0 else 11 - mod
}

fun Document.isValidCnpj(): Boolean {
    return this.value.isValidCnpj()
}

fun String.isValidCnpj(): Boolean {
    if (length != CNPJ_SIZE) {
        return false
    }

    if (!validateCNPJRepeatedNumbers(this)) {
        return false
    }

    return validateCNPJVerificationDigit(true, this) && validateCNPJVerificationDigit(false, this)
}

fun String.sanitizeDocumentNumber(): String { // FIXME: BankStatement não tem o tipo de documento, atualmente
    if (isBlank() || this.isValidCpf() || this.isValidCnpj()) {
        return this
    }

    val trimmed = this.dropWhile { it == '0' }
    if (trimmed.isValidCpf() || trimmed.isValidCnpj()) {
        return trimmed
    }

    if (trimmed.length < CPF_SIZE) {
        val paddedCPF = trimmed.padStart(CPF_SIZE, '0')
        if (paddedCPF.isValidCpf()) {
            return paddedCPF
        }
    }
    val paddedCNPJ = trimmed.padStart(CNPJ_SIZE, '0')
    if (paddedCNPJ.isValidCnpj()) {
        return paddedCNPJ
    }
    return this
}

private fun validateCNPJRepeatedNumbers(cnpj: String): Boolean {
    return (0..9)
        .map { it.toString().repeat(14) }
        .map { cnpj == it }
        .all { !it }
}

/**
 * Verifies the CNPJ verification digit.
 *
 * This algorithm checks the verification digit (dígito verificador) do CNPJ.
 * This was based from: https://www.devmedia.com.br/validando-o-cnpj-em-uma-aplicacao-java/22374
 *
 * @param[firstDigit] True when checking the first digit. False to check the second digit.
 *
 * @return True if valid.
 */
private fun validateCNPJVerificationDigit(firstDigit: Boolean, cnpj: String): Boolean {
    val (startPos, weightOffset) = when (firstDigit) {
        true -> 11 to 0
        false -> 12 to 1
    }

    val sum = (startPos downTo 0).fold(0) { acc, pos ->
        val weight = 2 + ((11 + weightOffset - pos) % 8)
        val num = cnpj[pos].toString().toInt()
        val sum = acc + (num * weight)
        sum
    }

    val expectedDigit = when (val result = sum % 11) {
        0, 1 -> 0
        else -> 11 - result
    }

    val actualDigit = cnpj[startPos + 1].toString().toInt()

    return expectedDigit == actualDigit
}
enum class DocumentType {
    CPF,
    CNPJ,
    OTHER,
}