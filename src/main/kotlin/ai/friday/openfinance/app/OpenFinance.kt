package ai.friday.openfinance.app

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.ZonedDateTime
import java.util.UUID

data class BankAccount(
    val userAccountId: UserAccountId,
    val dataConsentId: DataConsentId,
    val bankAccountId: BankAccountId,
    val bankCode: String,
    val brandName: String,
    val companyCnpj: String,
    val type: AccountType,
    val subtype: AccountSubType,
    val agencyNumber: String,
    val accountNumber: String,
    val accountDv: String,
    val currency: String,
)

data class BankAccountMeta(
    val userAccountId: UserAccountId,
    val dataConsentId: DataConsentId,
    val bankAccountId: BankAccountId,
    val bankCode: String,
    val raw: String,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
)

data class BankAccountLimits(
    val overdraftContractedLimit: BankAccountAmount,
    val overdraftUsedLimit: BankAccountAmount,
    val unarrangedOverdraftAmount: BankAccountAmount,
)

data class BankTransaction(
    val transactionStatus: CompletedAuthorisedPaymentType,
    val bankAccountId: BankAccountId,
    val agencyNumber: String? = null,
    val transactionName: String,
    val transactionId: TransactionId,
    val document: Document? = null,
    val type: TransactionType,
    val transactionDate: ZonedDateTime,
    val bankCode: String? = null,
    val amount: BankAccountAmount,
    val personType: TransactionPersonType? = null,
    val creditDebitType: CreditDebitType,
    val accountNumber: String? = null,
    val accountDv: String? = null,
)

data class BankTransactionMeta(
    val transactionStatus: CompletedAuthorisedPaymentType,
    val bankAccountId: BankAccountId,
    val transactionId: TransactionId,
    val transactionDate: ZonedDateTime,
    val raw: String,
    val createdAt: ZonedDateTime? = null,
    val updatedAt: ZonedDateTime? = null,
)

data class DataConsentLink(
    val dataConsentId: DataConsentId,
    val link: String,
)

data class DataConsent(
    val id: DataConsentId,
    val requestId: DataConsentRequestId,
    val userAccountId: UserAccountId,
    val status: DataConsentStatus,
    val permissions: List<DataConsentPermission>,
    val permissionsGranted: List<DataConsentPermission>,
    val bankId: String,
    val participantId: ParticipantId,
    val bankName: String,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
)

enum class DataConsentStatus(val final: Boolean) {
    AVAILABLE(true), UNAVAILABLE(true), REVOKED(true), TEMPORARILY_UNAVAILABLE(false), PENDING_AUTHORISATION(false),
}

enum class OpenFinanceDataConsentStatus(val internalStatus: DataConsentStatus) {
    STARTED(DataConsentStatus.PENDING_AUTHORISATION),
    AWAITING_AUTHORISATION(DataConsentStatus.PENDING_AUTHORISATION),
    AWAITING_LGPD_AUTHORISATION(DataConsentStatus.PENDING_AUTHORISATION),
    ERROR(DataConsentStatus.UNAVAILABLE),
    REJECTED(DataConsentStatus.UNAVAILABLE),
    REVOKED(DataConsentStatus.REVOKED),
    EXPIRED(DataConsentStatus.UNAVAILABLE),
    DELETED_BY_USER(DataConsentStatus.UNAVAILABLE),
    AUTHORISED(DataConsentStatus.AVAILABLE),
}

enum class OpenFinanceResourceType {
    BANK_ACCOUNT,
    BANK_TRANSACTION,
    CREDIT_CARD,
    CREDIT_CARD_TRANSACTION,
    INVESTMENT_TRANSACTION,
    DATA_CONSENT,
    SWEEPING_ACCOUNT,
    SWEEPING_PAYMENT,
}

data class BankAccountAmount(
    val amount: String,
    val currency: String,
) {
    val amountInCents: Long = BigDecimal(amount).multiply(BigDecimal(100)).setScale(0, RoundingMode.HALF_EVEN).toLong()
}

data class DataConsentId(val value: String = "DATA-CONSENT-${UUID.randomUUID()}")

data class DataConsentRequestId(val value: String = "DATA-CONSENT-REQUEST-${UUID.randomUUID()}")

data class UserAccountId(val value: String)

data class BankAccountId(val value: String = "BANK-ACCOUNT-${UUID.randomUUID()}")

data class TransactionId(val value: String)

data class ParticipantId(val value: String)

enum class CompletedAuthorisedPaymentType {
    TRANSACAO_EFETIVADA, LANCAMENTO_FUTURO, TRANSACAO_PROCESSANDO
}

enum class CreditDebitType {
    CREDITO, DEBITO
}

enum class TransactionType {
    TED, DOC, PIX, TRANSFERENCIA_MESMA_INSTITUICAO, BOLETO, CONVENIO_ARRECADACAO, PACOTE_TARIFA_SERVICOS, TARIFA_SERVICOS_AVULSOS, FOLHA_PAGAMENTO, DEPOSITO, SAQUE, CARTAO, ENCARGOS_JUROS_CHEQUE_ESPECIAL, RENDIMENTO_APLIC_FINANCEIRA, PORTABILIDADE_SALARIO, RESGATE_APLIC_FINANCEIRA, OPERACAO_CREDITO, OUTROS,
}

enum class TransactionPersonType {
    PESSOA_NATURAL, PESSOA_JURIDICA,
}

enum class AccountType {
    CONTA_DEPOSITO_A_VISTA, CONTA_POUPANCA, CONTA_PAGAMENTO_PRE_PAGA
}

enum class AccountSubType {
    INDIVIDUAL, CONJUNTA_SIMPLES, CONJUNTA_SOLIDARIA,
}

enum class DataConsentPermission {
    REGISTRATION_ALL,
    REGISTRATION_IDENTIFICATIONS,
    REGISTRATION_QUALIFICATIONS,
    REGISTRATION_FINANCIAL_RELATIONS,
    ACCOUNTS_ALL,
    ACCOUNTS_LIST,
    ACCOUNTS_BALANCES,
    ACCOUNTS_LIMITS,
    ACCOUNTS_TRANSACTIONS,
    CREDIT_CARDS_ALL,
    CREDIT_CARDS_LIST,
    CREDIT_CARDS_LIMITS,
    CREDIT_CARDS_TRANSACTIONS,
    CREDIT_CARDS_BILLS,
    CREDIT_OPERATIONS_ALL,
    INVESTMENTS_ALL,
    EXCHANGES_ALL,
}

enum class OFProvider {
    INICIADOR,
}