package ai.friday.openfinance.app.sweepingaccount

import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentExternalId
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

data class SweepingCreditor(
    val userAccountId: UserAccountId,
    val id: CreditorId = CreditorId(),
    val type: CreditorType,
    val userTaxId: Document,
    val creditorTaxId: Document,
    val name: String? = null,
    val ispb: String? = null,
    val issuer: String? = null,
    val number: String? = null,
    val accountType: CreditorAccountType? = null,
    val participant: ParticipantCreditor? = null,
    val status: String? = null,
    val createdAt: ZonedDateTime? = null,
    val updatedAt: ZonedDateTime? = null,
)

data class CreateCreditorRequest(
    val type: CreditorType,
    val userTaxId: Document,
    val creditorTaxId: Document,
    val name: String? = null,
    val ispb: String? = null,
    val issuer: String? = null,
    val number: String? = null,
    val accountType: CreditorAccountType? = null,
)

data class CreateCreditorResponse(
    val id: CreditorId,
    val type: CreditorType,
    val userTaxId: Document,
    val creditorTaxId: Document,
    val name: String? = null,
    val ispb: String? = null,
    val issuer: String? = null,
    val number: String? = null,
    val accountType: CreditorAccountType? = null,
    val participant: ParticipantCreditor? = null,
    val status: String,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
)

data class CreateConsentRequest(
    val user: UserConsent,
    val participantId: ParticipantId,
    val creditors: List<CreditorId>,
    val sweepingLimits: SweepingLimits,
)

data class CreateConsentResponse(
    val consentId: SweepingConsentId,
    val participant: SweepingParticipant,
    val status: ConsentStatus,
    val authUrl: String,
    val user: UserConsent,
    val businessEntity: UserConsent? = null,
    val startDateTime: ZonedDateTime? = null,
    val expirationDateTime: ZonedDateTime? = null,
    val additionalInformation: String?,
    val statusUpdateDateTime: ZonedDateTime,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val sweepingLimits: SweepingLimits?,
    val debtor: SweepingDebtor?,
)

data class CreateSweepingPaymentRequest(
    val id: SweepingPaymentRequestId,
    val consentId: SweepingConsentId,
    val amount: Long,
    val description: String,
    val creditorId: CreditorId,
    val riskSignals: SweepingRiskSignals,
) {
    fun toSweepingPayment(userAccountId: UserAccountId, participantId: String): SweepingPayment {
        return SweepingPayment(
            requestId = this.id,
            userAccountId = userAccountId,
            consentId = this.consentId,
            creditorId = this.creditorId,
            participantId = participantId,
            amount = this.amount,
            riskSignals = this.riskSignals,
            description = this.description,
            createdAt = BrazilZonedDateTimeSupplier.getZonedDateTime(),
            updatedAt = BrazilZonedDateTimeSupplier.getZonedDateTime(),
            status = SweepingPaymentStatus.CREATED,
        )
    }
}

enum class SweepingRiskSignalsType {
    AUTOMATIC, MANUAL
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "type",
    visible = false,
)
sealed interface SweepingRiskSignals {
    val type: SweepingRiskSignalsType
}

@JsonTypeName("AUTOMATIC")
data class AutomaticSweepingRiskSignals(
    val lastLoginDateTime: ZonedDateTime,
    val pixKeyRegistrationDateTime: ZonedDateTime? = null,
) : SweepingRiskSignals {
    override val type = SweepingRiskSignalsType.AUTOMATIC
}

@JsonTypeName("MANUAL")
data class ManualSweepingRiskSignals(
    val deviceId: String,
    val isRootedDevice: Boolean? = null,
    val screenBrightness: Number? = null,
    val elapsedTimeSinceBoot: java.time.Duration? = null,
    val osVersion: String? = null,
    val userTimeZoneOffset: String? = null,
    val language: String? = null,
    val screenDimensions: ScreenDimensions? = null,
    val accountTenure: LocalDate? = null,
) : SweepingRiskSignals {
    override val type = SweepingRiskSignalsType.MANUAL
}

data class ScreenDimensions(
    val width: Int,
    val height: Int,
)

data class SweepingPaymentResponse(
    val requestId: SweepingPaymentRequestId,
    val externalId: SweepingPaymentExternalId,
    val date: LocalDate? = null,
    val description: String,
    val createdAt: ZonedDateTime,
    val consentId: SweepingConsentId,
    val updatedAt: ZonedDateTime? = null,
    val endToEndId: String? = null,
    val status: SweepingPaymentStatus,
    val amount: Long,
    val fee: Long? = null,
    val method: String ? = null,
    val riskSignals: SweepingRiskSignals,
    val participantId: String,
    val businessEntityTaxId: String? = null,
    val userTaxId: String? = null,
)

data class SweepingPaymentStatusResponse(
    val requestId: SweepingPaymentRequestId,
    val externalId: SweepingPaymentExternalId? = null,
    val status: SweepingPaymentStatus,
    val error: SweepingPaymentStatusError? = null,
    val description: String,
    val date: LocalDate?,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime?,
    val consentId: SweepingConsentId,
    val endToEndId: String?,
    val amount: Long,
    val fee: Long?,
    val method: String?,
    val participantId: String,
    val businessEntityTaxId: String?,
    val userTaxId: String?,
)

data class SweepingPaymentStatusError(
    val code: String?,
    val title: String?,
    val detail: String?,
)

data class ParticipantCreditor(val name: String)

data class CreditorId(val value: String = UUID.randomUUID().toString())
enum class CreditorAccountType {
    CACC,
    SVGS,
    TRAN,
}

enum class CreditorType {
    BANK_ACCOUNT,
    PIX_KEY,
}

enum class ConsentStatus(val final: Boolean) {
    AWAITING_AUTHORISATION(false), PARTIALLY_ACCEPTED(false), AUTHORISED(true), REJECTED(true), REVOKED(true), CONSUMED(true), CANCELED(true)
}