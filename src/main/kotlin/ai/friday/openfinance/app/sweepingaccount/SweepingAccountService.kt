package ai.friday.openfinance.app.sweepingaccount

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceSweepingConsentStatusMessage
import ai.friday.openfinance.app.bankaccount.SweepingDebtorTO
import ai.friday.openfinance.app.integrations.CheckSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.CreateSweepingPaymentAdapterError
import ai.friday.openfinance.app.integrations.GetSweepingPaymentStatusError
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.integrations.SweepingAccountAdapter
import ai.friday.openfinance.app.integrations.SweepingConsentLimitUsageRepository
import ai.friday.openfinance.app.integrations.SweepingConsentRepository
import ai.friday.openfinance.app.integrations.SweepingCreditorRepository
import ai.friday.openfinance.app.integrations.SweepingParticipantAdapter
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentMessage
import ai.friday.openfinance.app.integrations.SweepingPaymentRepository
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.utils.PrintableSealedClass
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.Month
import java.time.ZonedDateTime
import java.util.*
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class SweepingAccountService(
    private val sweepingPaymentRepository: SweepingPaymentRepository,
    private val sweepingCreditorRepository: SweepingCreditorRepository,
    private val sweepingConsentRepository: SweepingConsentRepository,
    private val sweepingConsentLimitUsageRepository: SweepingConsentLimitUsageRepository,
    private val sweepingAccountAdapter: SweepingAccountAdapter,
    private val sweepingParticipantAdapter: SweepingParticipantAdapter,
    private val publisher: MessagePublisher,
    @Property(name = "features.calcLimitUsage") private val calcLimitUsage: Boolean,
) {
    private val logger = LoggerFactory.getLogger(SweepingAccountService::class.java)

    fun requestConsent(userAccountId: UserAccountId, createCreditorRequest: CreateCreditorRequest, user: UserConsent, participantId: ParticipantId, sweepingLimits: SweepingLimits): Either<Exception, SweepingConsent> {
        val logName = "SweepingAccountService#requestConsent"
        val markers = Markers.append("userAccountId", userAccountId.value)
            .andAppend("createCreditorRequest", createCreditorRequest)
            .andAppend("user", user)
            .andAppend("participantId", participantId.value)
            .andAppend("sweepingLimits", sweepingLimits)

        val creditor = createCreditor(userAccountId, createCreditorRequest)
        markers.andAppend("creditor", creditor)

        val response = sweepingAccountAdapter.requestSweepingConsent(
            CreateConsentRequest(
                user = user,
                participantId = participantId,
                creditors = listOf(creditor.id),
                sweepingLimits = sweepingLimits,
            ),
        )

        if (response.sweepingLimits == null) {
            markers.andAppend("ACTION", "VERIFY")
                .andAppend("CONTEXT", "Se a autorização foi criada sem configuração de limite no Iniciador, entrar em contato com o usuário para refazer a autorização.")
            logger.warn(markers, logName)
        }

        publisher.sendMessage(CheckSweepingConsentStatusMessage(consentId = response.consentId.value))

        return response.toSweepingConsent(userAccountId, listOf(creditor.id), sweepingLimits).also {
            markers.andAppend("consent", it)
            sweepingConsentRepository.save(it)
            logger.info(markers, logName)
        }.right()
    }

    fun createCreditor(
        userAccountId: UserAccountId,
        creditor: CreateCreditorRequest,
    ): SweepingCreditor {
        val createdCreditor = sweepingAccountAdapter.createCreditor(creditor)

        return sweepingCreditorRepository.save(createdCreditor.toSweepingCreditor(userAccountId))
    }

    fun createPayment(request: CreateSweepingPaymentRequest): Either<CreateSweepingPaymentServiceError, SweepingPayment> {
        val logName = "SweepingAccountService#createPayment"
        val markers = Markers.append("request", request)

        val consent = sweepingConsentRepository.find(request.consentId).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return CreateSweepingPaymentServiceError.ConsentNotFound(request.consentId).left()
        }
        markers.andAppend("consent", consent)

        val newRequest = when (request.riskSignals) {
            is AutomaticSweepingRiskSignals -> request
            is ManualSweepingRiskSignals -> request.copy(
                riskSignals = request.riskSignals.copy(
                    accountTenure = consent.createdAt.toLocalDate(),
                ),
            )
        }

        val payment = newRequest.toSweepingPayment(consent.userAccountId, consent.participant.id)

        sweepingPaymentRepository.save(payment)

        return sweepingAccountAdapter.createPayment(newRequest).map {
            markers.andAppend("payment", it)
            logger.info(markers, logName)
            sweepingPaymentRepository.save(
                it.toSweepingPayment(
                    userAccountId = consent.userAccountId,
                    creditorId = request.creditorId,
                ),
            ).right()
        }.getOrElse { error ->
            when (error) {
                is CreateSweepingPaymentAdapterError.LimitExceededError -> error.handle(payment, markers, logName).left()
                is CreateSweepingPaymentAdapterError.InvalidConsentStatus -> error.handle(payment, markers, logName).left()
                is CreateSweepingPaymentAdapterError.GenericError -> error.handle(payment, markers, logName)
            }
        }
    }

    private fun CreateSweepingPaymentAdapterError.LimitExceededError.handle(payment: SweepingPayment, markers: LogstashMarker, logName: String): CreateSweepingPaymentServiceError.LimitExceededError {
        markers.andAppend("error", this)
        logger.warn(markers, logName)

        sweepingPaymentRepository.save(
            payment.copy(
                status = SweepingPaymentStatus.FAILED,
                error = limitType.toLimitTypeErrorCode(),
                errorDescription = limitType.toLimitTypeErrorDescription(),
            ),
        )

        return CreateSweepingPaymentServiceError.LimitExceededError(limitType)
    }

    private fun CreateSweepingPaymentAdapterError.InvalidConsentStatus.handle(payment: SweepingPayment, markers: LogstashMarker, logName: String): CreateSweepingPaymentServiceError.InvalidConsentStatus {
        markers.andAppend("error", this)
        logger.warn(markers, logName)

        sweepingPaymentRepository.save(
            payment.copy(
                status = SweepingPaymentStatus.FAILED,
                error = "4101",
                errorDescription = this.message,
            ),
        )

        publisher.sendMessage(CheckSweepingConsentStatusMessage(consentId = payment.consentId.value, forceFetch = true))

        return CreateSweepingPaymentServiceError.InvalidConsentStatus
    }

    private fun CreateSweepingPaymentAdapterError.GenericError.handle(payment: SweepingPayment, markers: LogstashMarker, logName: String) = sweepingAccountAdapter.getPaymentStatus(
        consentId = payment.consentId,
        paymentRequestId = payment.requestId,
    ).map {
        markers.andAppend("payment", it)
        logger.info(markers, logName)
        sweepingPaymentRepository.save(
            it.toSweepingPayment(
                userAccountId = payment.userAccountId,
                creditorId = payment.creditorId,
                riskSignals = payment.riskSignals,
            ),
        ).right()
    }.getOrElse {
        markers.andAppend("error", it)
        when (it) {
            is GetSweepingPaymentStatusError.PaymentNotFound -> {
                logger.error(markers, logName)
                sweepingPaymentRepository.save(
                    payment.copy(
                        status = SweepingPaymentStatus.FAILED,
                        error = "4001",
                        errorDescription = "PaymentNotRequested",
                    ),
                )
                CreateSweepingPaymentServiceError.PaymentNotCreated("").left()
            }
            is GetSweepingPaymentStatusError.GenericError -> {
                logger.error(markers, logName)
                CreateSweepingPaymentServiceError.GenericError(it.message).left()
            }
        }
    }

    fun getPaymentStatus(paymentRequestId: SweepingPaymentRequestId): Either<GetSweepingPaymentStatusError, SweepingPaymentStatusResponse> {
        val logName = "SweepingAccountService#getPaymentStatus"
        val markers = Markers.append("paymentRequestId", paymentRequestId.value)

        val payment = sweepingPaymentRepository.find(paymentRequestId).getOrElse {
            return GetSweepingPaymentStatusError.PaymentNotFound("Payment ${paymentRequestId.value} not found").left()
        }

        logger.info(markers, logName)
        return if (payment.status.final) {
            SweepingPaymentStatusResponse(
                requestId = payment.requestId,
                externalId = payment.externalId,
                status = payment.status,
                error = payment.error?.let {
                    SweepingPaymentStatusError(it, it, it)
                },
                description = payment.description,
                date = payment.date,
                createdAt = payment.createdAt,
                updatedAt = payment.updatedAt,
                consentId = payment.consentId,
                endToEndId = payment.endToEndId,
                amount = payment.amount,
                fee = payment.fee,
                method = payment.method,
                participantId = payment.participantId,
                businessEntityTaxId = payment.businessEntityTaxId,
                userTaxId = payment.userTaxId,
            ).right()
        } else {
            sweepingAccountAdapter.getPaymentStatus(
                consentId = payment.consentId,
                paymentRequestId = paymentRequestId,
            ).also {
                it.map { statusResponse ->
                    sweepingPaymentRepository.save(
                        payment.copy(
                            status = statusResponse.status,
                            externalId = statusResponse.externalId,
                            error = statusResponse.error?.code,
                            errorDescription = statusResponse.error?.detail,
                        ),
                    )
                }
            }
        }
    }

    fun updatePayment(command: UpdatePaymentCommand): Either<SweepingAccountServiceError, SweepingPayment> {
        val logName = "SweepingAccountService#updatePayment"
        val markers = Markers.append("command", command)

        val payment = sweepingPaymentRepository.find(command.paymentRequestId).getOrElse {
            return SweepingAccountServiceError.ItemNotFound("Payment ${command.paymentRequestId.value} not found").left()
        }
        val participantId = ParticipantId(payment.participantId)
        val participant = sweepingParticipantAdapter.getParticipants(participantId)
        markers.andAppend("participantName", participant["name"])
            .andAppend("participant", participant)

        val updatedPayment = if (payment.status != command.status) {
            payment.copy(
                status = command.status,
                error = command.error,
                errorDescription = command.errorDescription,
            ).also { updatedPayment ->
                if (updatedPayment.status == SweepingPaymentStatus.SUCCESS && calcLimitUsage) {
                    val sweepingConsentLimitUsage = sweepingConsentLimitUsageRepository.find(payment.consentId).getOrNull()
                    markers.andAppend("currentLimit", sweepingConsentLimitUsage)

                    sweepingConsentLimitUsage?.let {
                        val updatedLimit = it.add(updatedPayment)
                        markers.andAppend("updatedLimit", updatedLimit)
                        sweepingConsentLimitUsageRepository.save(updatedLimit)
                    }
                }
                sweepingPaymentRepository.save(updatedPayment)
                publisher.sendMessage(
                    SweepingPaymentMessage(
                        requestId = updatedPayment.requestId.value,
                        status = updatedPayment.status,
                        amount = updatedPayment.amount,
                        error = updatedPayment.error,
                        errorDescription = updatedPayment.errorDescription,
                    ),
                )
            }
        } else {
            payment
        }

        logger.info(markers, logName)
        return updatedPayment.right()
    }

    fun revokeConsent(consentId: SweepingConsentId): Either<SweepingAccountServiceError, ConsentStatus> {
        val logName = "SweepingAccountService#revokeConsent"
        val markers = Markers.append("consentId", consentId)
        val consent = sweepingConsentRepository.find(consentId).getOrElse {
            logger.info(markers, logName)
            return SweepingAccountServiceError.ItemNotFound("Consent $consentId not found").left()
        }

        markers.andAppend("consentStatus", consent.status)
            .andAppend("participantName", consent.participant.name)
            .andAppend("accountId", consent.userAccountId.value)

        return when (consent.status) {
            ConsentStatus.REVOKED -> {
                logger.info(markers, logName)
                ConsentStatus.REVOKED.right()
            }

            else -> {
                val consentStatus = sweepingAccountAdapter.revokeConsent(consentId)
                markers.andAppend("statusAfterRevoke", consentStatus)

                val updatedConsent = sweepingConsentRepository.save(consent.copy(status = consentStatus))

                updatedConsent.sendOpenFinanceSweepingAccountStatusMessage()

                logger.info(markers, logName)
                updatedConsent.status.right()
            }
        }
    }

    fun getConsent(consentId: SweepingConsentId, forceFetch: Boolean): Either<SweepingAccountServiceError, SweepingConsent> {
        val logName = "SweepingAccountService#getConsent"
        val markers = Markers.append("consentId", consentId.value)
        return try {
            val sweepingConsent = sweepingConsentRepository.find(consentId).getOrElse {
                return SweepingAccountServiceError.ItemNotFound("Consent $consentId not found").left()
            }
            markers.andAppend("originalSweepingConsent", sweepingConsent)

            if (forceFetch || !sweepingConsent.status.final) {
                val updatedSweepingConsent = sweepingAccountAdapter.getConsent(sweepingConsent.consentId)
                markers.andAppend("updatedSweepingConsent", updatedSweepingConsent)

                val newStatus = if (!updatedSweepingConsent.status.final && getZonedDateTime().isAfter(sweepingConsent.createdAt.plusMinutes(10))) {
                    ConsentStatus.CANCELED
                } else {
                    updatedSweepingConsent.status
                }
                markers.andAppend("newStatus", newStatus)

                if (sweepingConsent.status != newStatus) {
                    sweepingConsent.apply(updatedSweepingConsent, newStatus).also {
                        it.sendOpenFinanceSweepingAccountStatusMessage()
                        logger.info(markers, logName)
                        if (newStatus == ConsentStatus.AUTHORISED) {
                            sweepingConsentLimitUsageRepository.save(buildInitialLimitUsage(sweepingConsent.consentId))
                        }
                        return sweepingConsentRepository.save(it).right()
                    }
                }
            }

            logger.info(markers, logName)
            return sweepingConsent.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            SweepingAccountServiceError.ServerError(e).left()
        }
    }

    fun getConsentPeriodicUsage(consentId: SweepingConsentId): Either<SweepingAccountServiceError, SweepingConsentPeriodicUsage> {
        val logName = "SweepingAccountService#getConsentPeriodicUsage"
        val markers = Markers.append("consentId", consentId.value)
        return try {
            val consent = sweepingConsentRepository.find(consentId).getOrElse {
                return SweepingAccountServiceError.ItemNotFound("Consent $consentId not found").left()
            }
            markers.andAppend("sweepingConsent", consent)

            val limitUsage = sweepingConsentLimitUsageRepository.find(consentId).getOrElse {
                return SweepingAccountServiceError.ItemNotFound("Consent limit usage $consentId not found").left()
            }
            markers.andAppend("sweepingConsentLimitUsage", limitUsage)

            logger.info(markers, logName)
            return SweepingConsentPeriodicUsage(
                consentId = consentId,
                daily = SweepingConsentPeriodicLimitUsage(
                    amountLimit = consent.sweepingLimits.periodicLimits.day.transactionLimit,
                    amountUsed = limitUsage.dailyAmount,
                    quantityLimit = consent.sweepingLimits.periodicLimits.day.quantityLimit,
                    quantityUsed = limitUsage.dailyQuantity,
                ),
                weekly = SweepingConsentPeriodicLimitUsage(
                    amountLimit = consent.sweepingLimits.periodicLimits.week.transactionLimit,
                    amountUsed = limitUsage.weeklyAmount,
                    quantityLimit = consent.sweepingLimits.periodicLimits.week.quantityLimit,
                    quantityUsed = limitUsage.weeklyQuantity,
                ),
                monthly = SweepingConsentPeriodicLimitUsage(
                    amountLimit = consent.sweepingLimits.periodicLimits.month.transactionLimit,
                    amountUsed = limitUsage.monthlyAmount,
                    quantityLimit = consent.sweepingLimits.periodicLimits.month.quantityLimit,
                    quantityUsed = limitUsage.monthlyQuantity,
                ),
                yearly = SweepingConsentPeriodicLimitUsage(
                    amountLimit = consent.sweepingLimits.periodicLimits.year.transactionLimit,
                    amountUsed = limitUsage.yearlyAmount,
                    quantityLimit = consent.sweepingLimits.periodicLimits.year.quantityLimit,
                    quantityUsed = limitUsage.yearlyQuantity,
                ),
                totalLimit = consent.sweepingLimits.totalAllowedAmount,
                totalUsed = limitUsage.totalAmount,
            ).right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            SweepingAccountServiceError.ServerError(e).left()
        }
    }

    internal fun buildInitialLimitUsage(sweepingConsentId: SweepingConsentId): SweepingConsentLimitUsage {
        val daily = LimitUsage.buildFrom(LimitType.DAILY)
        val week = LimitUsage.buildFrom(LimitType.WEEKLY)
        val month = LimitUsage.buildFrom(LimitType.MONTHLY)
        val year = LimitUsage.buildFrom(LimitType.YEARLY)
        return SweepingConsentLimitUsage(
            consentId = sweepingConsentId,
            dailyAmount = daily.amountSuccessful,
            dailyQuantity = 0,
            dailyWindowStart = daily.windowStart,
            dailyWindowEnd = daily.windowEnd,
            weeklyAmount = week.amountSuccessful,
            weeklyQuantity = 0,
            weeklyWindowStart = week.windowStart,
            weeklyWindowEnd = week.windowEnd,
            monthlyAmount = month.amountSuccessful,
            monthlyQuantity = 0,
            monthlyWindowStart = month.windowStart,
            monthlyWindowEnd = month.windowEnd,
            yearlyAmount = year.amountSuccessful,
            yearlyQuantity = 0,
            yearlyWindowStart = year.windowStart,
            yearlyWindowEnd = year.windowEnd,
            totalAmount = 0,
        )
    }

    private fun SweepingConsentLimitUsage.add(payment: SweepingPayment): SweepingConsentLimitUsage {
        val daily = LimitUsage(
            limitType = LimitType.DAILY,
            amountSuccessful = this.dailyAmount,
            quantitySuccessful = this.dailyQuantity,
            windowStart = this.dailyWindowStart,
        ).add(payment)
        val weekly = LimitUsage(
            limitType = LimitType.WEEKLY,
            amountSuccessful = this.weeklyAmount,
            quantitySuccessful = this.weeklyQuantity,
            windowStart = this.weeklyWindowStart,
        ).add(payment)
        val monthly = LimitUsage(
            limitType = LimitType.MONTHLY,
            amountSuccessful = this.monthlyAmount,
            quantitySuccessful = this.monthlyQuantity,
            windowStart = this.monthlyWindowStart,
        ).add(payment)
        val yearly = LimitUsage(
            limitType = LimitType.YEARLY,
            amountSuccessful = this.yearlyAmount,
            quantitySuccessful = this.yearlyQuantity,
            windowStart = this.yearlyWindowStart,
        ).add(payment)
        return copy(
            dailyAmount = daily.amountSuccessful,
            dailyQuantity = daily.quantitySuccessful,
            dailyWindowStart = daily.windowStart,
            dailyWindowEnd = daily.windowEnd,
            weeklyAmount = weekly.amountSuccessful,
            weeklyQuantity = weekly.quantitySuccessful,
            weeklyWindowStart = weekly.windowStart,
            weeklyWindowEnd = weekly.windowEnd,
            monthlyAmount = monthly.amountSuccessful,
            monthlyQuantity = monthly.quantitySuccessful,
            monthlyWindowStart = monthly.windowStart,
            monthlyWindowEnd = monthly.windowEnd,
            yearlyAmount = yearly.amountSuccessful,
            yearlyQuantity = yearly.quantitySuccessful,
            yearlyWindowStart = yearly.windowStart,
            yearlyWindowEnd = yearly.windowEnd,
            totalAmount = totalAmount + payment.amount,
        )
    }

    private fun SweepingConsent.sendOpenFinanceSweepingAccountStatusMessage() = publisher.sendMessage(
        OpenFinanceSweepingConsentStatusMessage(
            sweepingConsentId = consentId.value,
            status = status,
            debtor = debtor?.toSweepingDebtorTO(participant.name),
        ),
    )

    private fun SweepingConsent.apply(response: CreateConsentResponse, newStatus: ConsentStatus) = copy(
        participant = response.participant,
        status = newStatus,
        authUrl = response.authUrl,
        user = response.user,
        businessEntity = response.businessEntity ?: businessEntity,
        startDateTime = response.startDateTime ?: startDateTime,
        expirationDateTime = response.expirationDateTime ?: expirationDateTime,
        additionalInformation = response.additionalInformation ?: additionalInformation,
        statusUpdateDateTime = response.statusUpdateDateTime,
        sweepingLimits = response.sweepingLimits ?: sweepingLimits,
        debtor = response.debtor ?: debtor,
    )

    private fun CreateConsentResponse.toSweepingConsent(userAccountId: UserAccountId, creditors: List<CreditorId>, originalSweepingLimits: SweepingLimits) = SweepingConsent(
        userAccountId = userAccountId,
        consentId = consentId,
        participant = participant,
        status = status,
        authUrl = authUrl,
        user = user,
        businessEntity = businessEntity,
        creditors = creditors,
        startDateTime = startDateTime,
        expirationDateTime = expirationDateTime,
        additionalInformation = additionalInformation,
        statusUpdateDateTime = statusUpdateDateTime,
        createdAt = createdAt,
        updatedAt = updatedAt,
        sweepingLimits = sweepingLimits ?: originalSweepingLimits,
        debtor = debtor,
    )

    private fun CreateCreditorResponse.toSweepingCreditor(userAccountId: UserAccountId) = SweepingCreditor(
        id = id,
        type = type,
        userTaxId = userTaxId,
        creditorTaxId = creditorTaxId,
        name = name,
        ispb = ispb,
        issuer = issuer,
        number = number,
        accountType = accountType,
        participant = participant,
        status = status,
        createdAt = createdAt,
        updatedAt = updatedAt,
        userAccountId = userAccountId,
    )

    private fun SweepingPaymentResponse.toSweepingPayment(userAccountId: UserAccountId, creditorId: CreditorId) = SweepingPayment(
        externalId = externalId,
        userAccountId = userAccountId,
        date = date,
        description = description,
        createdAt = createdAt,
        consentId = consentId,
        updatedAt = updatedAt,
        endToEndId = endToEndId,
        status = status,
        amount = amount,
        fee = fee,
        method = method,
        riskSignals = riskSignals,
        participantId = participantId,
        businessEntityTaxId = businessEntityTaxId,
        userTaxId = userTaxId,
        creditorId = creditorId,
        requestId = requestId,
    )

    private fun SweepingPaymentStatusResponse.toSweepingPayment(userAccountId: UserAccountId, creditorId: CreditorId, riskSignals: SweepingRiskSignals) = SweepingPayment(
        externalId = externalId,
        userAccountId = userAccountId,
        date = date,
        description = description,
        createdAt = createdAt,
        consentId = consentId,
        updatedAt = updatedAt,
        endToEndId = endToEndId,
        status = status,
        amount = amount,
        fee = fee,
        method = method,
        riskSignals = riskSignals,
        participantId = participantId,
        businessEntityTaxId = businessEntityTaxId,
        userTaxId = userTaxId,
        creditorId = creditorId,
        requestId = requestId,
    )
}

data class SweepingConsentLimitUsage(
    val consentId: SweepingConsentId,
    val dailyAmount: Long,
    val dailyQuantity: Long,
    val dailyWindowStart: LocalDate,
    val dailyWindowEnd: LocalDate,
    val weeklyAmount: Long,
    val weeklyQuantity: Long,
    val weeklyWindowStart: LocalDate,
    val weeklyWindowEnd: LocalDate,
    val monthlyAmount: Long,
    val monthlyQuantity: Long,
    val monthlyWindowStart: LocalDate,
    val monthlyWindowEnd: LocalDate,
    val yearlyAmount: Long,
    val yearlyQuantity: Long,
    val yearlyWindowStart: LocalDate,
    val yearlyWindowEnd: LocalDate,
    val totalAmount: Long,
)

data class SweepingConsentPeriodicUsage(
    val consentId: SweepingConsentId,
    val daily: SweepingConsentPeriodicLimitUsage,
    val weekly: SweepingConsentPeriodicLimitUsage,
    val monthly: SweepingConsentPeriodicLimitUsage,
    val yearly: SweepingConsentPeriodicLimitUsage,
    val totalLimit: Long,
    val totalUsed: Long,
)

data class SweepingConsentPeriodicLimitUsage(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
)

data class SweepingConsent(
    val userAccountId: UserAccountId,
    val consentId: SweepingConsentId,
    val participant: SweepingParticipant,
    val status: ConsentStatus,
    val authUrl: String,
    val user: UserConsent,
    val businessEntity: UserConsent? = null,
    val creditors: List<CreditorId>,
    val startDateTime: ZonedDateTime? = null,
    val expirationDateTime: ZonedDateTime? = null,
    val additionalInformation: String?,
    val statusUpdateDateTime: ZonedDateTime,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val sweepingLimits: SweepingLimits,
    val debtor: SweepingDebtor?,
)

data class SweepingParticipant(
    val id: String,
    val name: String,
    val avatar: String? = null,
)

data class UserConsent(
    val taxId: Document,
    val name: String,
)

data class SweepingConsentId(val value: String = UUID.randomUUID().toString())

data class SweepingLimits(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimits,
)

data class PeriodicLimits(
    val day: PeriodicLimit,
    val week: PeriodicLimit,
    val month: PeriodicLimit,
    val year: PeriodicLimit,
) {
    companion object {
        val ZERO = PeriodicLimits(PeriodicLimit(0, 0), PeriodicLimit(0, 0), PeriodicLimit(0, 0), PeriodicLimit(0, 0))
    }
}

data class PeriodicLimit(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class SweepingDebtor(
    val ispb: String,
    val issuer: String,
    val number: String,
    val accountType: String,
) {
    fun toSweepingDebtorTO(participantName: String?): SweepingDebtorTO {
        return SweepingDebtorTO(
            ispb = this.ispb,
            routingNumber = this.issuer,
            accountNumber = this.number,
            accountType = this.accountType,
            bankName = participantName,
        )
    }
}

sealed class SweepingAccountServiceError(open val message: String) : PrintableSealedClass() {
    class ItemNotFound(override val message: String) : SweepingAccountServiceError(message)
    class ServerError(exception: Throwable) : SweepingAccountServiceError(exception.localizedMessage.orEmpty())
}

data class UpdatePaymentCommand(
    val paymentRequestId: SweepingPaymentRequestId,
    val status: SweepingPaymentStatus,
    val error: String?,
    val errorDescription: String?,
)

fun CreateSweepingPaymentServiceError.toErrorCode() = when (this) {
    is CreateSweepingPaymentServiceError.GenericError -> "4001"
    is CreateSweepingPaymentServiceError.PaymentNotCreated -> "4100"
    is CreateSweepingPaymentServiceError.ConsentNotFound -> "4001"
    is CreateSweepingPaymentServiceError.LimitExceededError -> limitType.toLimitTypeErrorCode()
    is CreateSweepingPaymentServiceError.InvalidConsentStatus -> "4101"
}

fun CreateSweepingPaymentServiceError.isWarning() = when (this) {
    is CreateSweepingPaymentServiceError.GenericError -> false
    is CreateSweepingPaymentServiceError.PaymentNotCreated -> false
    is CreateSweepingPaymentServiceError.ConsentNotFound -> false
    is CreateSweepingPaymentServiceError.LimitExceededError -> true
    is CreateSweepingPaymentServiceError.InvalidConsentStatus -> false
}

fun LimitType.toLimitTypeErrorCode() = when (this) {
    LimitType.DAILY -> "4002"
    LimitType.WEEKLY -> "4003"
    LimitType.MONTHLY -> "4004"
    LimitType.YEARLY -> "4005"
    LimitType.GLOBAL -> "4006"
    LimitType.TRANSACTION -> "4007"
    LimitType.UNKNOWN -> "4008"
}

fun LimitType.toLimitTypeErrorDescription() = "LimitExceeded: $this"

sealed class CreateSweepingPaymentServiceError(val message: String) : PrintableSealedClass() {
    data class LimitExceededError(val limitType: LimitType) : CreateSweepingPaymentServiceError("Limit exceeded: $limitType")
    class GenericError(message: String) : CreateSweepingPaymentServiceError(message)
    class PaymentNotCreated(message: String) : CreateSweepingPaymentServiceError(message)
    data class ConsentNotFound(val consentId: SweepingConsentId) : CreateSweepingPaymentServiceError("Consent ${consentId.value} not found")
    data object InvalidConsentStatus : CreateSweepingPaymentServiceError("Invalid consent status")
}

data class LimitUsage(
    val limitType: LimitType,
    var amountSuccessful: Long = 0,
    var quantitySuccessful: Long = 0,
    var windowStart: LocalDate,
) {
    var windowEnd: LocalDate = nextWindowEnd()

    fun add(payment: SweepingPayment): LimitUsage {
        setWindow(payment.createdAt.toLocalDate())

        if (payment.status == SweepingPaymentStatus.SUCCESS) {
            amountSuccessful += payment.amount
            quantitySuccessful += 1
        }

        return this
    }

    private fun setWindow(date: LocalDate) {
        while (windowEnd <= date) {
            windowStart = windowEnd
            windowEnd = nextWindowEnd()
            amountSuccessful = 0
            quantitySuccessful = 0
        }
    }

    private fun nextWindowEnd() = when (limitType) {
        LimitType.GLOBAL -> LocalDate.of(2030, 1, 1)
        LimitType.DAILY -> windowStart.plusDays(1)
        LimitType.WEEKLY -> windowStart.plusWeeks(1)
        LimitType.MONTHLY -> windowStart.plusMonths(1)
        LimitType.YEARLY -> windowStart.plusYears(1)
        LimitType.TRANSACTION, LimitType.UNKNOWN -> throw IllegalStateException("Invalid limitType $limitType for LimitUsage")
    }

    companion object {
        fun buildFrom(limitType: LimitType) = when (limitType) {
            LimitType.DAILY -> LimitUsage(limitType, windowStart = LocalDate.of(2024, Month.SEPTEMBER, 1))
            LimitType.WEEKLY -> LimitUsage(limitType, windowStart = LocalDate.of(2024, Month.SEPTEMBER, 1))
            LimitType.MONTHLY -> LimitUsage(limitType, windowStart = LocalDate.of(2024, Month.SEPTEMBER, 1))
            LimitType.YEARLY -> LimitUsage(limitType, windowStart = LocalDate.of(2024, Month.JANUARY, 1))
            LimitType.GLOBAL -> LimitUsage(limitType, windowStart = LocalDate.of(2024, Month.SEPTEMBER, 1))
            LimitType.TRANSACTION, LimitType.UNKNOWN -> throw IllegalStateException("Invalid limitType $limitType for LimitUsage")
        }
    }
}

data class CheckResult(
    val quantityExceeded: Boolean,
    val amountExceeded: Boolean,
)