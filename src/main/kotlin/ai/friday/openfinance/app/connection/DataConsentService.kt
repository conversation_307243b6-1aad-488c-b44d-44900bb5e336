package ai.friday.openfinance.app.connection

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentLink
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.OpenFinanceDataConsentStatusMessage
import ai.friday.openfinance.app.integrations.CheckDataConsentStatusMessage
import ai.friday.openfinance.app.integrations.DataConsentAdapter
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class DataConsentService(
    private val dataConsentRepository: DataConsentRepository,
    private val dataConsentAdapter: DataConsentAdapter,
    private val publisher: MessagePublisher,
    @Property(name = "integrations.iniciador.link.permissions")
    private val permissions: List<DataConsentPermission>,
) {
    private val logger = LoggerFactory.getLogger(DataConsentService::class.java)

    fun findAllByStatus(status: DataConsentStatus): List<DataConsent> {
        return dataConsentRepository.findAllByStatus(status)
    }

    fun save(connection: DataConsent): DataConsent {
        return dataConsentRepository.save(connection)
    }

    fun requestConsent(userAccountId: UserAccountId, document: Document, participantId: ParticipantId): Either<Exception, DataConsentLink> {
        val logName = "DataConsentService#requestConsent"
        val markers = Markers.append("userAccountId", userAccountId.value)
            .andAppend("document", document.value)
            .andAppend("participantId", participantId.value)

        if (permissions.isEmpty()) {
            return Exception("Permissions list cannot be empty").left()
        }

        val requestId = DataConsentRequestId()
        markers.andAppend("requestId", requestId.value)

        return dataConsentAdapter.requestDataConsent(
            requestId,
            document,
            participantId,
            permissions,
        ).map { link ->
            save(
                DataConsent(
                    userAccountId = userAccountId,
                    id = link.dataConsentId,
                    requestId = requestId,
                    status = DataConsentStatus.PENDING_AUTHORISATION,
                    permissions = permissions,
                    bankId = "",
                    bankName = "",
                    permissionsGranted = listOf(),
                    participantId = participantId,
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
            ).also {
                publisher.sendMessage(CheckDataConsentStatusMessage(consentId = it.id.value))
            }
            link
        }.also { result ->
            result.onLeft {
                logger.error(markers, logName, it)
            }.onRight {
                markers.andAppend("link", it)
                logger.info(markers, logName)
            }
        }
    }

    open fun getConsent(consentId: DataConsentId): Either<Exception, DataConsent> {
        val logName = "DataConsentService#getConsent"
        val markers = Markers.append("consentId", consentId.value)
        return try {
            val dataConsent = dataConsentRepository.find(consentId)
            markers.andAppend("dataConsent", dataConsent)

            if (!dataConsent.status.final) {
                val externalLink = dataConsentAdapter.getLink(dataConsent.id)
                markers.andAppend("externalLink", externalLink)

                val newStatus = if (!externalLink.status.final && getZonedDateTime().isAfter(dataConsent.createdAt.plusMinutes(10))) {
                    DataConsentStatus.UNAVAILABLE
                } else {
                    externalLink.status
                }
                markers.andAppend("newStatus", newStatus)

                if (dataConsent.status != newStatus) {
                    dataConsent.apply(externalLink, newStatus).also {
                        it.sendOpenFinanceDataConsentStatusMessage()
                        logger.info(markers, logName)
                        return dataConsentRepository.save(it).right()
                    }
                }
            }

            logger.info(markers, logName)
            dataConsent.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            e.left()
        }
    }

    private fun DataConsent.apply(response: ExternalLink, newStatus: DataConsentStatus) = copy(
        status = newStatus,
        permissions = response.permissions,
        permissionsGranted = response.permissionsGranted,
        bankId = response.bankId,
        bankName = response.bankName,
    )

    private fun DataConsent.sendOpenFinanceDataConsentStatusMessage() = publisher.sendMessage(
        OpenFinanceDataConsentStatusMessage(
            dataConsentId = id.value,
            userAccountId = userAccountId.value,
            status = status,
        ),
    )

    fun revokeConsent(dataConsentId: DataConsentId): Either<Exception, DataConsentStatus> {
        val logName = "DataConsentService#revokeConsent"
        val markers = Markers.append("dataConsentId", dataConsentId.value)

        return try {
            val dataConsent = dataConsentRepository.find(dataConsentId)
            markers.andAppend("dataConsentStatus", dataConsent.status)

            if (dataConsent.status == DataConsentStatus.REVOKED) {
                return DataConsentStatus.REVOKED.right()
            }

            dataConsentAdapter.revokeDataConsent(dataConsentId).map {
                val updatedConsent = dataConsent.copy(
                    status = DataConsentStatus.REVOKED,
                    updatedAt = getZonedDateTime(),
                )
                dataConsentRepository.save(updatedConsent)
                updatedConsent.sendOpenFinanceDataConsentStatusMessage()
                logger.info(markers, logName)
                updatedConsent.status
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            e.left()
        }
    }
}

data class ExternalLink(
    val dataConsentId: DataConsentId,
    val status: DataConsentStatus,
    val redirectUrl: String,
    val requestConsentId: DataConsentRequestId,
    val bankId: String,
    val bankName: String,
    val permissions: List<DataConsentPermission>,
    val permissionsGranted: List<DataConsentPermission>,
)