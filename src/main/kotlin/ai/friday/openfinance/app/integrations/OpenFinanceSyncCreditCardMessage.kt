package ai.friday.openfinance.app.integrations

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenFinanceSyncCreditCardMessage(
    val eventType: OpenFinanceSyncCreditCardMessageEventType,
    val userAccountId: String,
    val dataConsentId: String,
    val creditCardId: String?,
    val invoiceId: String?,
)

enum class OpenFinanceSyncCreditCardMessageEventType {
    CARD_CREATED,
    CARD_REFRESH,
    INVOICE_CREATED,
    INVOICE_UPDATED,
    INVOICE_REFRESH,
}