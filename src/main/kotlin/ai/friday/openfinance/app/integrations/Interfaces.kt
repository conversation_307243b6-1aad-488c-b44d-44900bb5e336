package ai.friday.openfinance.app.integrations

import ai.friday.openfinance.app.AccountSubType
import ai.friday.openfinance.app.AccountType
import ai.friday.openfinance.app.BankAccountAmount
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.BankAccountMeta
import ai.friday.openfinance.app.BankTransactionMeta
import ai.friday.openfinance.app.CompletedAuthorisedPaymentType
import ai.friday.openfinance.app.CreditDebitType
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentLink
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.OpenFinanceResourceType
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.TransactionPersonType
import ai.friday.openfinance.app.TransactionType
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.connection.ExternalLink
import ai.friday.openfinance.app.creditcard.CreditCard
import ai.friday.openfinance.app.creditcard.CreditCardExternalId
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoice
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceExternalId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardTransaction
import ai.friday.openfinance.app.creditcard.CreditCardTransactionExternalId
import ai.friday.openfinance.app.job.Job
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.CreateConsentRequest
import ai.friday.openfinance.app.sweepingaccount.CreateConsentResponse
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorRequest
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorResponse
import ai.friday.openfinance.app.sweepingaccount.CreateSweepingPaymentRequest
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentLimitUsage
import ai.friday.openfinance.app.sweepingaccount.SweepingCreditor
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentStatusResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignals
import ai.friday.openfinance.app.utils.PrintableSealedClass
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.Duration
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZonedDateTime
import java.util.*
import net.javacrumbs.shedlock.core.SimpleLock

interface BankAccountRepository {
    fun save(bankAccount: BankAccountMeta): BankAccountMeta
    fun find(id: BankAccountId): Either<RepositoryError, BankAccountMeta>
    fun findByConsentId(dataConsentId: DataConsentId): List<BankAccountMeta>
}

interface SweepingCreditorRepository {
    fun save(creditor: SweepingCreditor): SweepingCreditor
    fun find(id: CreditorId): Either<RepositoryError, SweepingCreditor>
}

interface SweepingConsentRepository {
    fun save(consent: SweepingConsent): SweepingConsent
    fun find(id: SweepingConsentId): Either<RepositoryError, SweepingConsent>
}

interface SweepingConsentLimitUsageRepository {
    fun save(limitUsage: SweepingConsentLimitUsage)
    fun find(id: SweepingConsentId): Either<RepositoryError, SweepingConsentLimitUsage>
}

interface SweepingPaymentRepository {
    fun save(payment: SweepingPayment): SweepingPayment
    fun find(paymentExternalId: SweepingPaymentExternalId): Either<RepositoryError, SweepingPayment>
    fun find(paymentRequestId: SweepingPaymentRequestId): Either<RepositoryError, SweepingPayment>
}

interface DataConsentRepository {
    fun save(dataConsent: DataConsent): DataConsent
    fun find(dataConsentId: DataConsentId): DataConsent
    fun findAllByStatus(status: DataConsentStatus): List<DataConsent>
    fun findByUserAccountId(userAccountId: UserAccountId): List<DataConsent>
}

interface BankTransactionRepository {
    fun save(bankTransaction: BankTransactionMeta): BankTransactionMeta
    fun find(transactionId: String, bankAccountId: BankAccountId): Either<RepositoryError, BankTransactionMeta>
    fun findBankAccountTransactions(bankAccountId: BankAccountId): List<BankTransactionMeta>
}

interface CreditCardRepository {
    fun save(creditCard: CreditCard, userAccountId: UserAccountId): CreditCard
    fun find(id: CreditCardId): Either<RepositoryError, CreditCard>
    fun findByConsentId(dataConsentId: DataConsentId): List<CreditCard>
}

interface CreditCardInvoiceRepository {
    fun save(invoice: CreditCardInvoice, dataConsentId: DataConsentId, userAccountId: UserAccountId, creditCardId: CreditCardId): CreditCardInvoice
    fun find(creditCardInvoiceId: CreditCardInvoiceId): Either<RepositoryError, CreditCardInvoice>
    fun findByCreditCardId(creditCardId: CreditCardId): List<CreditCardInvoice>
}

interface CreditCardTransactionRepository {
    fun save(transaction: CreditCardTransaction, userAccountId: UserAccountId, creditCardId: CreditCardId): CreditCardTransaction
    fun find(transactionExternalId: CreditCardTransactionExternalId, creditCardId: CreditCardId): Either<RepositoryError, CreditCardTransaction>
    fun findByCreditCardId(creditCardId: CreditCardId, yearMonth: YearMonth?): List<CreditCardTransaction>
}

interface BankAccountAdapter {
    fun getBankAccounts(dataConsentId: DataConsentId): Either<Exception, List<OFBankAccount>>

    fun getBankAccountData(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountData>

    fun getBankAccountBalance(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountBalance>

    fun getBankAccountLimits(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountLimits>

    fun getBankAccountTransactions(recentTransactions: Boolean, dataConsentId: DataConsentId, bankAccountId: BankAccountId, startDate: LocalDate? = null, endDate: LocalDate = LocalDate.now().minusDays(1)): Either<Exception, List<OFBankAccountTransaction>>
}

interface OpenFinanceCreditCardAdapter {
    fun getCreditCards(dataConsentId: DataConsentId): Either<Exception, List<CreditCard>>

    fun getCreditCardTransactions(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId, startDate: LocalDate, endDate: LocalDate): Either<Exception, List<CreditCardTransaction>>

    fun getCreditCardInvoices(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId): Either<Exception, List<CreditCardInvoice>>

    fun getCreditCardInvoiceTransactions(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId, invoiceExternalId: CreditCardInvoiceExternalId): Either<Exception, List<CreditCardTransaction>>
}

data class OFBankAccount(
    val bankAccountId: BankAccountId,
    val type: AccountType,
    val compeCode: String,
    val branchCode: String,
    val number: String,
    val checkDigit: String,
    val brandName: String,
    val companyCnpj: String,
)

data class OFBankAccountData(
    val bankAccountId: BankAccountId,
    val type: AccountType,
    val compeCode: String,
    val branchCode: String,
    val number: String,
    val checkDigit: String,
    val subtype: AccountSubType,
    val currency: String,
    val raw: String,
)

data class OFBankAccountBalance(
    val availableAmount: BankAccountAmount,
    val blockedAmount: BankAccountAmount,
    val automaticallyInvestedAmount: BankAccountAmount,
    val updateDateTime: ZonedDateTime,
)

data class OFBankAccountLimits(
    val overdraftContractedLimit: BankAccountAmount?,
    val overdraftUsedLimit: BankAccountAmount?,
    val unarrangedOverdraftAmount: BankAccountAmount?,
)

data class OFBankAccountTransaction(
    val completedAuthorisedPaymentType: CompletedAuthorisedPaymentType,
    val partieBranchCode: String? = null,
    val transactionName: String,
    val transactionId: String,
    val partieCnpjCpf: String? = null,
    val type: TransactionType,
    val transactionDateTime: String,
    val partieCompeCode: String? = null,
    val transactionAmount: BankAccountAmount,
    val partiePersonType: TransactionPersonType? = null,
    val creditDebitType: CreditDebitType,
    val partieNumber: String? = null,
    val partieCheckDigit: String? = null,
    val raw: String,
)

interface SweepingAccountAdapter {
    fun createCreditor(request: CreateCreditorRequest): CreateCreditorResponse
    fun requestSweepingConsent(request: CreateConsentRequest): CreateConsentResponse
    fun createPayment(request: CreateSweepingPaymentRequest): Either<CreateSweepingPaymentAdapterError, SweepingPaymentResponse>

    fun getPaymentStatus(consentId: SweepingConsentId, paymentRequestId: SweepingPaymentRequestId): Either<GetSweepingPaymentStatusError, SweepingPaymentStatusResponse>
    fun getConsentStatus(consentId: SweepingConsentId): ConsentStatus
    fun getPayments(authId: String): String
    fun revokeConsent(consentId: SweepingConsentId): ConsentStatus
    fun getConsent(consentId: SweepingConsentId): CreateConsentResponse
}

data class SweepingPaymentRequestId(
    val value: String = UUID.randomUUID().toString(),
)

sealed class CreateSweepingPaymentAdapterError(val message: String) : PrintableSealedClass() {
    data class LimitExceededError(val limitType: LimitType) : CreateSweepingPaymentAdapterError("Limit exceeded: $limitType")
    data object InvalidConsentStatus : CreateSweepingPaymentAdapterError("Invalid consent status")
    class GenericError(exception: Exception) : CreateSweepingPaymentAdapterError(exception.message.orEmpty())
}

sealed class GetSweepingPaymentStatusError(val message: String) : PrintableSealedClass() {
    class GenericError(exception: Exception) : GetSweepingPaymentStatusError(exception.message.orEmpty())
    class PaymentNotFound(message: String) : GetSweepingPaymentStatusError(message)
}

enum class LimitType { DAILY, WEEKLY, MONTHLY, YEARLY, GLOBAL, TRANSACTION, UNKNOWN }

interface SweepingParticipantAdapter {
    fun getParticipants(participantId: ParticipantId): Map<String, Any?>
}

interface DataConsentAdapter {
    fun requestDataConsent(
        externalId: DataConsentRequestId,
        userTaxId: Document,
        participantId: ParticipantId,
        permissions: List<DataConsentPermission>,
    ): Either<Exception, DataConsentLink>

    fun revokeDataConsent(dataConsentId: DataConsentId): Either<Exception, Unit>
    fun getResources(dataConsentId: DataConsentId): String

    /*
    Get the identification data
    get https://data.sandbox.iniciador.com.br/v1/data/links/{id}/data/identifications
     */
    fun getIdentifications(dataConsentId: DataConsentId): String

    /*
    Get the qualification data
    get https://data.sandbox.iniciador.com.br/v1/data/links/{id}/data/qualifications
     */
    fun getQualifications(dataConsentId: DataConsentId): String

    /*
    Get data of relationships with the financial institution and representatives
    get https://data.sandbox.iniciador.com.br/v1/data/links/{id}/data/financial-relations
     */
    fun getFinancialRelations(dataConsentId: DataConsentId): String

    fun getLink(dataConsentId: DataConsentId): ExternalLink
}

interface JobRepository {
    fun findAll(): List<Job>
    fun save(job: Job)
}

interface InternalLock {
    fun waitForAcquireLock(lockName: String): SimpleLock

    fun acquireLock(
        lockName: String,
        minDuration: Duration? = null,
        maxDuration: Duration? = null,
        simultaneousLock: Int = 1,
    ): SimpleLock?
}

interface LockReleaser {
    fun release(lockName: String): Result<Unit>
}

interface MessagePublisher {
    fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int? = null,
    )

    fun sendMessage(message: OpenFinanceMessage)
    fun sendMessage(message: CheckSweepingConsentStatusMessage)
    fun sendMessage(message: CheckDataConsentStatusMessage)
    fun sendMessage(message: OpenFinanceSyncCreditCardMessage)
}

interface OpenFinanceMessage {
    val resourceType: OpenFinanceResourceType
}

interface KmsService {
    suspend fun encryptData(text: String): String
    suspend fun decryptData(encryptedDataVal: String): String
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SyncBankAccountEventTO(
    val consentId: String,
    val userAccountId: String,
    val bankAccountId: String,
    val recentTransactions: Boolean,
)

data class SweepingPaymentMessage(
    val requestId: String,
    val status: SweepingPaymentStatus,
    val amount: Long,
    val error: String? = null,
    val errorDescription: String? = null,
) : OpenFinanceMessage {
    override val resourceType = OpenFinanceResourceType.SWEEPING_PAYMENT
}

data class CheckSweepingConsentStatusMessage(
    val consentId: String,
    val forceFetch: Boolean = false,
)

data class CheckDataConsentStatusMessage(
    val consentId: String,
)

data class SweepingPayment(
    val requestId: SweepingPaymentRequestId,
    val externalId: SweepingPaymentExternalId? = null,
    val userAccountId: UserAccountId,
    val consentId: SweepingConsentId,
    val date: LocalDate? = null,
    val description: String,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime? = null,
    val endToEndId: String? = null,
    val status: SweepingPaymentStatus,
    val amount: Long,
    val fee: Long? = null,
    val method: String? = null,
    val riskSignals: SweepingRiskSignals,
    val participantId: String,
    val businessEntityTaxId: String? = null,
    val userTaxId: String? = null,
    val creditorId: CreditorId,
    val error: String? = null,
    val errorDescription: String? = null,
)

data class SweepingPaymentExternalId(val value: String)

enum class SweepingPaymentStatus(val final: Boolean) {
    CREATED(false), PROCESSING(false), UNKNOWN(false), FAILED(true), SUCCESS(true),
}