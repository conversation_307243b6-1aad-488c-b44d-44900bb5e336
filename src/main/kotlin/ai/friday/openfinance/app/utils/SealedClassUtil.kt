// ktlint-disable filename
package ai.friday.openfinance.app.utils

import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
abstract class PrintableSealedClass {
    override fun toString(): String {
        return this::class.simpleName ?: super.toString()
    }
}

sealed class RepositoryError(open val message: String) : PrintableSealedClass() {
    class ItemNotFound(override val message: String) : RepositoryError(message)
}