package ai.friday.openfinance.app.utils

import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.math.roundToLong
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers

val openFinanceDateTimeFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss[.SSS][.SS][.S]X")

object OpenFinanceDateTimeSupplier {

    fun tryParseZonedDateTime(date: String): Either<Exception, ZonedDateTime> {
        return try {
            ZonedDateTime.parse(date, dateTimeFormat).right()
        } catch (_: Exception) {
            try {
                ZonedDateTime.parse(date, openFinanceDateTimeFormat).right()
            } catch (e: Exception) {
                e.left()
            }
        }
    }
}

fun LogstashMarker.andAppend(fieldName: String, `object`: Any?): LogstashMarker {
    return and(Markers.append(fieldName, `object`))
}

fun convertToLong(value: String): Long {
    val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
    nf.applyPattern("#.##")
    return (nf.parse(value).toDouble() * 100.0).roundToLong()
}