package ai.friday.openfinance.adapters.jobs

import ai.friday.openfinance.app.bankaccount.BankAccountService
import ai.friday.openfinance.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
@Requires(notEnv = ["test"])
open class SyncBankAccountTransactionsJob(
    private val bankAccountService: BankAccountService,
    @Property(name = "schedules.syncBankAccountTransactions.cron") cron: String,
) : AbstractJob(
    cron = cron,
) {
    override fun execute() {
        try {
            bankAccountService.syncBankTransactionsCurrent()
            LOG.info("SyncBankAccountTransactionsJob")
        } catch (e: Exception) {
            LOG.error("SyncBankAccountTransactionsJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SyncBankAccountTransactionsJob::class.java)
    }
}