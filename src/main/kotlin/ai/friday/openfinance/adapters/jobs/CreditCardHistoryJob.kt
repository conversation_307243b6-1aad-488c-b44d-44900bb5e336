package ai.friday.openfinance.adapters.jobs

import ai.friday.morning.log.andAppend
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessage
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessageEventType
import ai.friday.openfinance.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requires(notEnv = ["test"])
open class CreditCardHistoryJob(
    private val dataConsentService: DataConsentService,
    private val publisher: MessagePublisher,
    @Property(name = "schedules.refreshCreditCardHistory.cron") cron: String,

) : AbstractJob(
    cron = cron,
) {
    private val logger = LoggerFactory.getLogger(CreditCardHistoryJob::class.java)

    override fun execute() {
        val logName = "CreditCardHistoryJob#processCreditCardHistory"
        logger.info(append("context", "Iniciando processamento de historico de cartão de crédito"), logName)
        val activeDataConsent = dataConsentService.findAllByStatus(DataConsentStatus.AVAILABLE)
        activeDataConsent.forEach { dataConsent ->
            try {
                publisher.sendMessage(
                    OpenFinanceSyncCreditCardMessage(
                        eventType = OpenFinanceSyncCreditCardMessageEventType.CARD_REFRESH,
                        userAccountId = dataConsent.userAccountId.value,
                        dataConsentId = dataConsent.id.value,
                        creditCardId = null,
                        invoiceId = null,
                    ),
                )
            } catch (e: Exception) {
                logger.error(append("context", "erro no processamento de historico de cartão de crédito").andAppend("dataConsentId", dataConsent.id.value), logName, e)
            }
        }
    }
}