package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.CheckDataConsentStatusMessage
import ai.friday.openfinance.app.utils.andAppend
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class CheckDataConsentStatusHandler(
    private val dataConsentService: DataConsentService,
) : MessageHandler {

    override val configurationName = "check-data-consent"

    @Trace
    @NewSpan("CheckDataConsentStatusHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "CheckDataConsentStatusHandler#handleMessage"
        val event = parseObjectFrom<CheckDataConsentStatusMessage>(message.body())
        val markers = Markers.append("consentId", event.consentId)

        return dataConsentService.getConsent(DataConsentId(event.consentId)).fold(
            ifLeft = {
                markers.andAppend("error", it)
                logger.error(markers, logName)
                MessageHandlerResponse.keep()
            },
            ifRight = {
                markers.andAppend("status", it.status.name)
                logger.info(markers, logName)
                MessageHandlerResponse.build(shouldDeleteMessage = it.status.final)
            },
        )
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CheckDataConsentStatusHandler::class.java)
    }
}