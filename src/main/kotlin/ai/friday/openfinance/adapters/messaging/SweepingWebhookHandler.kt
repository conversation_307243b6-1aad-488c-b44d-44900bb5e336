package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.api.SweepingWebhookTO
import ai.friday.openfinance.adapters.iniciador.sweeping.IniciadorPaymentStatus
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.integrations.SweepingParticipantAdapter
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountServiceError
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.UpdatePaymentCommand
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.getOrElse
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class SweepingWebhookHandler(
    private val sweepingAccountService: SweepingAccountService,
    private val sweepingParticipantAdapter: SweepingParticipantAdapter,
) : MessageHandler {

    override val configurationName = "sweeping-webhook"

    @Trace
    @NewSpan("SweepingWebhookHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "SweepingWebhookHandler#handleMessage"
        val event = parseObjectFrom<SweepingWebhookTO>(message.body())
        val markers = Markers.append("event", event)

        return when {
            event.authorizationId != null -> processConsent(event, markers, logName)
            else -> processPayment(event, markers, logName)
        }
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }

    private fun processConsent(
        request: SweepingWebhookTO,
        markers: LogstashMarker,
        logName: String,
    ): MessageHandlerResponse {
        markers.andAppend("consentId", request.authorizationId)
        return sweepingAccountService.getConsent(SweepingConsentId(request.authorizationId!!), forceFetch = true).map {
            markers.andAppend("consent", it)
            logger.info(markers, logName)
            MessageHandlerResponse.delete()
        }.getOrElse {
            markers.andAppend("error", it)
            if (it.isWarning()) {
                logger.warn(markers, logName)
            } else {
                logger.error(markers, logName)
            }
            MessageHandlerResponse.keep()
        }
    }

    private fun processPayment(
        request: SweepingWebhookTO,
        markers: LogstashMarker,
        logName: String,
    ): MessageHandlerResponse {
        return sweepingAccountService.updatePayment(request.toUpdatePaymentCommand()).map {
            val participantId = ParticipantId(it.participantId)
            val participant = sweepingParticipantAdapter.getParticipants(participantId)
            markers.andAppend("payment", it)
                .andAppend("participantName", participant["name"])
                .andAppend("participant", participant)
            logger.info(markers, logName)
            MessageHandlerResponse.delete()
        }.getOrElse {
            markers.andAppend("error", it)
            if (it.isWarning()) {
                logger.warn(markers, logName)
            } else {
                logger.error(markers, logName)
            }
            MessageHandlerResponse.keep()
        }
    }

    private fun SweepingAccountServiceError.isWarning() = when (this) {
        is SweepingAccountServiceError.ItemNotFound -> true
        is SweepingAccountServiceError.ServerError -> false
    }

    private fun SweepingWebhookTO.toUpdatePaymentCommand() = UpdatePaymentCommand(
        paymentRequestId = SweepingPaymentRequestId(externalId!!),
        status = IniciadorPaymentStatus.valueOf(status).sweepingPaymentStatus,
        error = error?.code,
        errorDescription = error?.detail,
    )

    companion object {
        private val logger = LoggerFactory.getLogger(SweepingWebhookHandler::class.java)
    }
}