package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.BankAccountService
import ai.friday.openfinance.app.integrations.SyncBankAccountEventTO
import ai.friday.openfinance.app.utils.andAppend
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class SyncBankAccountTransactionsHandler(
    private val bankAccountService: BankAccountService,
    @Property(name = "features.syncBankAccountTransactions") private val syncBankAccountTransactions: Boolean,
) : MessageHandler {

    override val configurationName = "sync-bank-account-transactions"

    @Trace
    @NewSpan("SyncBankAccountHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val messageBody = message.body()
        val markers = append("messageBody", messageBody)
            .andAppend("syncBankAccountTransactions", syncBankAccountTransactions)

        if (syncBankAccountTransactions) {
            val event = parseObjectFrom<SyncBankAccountEventTO>(messageBody)
            val dataConsentId = DataConsentId(event.consentId)
            val bankAccountId = BankAccountId(event.bankAccountId)
            val accountId = UserAccountId(event.userAccountId)

            bankAccountService.syncBankTransactions(event.recentTransactions, accountId, dataConsentId, bankAccountId)
        }

        logger.info(markers, "SyncBankAccountHandler")
        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SyncBankAccountTransactionsHandler::class.java)
    }
}