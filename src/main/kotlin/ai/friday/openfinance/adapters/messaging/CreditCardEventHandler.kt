package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardService
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessage
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessageEventType
import ai.friday.openfinance.app.utils.andAppend
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class CreditCardEventHandler(
    private val creditCardService: CreditCardService,
    private val dataConsentRepository: DataConsentRepository,
) : MessageHandler {

    override val configurationName = "sync-credit-card"

    private val logger = LoggerFactory.getLogger(CreditCardEventHandler::class.java)

    @Trace
    @NewSpan("CreditCardEventHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "CreditCardEventHandler#handleMessage"
        val event = parseObjectFrom<OpenFinanceSyncCreditCardMessage>(message.body())
        val markers = Markers.append("consentId", event.dataConsentId)
            .andAppend("creditCardId", event.creditCardId)
            .andAppend("type", event.eventType)

        val dataConsent = dataConsentRepository.find(DataConsentId(event.dataConsentId))

        val result = when (event.eventType) {
            OpenFinanceSyncCreditCardMessageEventType.CARD_REFRESH -> {
                creditCardService.refreshCreditCards(dataConsent)
            }

            OpenFinanceSyncCreditCardMessageEventType.CARD_CREATED, OpenFinanceSyncCreditCardMessageEventType.INVOICE_REFRESH -> {
                if (event.creditCardId == null) {
                    markers.andAppend("error", "creditCardId is required for CARD_CREATED event")
                    logger.error(markers, logName)
                    return MessageHandlerResponse.keep()
                }
                creditCardService.retrieveAndStoreInvoices(dataConsent, CreditCardId(event.creditCardId))

                creditCardService.retrieveAndStoreTransactions(
                    dataConsent,
                    CreditCardId(event.creditCardId),
                    getLocalDate().minusMonths(1).withDayOfMonth(1),
                    getLocalDate(),
                )
            }

            OpenFinanceSyncCreditCardMessageEventType.INVOICE_CREATED, OpenFinanceSyncCreditCardMessageEventType.INVOICE_UPDATED -> {
                if (event.creditCardId == null || event.invoiceId == null) {
                    markers.andAppend("context", "creditCardId and invoiceId are required for CARD_CREATED event. Mensagem descartada da fila, cartão não será sincronizado").andAppend("ACTION", "VERIFY")
                    logger.error(markers, logName)
                    return MessageHandlerResponse.delete()
                }

                creditCardService.retrieveAndStoreInvoicesTransactions(
                    dataConsent,
                    UserAccountId(event.userAccountId),
                    CreditCardId(event.creditCardId),
                    CreditCardInvoiceId(event.invoiceId),
                )
            }
        }

        return result.fold(
            ifLeft = {
                markers.andAppend("error", it)
                logger.error(markers, logName)
                MessageHandlerResponse.keep()
            },
            ifRight = {
                logger.info(markers, logName)
                MessageHandlerResponse.delete()
            },
        )
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }
}