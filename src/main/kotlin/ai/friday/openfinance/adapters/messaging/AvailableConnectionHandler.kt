package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OpenFinanceDataConsentStatus
import ai.friday.openfinance.app.bankaccount.BankAccountService
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.creditcard.CreditCardService
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class AvailableConnectionHandler(
    private val dataConsentService: DataConsentService,
    private val bankAccountService: BankAccountService,
    private val creditCardService: CreditCardService,
) : MessageHandler {

    override val configurationName = "available-connection"

    @Trace
    @NewSpan("AvailableConnectionHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "AvailableConnectionHandler#handleMessage"
        val event = parseObjectFrom<AvailableConnectionEventTO>(message.body())
        val markers = Markers.append("consentId", event.consentId).andAppend("status", event.status)
        logger.info(markers, logName)

        val dataConsent = dataConsentService.getConsent(DataConsentId(event.consentId)).getOrElse {
            markers.andAppend("linkStatus not found", it)
            logger.error(markers, logName)
            return MessageHandlerResponse.delete()
        }

        bankAccountService.processAvailableConnection(dataConsent).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return MessageHandlerResponse.keep()
        }

        creditCardService.processAvailableConnection(dataConsent).getOrElse {
            markers.andAppend("error", it)
            logger.warn(markers, logName) // se não pegar cartão de credito, tudo bem
        }

        logger.info(markers, "AvailableConnectionHandler#handleMessage")
        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AvailableConnectionHandler::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class AvailableConnectionEventTO(
    val consentId: String,
    val status: OpenFinanceDataConsentStatus,
)