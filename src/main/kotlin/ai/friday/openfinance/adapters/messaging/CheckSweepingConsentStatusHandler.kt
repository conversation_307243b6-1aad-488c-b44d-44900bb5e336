package ai.friday.openfinance.adapters.messaging

import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.integrations.CheckSweepingConsentStatusMessage
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.utils.andAppend
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class CheckSweepingConsentStatusHandler(
    private val sweepingAccountService: SweepingAccountService,
) : MessageHandler {

    override val configurationName = "check-sweeping-consent"

    @Trace
    @NewSpan("CheckSweepingConsentStatusHandler#handleMessage")
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "CheckSweepingConsentStatusHandler#handleMessage"
        val event = parseObjectFrom<CheckSweepingConsentStatusMessage>(message.body())
        val markers = Markers.append("consentId", event.consentId)

        return sweepingAccountService.getConsent(SweepingConsentId(event.consentId), event.forceFetch).fold(
            ifLeft = {
                markers.andAppend("error", it)
                logger.error(markers, logName)
                MessageHandlerResponse.keep()
            },
            ifRight = {
                markers.andAppend("status", it.status.name)
                logger.info(markers, logName)
                MessageHandlerResponse.build(shouldDeleteMessage = it.status.final)
            },
        )
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CheckSweepingConsentStatusHandler::class.java)
    }
}