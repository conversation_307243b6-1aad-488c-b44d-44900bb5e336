package ai.friday.openfinance.adapters.api

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.api.StandardHttpResponses.badRequest
import ai.friday.openfinance.adapters.api.StandardHttpResponses.customStatusResponse
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.bankaccount.BankAccountService
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.GetSweepingPaymentStatusError
import ai.friday.openfinance.app.integrations.OFBankAccountBalance
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.isValidCnpj
import ai.friday.openfinance.app.isValidCpf
import ai.friday.openfinance.app.sweepingaccount.AutomaticSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorRequest
import ai.friday.openfinance.app.sweepingaccount.CreateSweepingPaymentRequest
import ai.friday.openfinance.app.sweepingaccount.CreditorAccountType
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.CreditorType
import ai.friday.openfinance.app.sweepingaccount.ManualSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimit
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimits
import ai.friday.openfinance.app.sweepingaccount.ScreenDimensions
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentPeriodicLimitUsage
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentPeriodicUsage
import ai.friday.openfinance.app.sweepingaccount.SweepingLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentStatusResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignalsType
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import ai.friday.openfinance.app.sweepingaccount.isWarning
import ai.friday.openfinance.app.sweepingaccount.toErrorCode
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.time.Duration
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/openFinance")
class OpenFinanceController(
    private val dataConsentService: DataConsentService,
    private val sweepingAccountService: SweepingAccountService,
    private val bankAccountService: BankAccountService,
) {

    @Post("/data/consent")
    fun createDataConsent(
        @Body request: CreateDataConsentRequestTO,
    ): HttpResponse<*> {
        val logName = "OpenFinanceController#createDataConsent"
        val markers = Markers.append("request", request)

        if (!request.document.isValidCpf() && !request.document.isValidCnpj()) {
            logger.error(markers.andAppend("document", request.document), logName)
            return badRequest(ResponseTO("4001", message = "Cpf ou Cnpj inválido"))
        }

        val connectionLink = dataConsentService.requestConsent(
            userAccountId = UserAccountId(value = request.userAccountId),
            document = Document(request.document),
            participantId = ParticipantId(request.participantId),
        )

        return connectionLink.fold(
            ifLeft = { error ->
                logger.error(markers, logName, error)
                customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("4001", message = "Erro ao criar link. Tente novamente mais tarde."),
                )
            },
            ifRight = {
                logger.info(markers, logName)
                HttpResponse.ok(
                    CreateDataConsentResponseTO(
                        dataConsentId = it.dataConsentId.value,
                        link = it.link,
                    ),
                )
            },
        )
    }

    @Get("/data/consent/{dataConsentId}/bankAccount/{bankAccountId}/balance")
    fun getBankAccountBalance(
        @PathVariable dataConsentId: String,
        @PathVariable bankAccountId: String,
    ): HttpResponse<*> {
        val logName = "OpenFinanceController#getBankAccountBalance"
        val markers = Markers.append("dataConsentId", dataConsentId)
            .andAppend("bankAccountId", bankAccountId)

        return bankAccountService.getBankAccountBalance(
            dataConsentId = DataConsentId(value = dataConsentId),
            bankAccountId = BankAccountId(value = bankAccountId),
        ).map {
            markers.andAppend("balance", it)
            logger.info(markers, logName)
            HttpResponse.ok(it.toBankAccountBalanceTO())
        }.getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            HttpResponse.serverError(it)
        }
    }

    @Post("/sweeping/consent")
    fun createSweepingConsent(@Body request: CreateCreditorConsentTO): HttpResponse<*> {
        val logName = "OpenFinanceController#createSweepingConsent"
        val markers = Markers.append("request", request)
        val userDocument = request.user.taxId
        if (!userDocument.isValidCpf() && !userDocument.isValidCnpj()) {
            logger.error(markers.andAppend("document", userDocument), logName)
            return badRequest(ResponseTO("4001", message = "Cpf ou Cnpj inválido"))
        }

        try {
            val result = sweepingAccountService.requestConsent(
                userAccountId = UserAccountId(request.userAccountId),
                createCreditorRequest = request.toCreateCreditorRequest(),
                user = request.user.toUserConsent(),
                participantId = ParticipantId(request.participantId),
                sweepingLimits = request.sweepingLimits.toSweepingLimits(),
            )
            return result.fold(
                ifLeft = { error ->
                    logger.error(markers, logName, error)
                    customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO("4001", message = "Erro ao criar link. Tente novamente mais tarde."),
                    )
                },
                ifRight = {
                    markers.andAppend("consent", it)
                    logger.info(markers, logName)

                    val responseTO = SweepingAccountConsentResponseTO(
                        sweepingAccountId = it.consentId.value,
                        creditorId = it.creditors.first().value,
                        consentUrl = it.authUrl,
                        startDateTime = it.startDateTime?.format(DateTimeFormatter.ISO_ZONED_DATE_TIME),
                        expirationDateTime = it.expirationDateTime?.format(DateTimeFormatter.ISO_ZONED_DATE_TIME),
                        configuration = it.sweepingLimits.toTO(),
                    )
                    HttpResponse.ok(responseTO)
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao criar link. Tente novamente mais tarde."),
            )
        }
    }

    @Get("/sweeping/consent/{consentId}")
    fun getSweepingConsent(consentId: String): HttpResponse<*> {
        val logName = "OpenFinanceController#getSweepingConsent"
        val markers = Markers.append("consentId", consentId)

        return try {
            sweepingAccountService.getConsent(SweepingConsentId(consentId), forceFetch = false).map {
                markers.andAppend("consent", it)
                logger.info(markers, logName)
                HttpResponse.ok(it)
            }.getOrElse { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)
                customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("4001", message = "Erro ao buscar status do consentimento. Tente novamente mais tarde."),
                )
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao buscar status do consentimento. Tente novamente mais tarde."),
            )
        }
    }

    @Get("/sweeping/consent/{consentId}/limitUsage")
    fun getSweepingConsentLimitUsage(consentId: String): HttpResponse<*> {
        val logName = "OpenFinanceController#getSweepingConsentLimitUsage"
        val markers = Markers.append("consentId", consentId)

        return try {
            sweepingAccountService.getConsentPeriodicUsage(SweepingConsentId(consentId)).map {
                markers.andAppend("consent", it)
                logger.info(markers, logName)
                HttpResponse.ok(it.toSweepingConsentPeriodicUsageTO())
            }.getOrElse { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)
                customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("4001", message = "Erro ao buscar limite do consentimento. Tente novamente mais tarde."),
                )
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao buscar limite do consentimento. Tente novamente mais tarde."),
            )
        }
    }

    @Delete("/sweeping/consent/{consentId}")
    fun revokeSweepingConsent(consentId: String): HttpResponse<*> {
        val logName = "OpenFinanceController#revokeSweepingConsent"
        val markers = Markers.append("consentId", consentId)
        try {
            val result = sweepingAccountService.revokeConsent(SweepingConsentId(consentId))
            return result.fold(
                ifLeft = { error ->
                    markers.andAppend("error", error)
                    logger.error(markers, logName)
                    customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO("4001", message = "Erro ao revogar consentimento. Tente novamente mais tarde."),
                    )
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.ok()
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao revogar consentimento. Tente novamente mais tarde."),
            )
        }
    }

    @Delete("/data/consent/{dataConsentId}")
    fun revokeDataConsent(@PathVariable dataConsentId: String): HttpResponse<*> {
        val logName = "OpenFinanceController#revokeDataConsent"
        val markers = Markers.append("dataConsentId", dataConsentId)
        try {
            val result = dataConsentService.revokeConsent(DataConsentId(dataConsentId))
            return result.fold(
                ifLeft = { error ->
                    logger.error(markers, logName, error)
                    customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO("4001", message = "Erro ao revogar consentimento. Tente novamente mais tarde."),
                    )
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.ok()
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao revogar consentimento. Tente novamente mais tarde."),
            )
        }
    }

    @Post("/sweeping/payment")
    fun createSweepingPayment(
        @Body request: CreatePaymentRequestTO,
    ): HttpResponse<*> {
        val logName = "OpenFinanceController#createSweepingPayment"
        val markers = Markers.append("request", request)

        try {
            val result = sweepingAccountService.createPayment(request.toDomain())

            return result.map {
                logger.info(markers, logName)
                HttpResponse.ok(it.toCreatePaymentResponseTO())
            }.getOrElse { error ->
                val errorCode = error.toErrorCode()
                markers.andAppend("errorCode", errorCode)
                    .andAppend("error", error)
                if (error.isWarning()) {
                    logger.warn(markers, logName)
                } else {
                    logger.error(markers, logName)
                }

                customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO(errorCode, message = "Erro ao criar pagamento. Tente novamente mais tarde."),
                )
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao criar pagamento. Tente novamente mais tarde."),
            )
        }
    }

    @Get("/sweeping/payment/{paymentRequestId}")
    fun getSweepingPaymentStatus(
        @PathVariable paymentRequestId: String,
    ): HttpResponse<*> {
        val logName = "OpenFinanceController#getSweepingPaymentStatus"
        val markers = Markers.append("paymentRequestId", paymentRequestId)

        try {
            val result = sweepingAccountService.getPaymentStatus(SweepingPaymentRequestId(paymentRequestId))
            return result.map {
                logger.info(markers, logName)
                HttpResponse.ok(it.toSweepingPaymentStatusResponseTO())
            }.getOrElse { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)

                when (error) {
                    is GetSweepingPaymentStatusError.GenericError -> customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO("4001", message = "$error: ${error.message}"),
                    )

                    is GetSweepingPaymentStatusError.PaymentNotFound -> HttpResponse.ok(
                        SweepingPaymentStatusResponseTO(
                            status = SweepingPaymentStatus.FAILED,
                            error = error.toString(),
                            errorDescription = error.message,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro consultando pagamento. Tente novamente mais tarde."),
            )
        }
    }

    private fun OFBankAccountBalance.toBankAccountBalanceTO() = BankAccountBalanceTO(
        availableAmount = availableAmount.amountInCents,
        blockedAmount = blockedAmount.amountInCents,
        automaticallyInvestedAmount = automaticallyInvestedAmount.amountInCents,
        updateDateTime = updateDateTime.format(dateTimeFormat),
    )

    companion object {
        private val logger = LoggerFactory.getLogger(OpenFinanceController::class.java)
    }
}

fun CreatePaymentRequestTO.toDomain() = CreateSweepingPaymentRequest(
    id = SweepingPaymentRequestId(requestId),
    consentId = SweepingConsentId(consentId),
    creditorId = CreditorId(creditorId),
    amount = amount,
    description = description,
    riskSignals = riskSignals.toDomain(),
)

private fun CreatePaymentRiskSignalsTO?.toDomain(): SweepingRiskSignals {
    return when (this) {
        is AutomaticPaymentRiskSignalsTO -> AutomaticSweepingRiskSignals(
            lastLoginDateTime = ZonedDateTime.parse(lastLoginDateTime, dateTimeFormat),
            pixKeyRegistrationDateTime = pixKeyRegistrationDateTime?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        )
        is ManualPaymentRiskSignalsTO -> ManualSweepingRiskSignals(
            deviceId = deviceId,
            isRootedDevice = isRootedDevice,
            screenBrightness = screenBrightness,
            elapsedTimeSinceBoot = elapsedTimeSinceBoot?.let { Duration.ofMillis(it) },
            osVersion = osVersion,
            userTimeZoneOffset = userTimeZoneOffset,
            language = language,
            screenDimensions = screenDimensions?.toDomain(),
            accountTenure = null,
        )
        null -> AutomaticSweepingRiskSignals(
            lastLoginDateTime = getZonedDateTime().minusMinutes(57),
            pixKeyRegistrationDateTime = ZonedDateTime.of(2024, 10, 7, 14, 54, 0, 0, brazilTimeZone),
        )
    }
}

private fun ScreenDimensionsTO.toDomain() = ScreenDimensions(
    width = width,
    height = height,
)

private fun UserConsentTO.toUserConsent(): UserConsent {
    return UserConsent(
        taxId = Document(taxId),
        name = name,
    )
}

private fun CreateCreditorConsentTO.toCreateCreditorRequest() = CreateCreditorRequest(
    type = creditorConsentTO.type,
    userTaxId = Document(user.taxId),
    creditorTaxId = Document(user.taxId),
    name = creditorConsentTO.name,
    ispb = creditorConsentTO.ispb,
    issuer = creditorConsentTO.issuer,
    number = creditorConsentTO.number,
    accountType = creditorConsentTO.accountType,
)

data class CreateDataConsentRequestTO(
    val userAccountId: String,
    val document: String,
    val participantId: String,
)

data class CreateDataConsentResponseTO(
    val dataConsentId: String,
    val link: String,
)

data class SweepingAccountConsentResponseTO(
    val sweepingAccountId: String,
    val creditorId: String,
    val consentUrl: String,
    val expirationDateTime: String?,
    val startDateTime: String?,
    val configuration: SweepingLimitsTO? = null,
)

data class SweepingLimitsTO(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimitsTO,
)

data class PeriodicLimitsTO(
    val day: PeriodicLimitTO,
    val week: PeriodicLimitTO,
    val month: PeriodicLimitTO,
    val year: PeriodicLimitTO,
)

data class PeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class CreateCreditorConsentTO(
    val userAccountId: String,
    val user: UserConsentTO,
    val businessEntity: UserConsentTO? = null,
    val participantId: String,
    val creditorConsentTO: SweepingCreditorRequestTO,
    val sweepingLimits: SweepingLimitsTO,
)

data class SweepingConsentConfigurationRequestTO(
    val transactionLimit: Long,
    val dayTransactionLimit: Long,
    val weekTransactionLimit: Long,
    val monthTransactionLimit: Long,
    val yearTransactionLimit: Long,
    val dayTransactionQuantity: Long,
)

data class SweepingCreditorRequestTO(
    val type: CreditorType,
    val name: String? = null,
    val ispb: String? = null,
    val issuer: String? = null,
    val number: String? = null,
    val accountType: CreditorAccountType? = null,
)

data class UserConsentTO(
    val taxId: String,
    val name: String,
)

data class CreatePaymentRequestTO(
    val requestId: String,
    val consentId: String,
    val creditorId: String,
    val amount: Long,
    val description: String,
    val riskSignals: CreatePaymentRiskSignalsTO? = null,
)

data class CreatePaymentResponseTO(
    val requestId: String,
    val endToEnd: String?,
)

fun SweepingLimitsTO.toSweepingLimits(): SweepingLimits {
    return SweepingLimits(
        totalAllowedAmount = this.totalAllowedAmount,
        transactionLimit = this.transactionLimit,
        periodicLimits = this.periodicLimits.toPeriodicLimits(),
    )
}

fun PeriodicLimitsTO.toPeriodicLimits(): PeriodicLimits {
    return PeriodicLimits(
        day = this.day.toPeriodicLimit(),
        week = this.week.toPeriodicLimit(),
        month = this.month.toPeriodicLimit(),
        year = this.year.toPeriodicLimit(),
    )
}

fun PeriodicLimitTO.toPeriodicLimit(): PeriodicLimit {
    return PeriodicLimit(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

fun SweepingLimits.toTO(): SweepingLimitsTO {
    return SweepingLimitsTO(
        totalAllowedAmount = this.totalAllowedAmount,
        transactionLimit = this.transactionLimit,
        periodicLimits = this.periodicLimits.toTO(),
    )
}

fun PeriodicLimits.toTO(): PeriodicLimitsTO {
    return PeriodicLimitsTO(
        day = this.day.toTO(),
        week = this.week.toTO(),
        month = this.month.toTO(),
        year = this.year.toTO(),
    )
}

fun PeriodicLimit.toTO(): PeriodicLimitTO {
    return PeriodicLimitTO(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

fun SweepingPayment.toCreatePaymentResponseTO() = CreatePaymentResponseTO(
    requestId = requestId.value,
    endToEnd = endToEndId,
)

data class SweepingPaymentStatusResponseTO(
    val status: SweepingPaymentStatus,
    val error: String? = null,
    val errorDescription: String? = null,
)

fun SweepingPaymentStatusResponse.toSweepingPaymentStatusResponseTO() = SweepingPaymentStatusResponseTO(
    status = this.status,
    error = this.error?.code,
    errorDescription = this.error?.detail,
)

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "type",
    visible = true,
)
sealed interface CreatePaymentRiskSignalsTO {
    val type: SweepingRiskSignalsType
}

@JsonTypeName("AUTOMATIC")
data class AutomaticPaymentRiskSignalsTO(
    val lastLoginDateTime: String,
    val pixKeyRegistrationDateTime: String?,
) : CreatePaymentRiskSignalsTO {
    override val type = SweepingRiskSignalsType.AUTOMATIC
}

@JsonTypeName("MANUAL")
data class ManualPaymentRiskSignalsTO(
    val deviceId: String,
    val isRootedDevice: Boolean?,
    val screenBrightness: Number?,
    val elapsedTimeSinceBoot: Long?,
    val osVersion: String?,
    val userTimeZoneOffset: String?,
    val language: String?,
    val screenDimensions: ScreenDimensionsTO?,
) : CreatePaymentRiskSignalsTO {
    override val type = SweepingRiskSignalsType.MANUAL
}

data class ScreenDimensionsTO(
    val height: Int,
    val width: Int,
)

data class BankAccountBalanceTO(
    val availableAmount: Long,
    val blockedAmount: Long,
    val automaticallyInvestedAmount: Long,
    val updateDateTime: String,
)

data class SweepingConsentPeriodicUsageTO(
    val consentId: String,
    val daily: SweepingConsentPeriodicLimitUsageTO,
    val weekly: SweepingConsentPeriodicLimitUsageTO,
    val monthly: SweepingConsentPeriodicLimitUsageTO,
    val yearly: SweepingConsentPeriodicLimitUsageTO,
    val totalLimit: Long,
    val totalUsed: Long,
)

data class SweepingConsentPeriodicLimitUsageTO(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
)

private fun SweepingConsentPeriodicUsage.toSweepingConsentPeriodicUsageTO() = SweepingConsentPeriodicUsageTO(
    consentId = consentId.value,
    daily = daily.toSweepingConsentPeriodicLimitUsageTO(),
    weekly = weekly.toSweepingConsentPeriodicLimitUsageTO(),
    monthly = monthly.toSweepingConsentPeriodicLimitUsageTO(),
    yearly = yearly.toSweepingConsentPeriodicLimitUsageTO(),
    totalLimit = totalLimit,
    totalUsed = totalUsed,
)

private fun SweepingConsentPeriodicLimitUsage.toSweepingConsentPeriodicLimitUsageTO() = SweepingConsentPeriodicLimitUsageTO(
    amountLimit = amountLimit,
    amountUsed = amountUsed,
    quantityLimit = quantityLimit,
    quantityUsed = quantityUsed,
)