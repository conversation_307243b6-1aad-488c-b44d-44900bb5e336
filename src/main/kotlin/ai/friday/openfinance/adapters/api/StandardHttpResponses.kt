package ai.friday.openfinance.adapters.api

import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus

object StandardHttpResponses {
    fun serverError(message: String = "Internal Server Error. Please try again later."): HttpResponse<ResponseTO> =
        HttpResponse.serverError(ResponseTO("500", message))

    fun serverError(responseTO: ResponseTO): HttpResponse<ResponseTO> = HttpResponse.serverError(responseTO)

    fun badRequest(code: Int, message: String): HttpResponse<*> =
        HttpResponse.badRequest(ResponseTO(code.toString(), message))

    fun badRequest(response: ResponseTO): HttpResponse<*> = HttpResponse.badRequest(response)

    fun customStatusResponse(httpStatus: HttpStatus, responseTO: ResponseTO): HttpResponse<ResponseTO> =
        HttpResponse.status<ResponseTO>(httpStatus).body(responseTO)

    fun created(): HttpResponse<ResponseTO> = HttpResponse.created(ResponseTO("000", "Success"))

    fun notFound(message: String): HttpResponse<ResponseTO> = HttpResponse.notFound(ResponseTO("404", message))

    fun notFound(responseTO: ResponseTO): HttpResponse<ResponseTO> = HttpResponse.notFound(responseTO)
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ResponseTO(
    val code: String,
    val message: String,
)