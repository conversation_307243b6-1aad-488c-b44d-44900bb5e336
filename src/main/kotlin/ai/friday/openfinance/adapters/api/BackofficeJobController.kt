package ai.friday.openfinance.adapters.api

import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.auth.Role
import ai.friday.openfinance.app.integrations.JobRepository
import ai.friday.openfinance.app.job.Job
import ai.friday.openfinance.app.job.JobManager
import ai.friday.openfinance.app.utils.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.security.annotation.Secured
import jakarta.inject.Named
import java.time.Duration
import java.util.Optional
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/job")
class BackofficeJobController(
    private val jobManager: Optional<JobManager>,
    private val jobRepository: JobRepository,
    @param:Named(TaskExecutors.SCHEDULED) private val taskScheduler: TaskScheduler,
) {

    @Get("/")
    fun listJobs(): HttpResponse<*> {
        val jobs = jobRepository.findAll()

        LOG.info(Markers.append("jobsSize", jobs.size), "BackofficeJobController#listJobs")

        return HttpResponse.ok(jobs.map { it.toJobTO() })
    }

    @Get("/{jobName}")
    fun getJobByName(@PathVariable jobName: String): HttpResponse<*> {
        val job = jobRepository.findAll().single { it.name == jobName }

        LOG.info(Markers.append("jobName", jobName), "BackofficeJobController#getJobByName")

        return HttpResponse.ok(job.toJobTO())
    }

    @Post("/{jobName}/execute/")
    fun executeJob(@PathVariable jobName: String): HttpResponse<*> {
        val markers = Markers.append("jobName", jobName)

        val abstractJob = jobManager.get().listJobs().single { it.jobName == jobName }
        markers.andAppend("previousLastStartTime", abstractJob.lastStartTime)

        val now = BrazilZonedDateTimeSupplier.getZonedDateTime()

        taskScheduler.schedule(Duration.ZERO, abstractJob)

        while (abstractJob.lastStartTime?.isBefore(now) != false) {
            Thread.sleep(500)
        }
        markers.andAppend("currentLastStartTime", abstractJob.lastStartTime)

        LOG.info(markers, "BackofficeJobController#executeJob")
        return HttpResponse.ok(abstractJob.toJob().toJobTO())
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeJobController::class.java)
    }
}

data class JobTO(
    val name: String,
    val crons: List<String>,
    val fixedDelay: String?,
    val running: Boolean,
    val lastStartTime: String?,
    val lastElapsedMinutes: Long?,
    val shouldLock: Boolean,
    val lockAtLeastFor: String,
    val lockAtMostFor: String,
    val shutdownGracefully: Boolean,
    val shutdownGracefullyMaxWaitTime: Int,
)

private fun Job.toJobTO() = JobTO(
    name = this.name,
    crons = this.crons,
    fixedDelay = this.fixedDelay?.toString(),
    running = this.running,
    lastStartTime = this.lastStartTime?.format(dateTimeFormat),
    lastElapsedMinutes = this.lastElapsedMinutes,
    shouldLock = this.shouldLock,
    lockAtLeastFor = this.lockAtLeastFor.toString(),
    lockAtMostFor = this.lockAtMostFor.toString(),
    shutdownGracefully = this.shutdownGracefully,
    shutdownGracefullyMaxWaitTime = this.shutdownGracefullyMaxWaitTime,
)