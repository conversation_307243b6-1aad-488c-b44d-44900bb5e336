package ai.friday.openfinance.adapters.api

import ai.friday.openfinance.adapters.auth.Role
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.connection.DataConsentService
import ai.friday.openfinance.app.integrations.BankAccountAdapter
import ai.friday.openfinance.app.isValidCnpj
import ai.friday.openfinance.app.isValidCpf
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/bankAccount")
class BackofficeBankAccountController(
    private val dataConsentService: DataConsentService,
    private val bankAccountAdapter: BankAccountAdapter,
) {
    private val logger = LoggerFactory.getLogger(BackofficeBankAccountController::class.java)

    @Post("/link")
    fun createLink(
        @Body request: CreateDataConsentRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeBankAccountController#link"
        val markers = Markers.append("request", request)

        if (!request.document.isValidCpf() && !request.document.isValidCnpj()) {
            logger.error(markers.andAppend("document", request.document), logName)
            return StandardHttpResponses.badRequest(ResponseTO("4001", message = "Cpf ou Cnpj inválido"))
        }

        val connectionLink = dataConsentService.requestConsent(
            userAccountId = UserAccountId(value = request.userAccountId),
            document = Document(request.document),
            participantId = ParticipantId(request.participantId),
        )
        return connectionLink.fold(
            ifLeft = { error ->
                logger.error(markers, logName, error)
                StandardHttpResponses.customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("4001", message = "Erro ao criar link. Tente novamente mais tarde."),
                )
            },
            ifRight = {
                logger.info(markers, logName)
                HttpResponse.ok(
                    CreateDataConsentResponseTO(
                        dataConsentId = it.dataConsentId.value,
                        link = it.link,
                    ),
                )
            },
        )
    }

    @Get("/{consentId}/{bankAccountId}/balances")
    fun getBalances(@PathVariable consentId: String, @PathVariable bankAccountId: String): HttpResponse<*> {
        val logName = "BackofficeBankAccountController#getBalances"
        val markers = Markers.append("consentId", consentId)
            .andAppend("bankAccountId", bankAccountId)

        return bankAccountAdapter.getBankAccountBalance(
            dataConsentId = DataConsentId(consentId),
            bankAccountId = BankAccountId(bankAccountId),
        ).map {
            markers.andAppend("balances", it)
            logger.info(markers, logName)
            HttpResponse.ok(it)
        }.getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            HttpResponse.serverError(it)
        }
    }

    @Get("/{consentId}/{bankAccountId}/limits")
    fun getLimits(@PathVariable consentId: String, @PathVariable bankAccountId: String): HttpResponse<*> {
        val logName = "BackofficeBankAccountController#getLimits"
        val markers = Markers.append("consentId", consentId)
            .andAppend("bankAccountId", bankAccountId)

        return bankAccountAdapter.getBankAccountLimits(
            dataConsentId = DataConsentId(consentId),
            bankAccountId = BankAccountId(bankAccountId),
        ).map {
            markers.andAppend("balances", it)
            logger.info(markers, logName)
            HttpResponse.ok(it)
        }.getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            HttpResponse.serverError(it)
        }
    }
}