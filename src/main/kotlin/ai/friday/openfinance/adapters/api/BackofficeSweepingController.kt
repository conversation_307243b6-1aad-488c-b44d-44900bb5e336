package ai.friday.openfinance.adapters.api

import ai.friday.openfinance.adapters.auth.Role
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.sweepingaccount.SweepingAccountService
import ai.friday.openfinance.app.sweepingaccount.toErrorCode
import ai.friday.openfinance.app.utils.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/sweeping")
class BackofficeSweepingController(
    private val sweepingAccountService: SweepingAccountService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeSweepingController::class.java)

    @Post("/sweepingTransaction")
    fun createPayment(
        @Body request: CreatePaymentRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeSweepingController#createPayment"
        val markers = Markers.append("request", request)

        try {
            val result = sweepingAccountService.createPayment(request.toDomain())
            return result.fold(
                ifLeft = { error ->
                    val errorCode = error.toErrorCode()
                    markers.andAppend("errorCode", errorCode)
                        .andAppend("error", error)
                    logger.error(markers, logName)

                    StandardHttpResponses.customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO(errorCode, message = "Erro ao criar pagamento. Tente novamente mais tarde."),
                    )
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.ok(it.toCreatePaymentResponseTO())
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return StandardHttpResponses.customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro ao criar pagamento. Tente novamente mais tarde."),
            )
        }
    }

    @Get("/payment/{paymentRequestId}")
    fun getPayment(@PathVariable paymentRequestId: String): HttpResponse<*> {
        val logName = "BackofficeSweepingController#getPayment"
        val markers = Markers.append("paymentRequestId", paymentRequestId)

        try {
            val result = sweepingAccountService.getPaymentStatus(SweepingPaymentRequestId(paymentRequestId))
            return result.fold(
                ifLeft = { error ->
                    markers.andAppend("error", error)
                    logger.error(markers, logName)

                    StandardHttpResponses.customStatusResponse(
                        HttpStatus.UNPROCESSABLE_ENTITY,
                        ResponseTO("4001", message = "Erro consultando pagamento. Tente novamente mais tarde."),
                    )
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.ok(it)
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return StandardHttpResponses.customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO("4001", message = "Erro consultando pagamento. Tente novamente mais tarde."),
            )
        }
    }
}