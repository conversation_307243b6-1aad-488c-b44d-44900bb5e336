package ai.friday.openfinance.adapters.api

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.openfinance.adapters.auth.Role
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCard
import ai.friday.openfinance.app.creditcard.CreditCardService
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.utils.toYearMonth
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import java.time.YearMonth

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/creditCard")
class BackofficeCreditCardController(
    private val creditCardService: CreditCardService,
    private val dataConsentRepository: DataConsentRepository,
) {
    @Get("/users/{userAccountId}/debt-by-month")
    fun getUserDebtByMonth(
        @PathVariable userAccountId: String,
    ): List<Pair<YearMonth, CreditCardService.MonthlyDebt>> {
        return creditCardService.calculateUserDebtByMonth(UserAccountId(userAccountId), getLocalDate().toYearMonth().minusMonths(12)).toList().sortedByDescending {
            it.first
        }
    }

    @Get("/users/{userAccountId}/open-transactions")
    fun getOpenTransactionsByCard(
        @PathVariable userAccountId: String,
    ): List<CreditCardService.CreditCardOpenTransactions> {
        return creditCardService.getOpenTransactionsByCard(UserAccountId(userAccountId))
            .map { openTransactions ->
                openTransactions.copy(
                    transactions = openTransactions.transactions.sortedByDescending { it.transactionDateTime },
                )
            }
    }

    @Get("/users/{userAccountId}/transactions-by-month")
    fun getTransactionsByMonth(
        @PathVariable userAccountId: String,
        @QueryValue year: Int,
        @QueryValue month: Int,
    ): List<CreditCardService.CreditCardMonthlyTransactions> {
        return creditCardService.getTransactionsByMonth(UserAccountId(userAccountId), YearMonth.of(year, month))
            .map { monthlyTransactions ->
                monthlyTransactions.copy(
                    transactions = monthlyTransactions.transactions.sortedByDescending { it.transactionDateTime },
                )
            }
    }

    @Get("/users/{userAccountId}/all-invoices")
    fun getAllInvoices(
        @PathVariable userAccountId: String,
    ): List<CreditCardService.CreditCardInvoices> {
        return creditCardService.getAllInvoices(UserAccountId(userAccountId))
            .map { invoices ->
                invoices.copy(
                    invoices = invoices.invoices.sortedByDescending { it.dueDate },
                )
            }
    }

    @Post("/users/{userAccountId}/sync")
    fun syncUserCreditCards(
        @PathVariable userAccountId: String,
    ): List<CreditCard> {
        val userAccountIdObj = UserAccountId(userAccountId)
        val dataConsents = dataConsentRepository.findByUserAccountId(userAccountIdObj).filter { it.status == DataConsentStatus.AVAILABLE }

        return dataConsents.flatMap { dataConsent ->
            creditCardService.refreshCreditCards(dataConsent, force = true).fold(
                ifLeft = { emptyList() },
                ifRight = { it },
            )
        }
    }
}