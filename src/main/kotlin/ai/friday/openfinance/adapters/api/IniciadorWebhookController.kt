package ai.friday.openfinance.adapters.api

import ai.friday.openfinance.adapters.messaging.AvailableConnectionEventTO
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.OpenFinanceDataConsentStatus
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.utils.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpHeaders
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/iniciador")
@Secured(SecurityRule.IS_ANONYMOUS)
class IniciadorWebhookController(
    private val messagePublisher: MessagePublisher,
    @Property(name = "friday.morning.messaging.consumer.available-connection.queueName") private val availableConnectionQueueName: String,
    @Property(name = "friday.morning.messaging.consumer.sweeping-webhook.queueName") private val sweepingWebhookQueueName: String,
) {

    @Post("/webhook")
    fun webhook(
        @Body request: WebhookTO,
        headers: HttpHeaders,
    ): HttpResponse<*> {
        val logName = "IniciadorWebhookController#webhook"
        val markers = Markers.append("request", request)

        val signature = headers.get("x-iniciador-signature")
        val timestamp = headers.get("x-iniciador-timestamp")

        if (signature == null || timestamp == null) {
            markers.andAppend("signature", signature)
                .andAppend("timestamp", timestamp)
            logger.warn(markers, logName)
            return HttpResponse.unauthorized<Unit>()
        }

        if (request.status.internalStatus == DataConsentStatus.AVAILABLE) {
            messagePublisher.sendMessage(
                availableConnectionQueueName,
                AvailableConnectionEventTO(
                    consentId = request.linkId,
                    status = request.status,
                ),
            )
        }

        logger.info(markers, logName)
        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IniciadorWebhookController::class.java)
    }

    @Post("/sweeping/webhook")
    fun sweepingWebhook(
        @Body request: SweepingWebhookTO,
    ): HttpResponse<*> {
        val logName = "IniciadorWebhookController#sweepingWebhook"
        val markers = Markers.append("request", request)

        return try {
            messagePublisher.sendMessage(
                sweepingWebhookQueueName,
                request,
            )
            logger.info(markers, logName)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            HttpResponse.serverError<Unit>()
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingWebhookTO(
    val authorizationId: String?,
    val id: String?,
    val date: String?,
    val description: String?,
    val createdAt: String?,
    val consentId: String?,
    val paymentId: String? = null,
    val updatedAt: String? = null,
    val endToEndId: String?,
    val status: String,
    val amount: Long?,
    val externalId: String? = null,
    val error: SweepingWebhookErrorTO? = null,
    val errors: List<SweepingWebhookErrorTO>? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingWebhookErrorTO(
    val code: String?,
    val title: String?,
    val detail: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class WebhookTO(
    val linkId: String,
    val externalId: String,
    val status: OpenFinanceDataConsentStatus,
    val applicationId: String,

)