package ai.friday.openfinance.adapters.kms

import ai.friday.openfinance.app.integrations.KmsService
import com.amazonaws.services.kms.AWSKMSClient
import com.amazonaws.services.kms.model.DecryptRequest
import com.amazonaws.services.kms.model.EncryptRequest
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.nio.ByteBuffer
import org.apache.commons.codec.binary.Base64

@Singleton
class KmsAdapter(
    @Property(name = "aws.region") private val awsRegion: String,
    @Property(name = "aws.kms.keyId") private val kmsKeyId: String,
) : KmsService {

    private val base64 = Base64()
    private val awskms = AWSKMSClient.builder().withRegion(awsRegion).build()

    override suspend fun encryptData(text: String): String {
        val encryptRequest = EncryptRequest().withKeyId(kmsKeyId).withPlaintext(ByteBuffer.wrap(text.toByteArray()))
        return awskms.encrypt(encryptRequest).ciphertextBlob?.let {
            base64.encodeToString(it.array())
        } ?: throw RuntimeException("Error encrypting data")
    }

    override suspend fun decryptData(encryptedDataVal: String): String {
        val decryptRequest = DecryptRequest().withKeyId(kmsKeyId).withCiphertextBlob(ByteBuffer.wrap(base64.decode(encryptedDataVal)))

        return awskms.decrypt(decryptRequest).plaintext?.let {
            return String(it.array())
        } ?: ""
    }

    private fun convert(byteBuffer: ByteBuffer): String {
        return base64.encodeToString(byteBuffer.array())
    }

    private fun convert(value: String): ByteBuffer {
        return ByteBuffer.wrap(base64.decode(value))
    }
}