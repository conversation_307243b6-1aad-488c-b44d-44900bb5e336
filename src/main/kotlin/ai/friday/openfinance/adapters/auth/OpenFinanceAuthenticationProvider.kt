package ai.friday.openfinance.adapters.auth

import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.AuthenticationProvider
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import io.reactivex.Flowable
import jakarta.inject.Singleton
import org.reactivestreams.Publisher

@Singleton
class OpenFinanceAuthenticationProvider(
    @Property(name = "open-finance-auth.clientid") private val billPaymentClientId: String,
    @Property(name = "open-finance-auth.clientsecret") private val billPaymentClientSecret: String,
    @Property(name = "open-finance-backoffice-auth.clientid") private val backofficeClientId: String,
    @Property(name = "open-finance-backoffice-auth.clientsecret") private val backofficeClientSecret: String,
) : AuthenticationProvider<HttpRequest<*>> {
    override fun authenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Publisher<AuthenticationResponse> {
        return if (authenticationRequest.identity == billPaymentClientId && authenticationRequest.secret == billPaymentClientSecret) {
            Flowable.just(AuthenticationResponse.success(billPaymentClientId))
        } else if (authenticationRequest.identity == backofficeClientId && authenticationRequest.secret == backofficeClientSecret) {
            Flowable.just(AuthenticationResponse.success(backofficeClientId, listOf(Role.BACKOFFICE.name)))
        } else {
            Flowable.just(AuthenticationResponse.failure())
        }
    }
}