package ai.friday.openfinance.adapters.sqs

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryPolicy
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient

@Factory
class SqsFactory(@Property(name = "aws.region") private val sqsRegion: String) {
    @Singleton
    fun getSqsFactory() = buildSqsClient(Region.of(sqsRegion))

    private fun buildSqsClient(region: Region): SqsClient {
        return SqsClient.builder()
            .region(region)
            .overrideConfiguration(
                ClientOverrideConfiguration.builder()
                    .retryPolicy(RetryPolicy.builder().numRetries(25).build()).build(),
            )
            .build()
    }
}

@Factory
class SNSFactory(@Property(name = "aws.region") private val snsRegion: String) {

    @Singleton
    fun snsFactory(): SnsClient =
        SnsClient.builder()
            .region(Region.of(snsRegion))
            .overrideConfiguration(
                ClientOverrideConfiguration.builder()
                    .retryPolicy(RetryPolicy.builder().numRetries(25).build()).build(),
            )
            .build()
}