package ai.friday.openfinance.adapters.sqs

import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.app.OpenFinanceResourceType
import ai.friday.openfinance.app.integrations.CheckDataConsentStatusMessage
import ai.friday.openfinance.app.integrations.CheckSweepingConsentStatusMessage
import ai.friday.openfinance.app.integrations.MessagePublisher
import ai.friday.openfinance.app.integrations.OpenFinanceMessage
import ai.friday.openfinance.app.integrations.OpenFinanceSyncCreditCardMessage
import ai.friday.openfinance.app.utils.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@Singleton
@Requires(notEnv = ["test"])
class SQSMessagePublisher(
    private val amazonSQS: SqsClient,
    @Property(name = "friday.morning.messaging.publisher.open-finance.queueName") private val openFinanceDataQueueName: String,
    @Property(name = "friday.morning.messaging.consumer.check-sweeping-consent.queueName") private val checkSweepingAccountQueueName: String,
    @Property(name = "friday.morning.messaging.consumer.check-data-consent.queueName") private val checkDataConsentQueueName: String,
    @Property(name = "friday.morning.messaging.consumer.sync-credit-card.queueName") private val syncCreditCardQueueName: String,
) : MessagePublisher {

    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String): String {
        return queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }
    }

    override fun sendMessage(message: OpenFinanceMessage) {
        sendMessage(openFinanceDataQueueName, message, message.resourceType)
    }

    override fun sendMessage(message: CheckSweepingConsentStatusMessage) {
        sendMessage(checkSweepingAccountQueueName, message, null, 300)
    }

    override fun sendMessage(message: CheckDataConsentStatusMessage) {
        sendMessage(checkDataConsentQueueName, message, null, 300)
    }

    override fun sendMessage(message: OpenFinanceSyncCreditCardMessage) {
        sendMessage(syncCreditCardQueueName, message, null, 0)
    }

    override fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int?,
    ) {
        sendMessage(queueName, body, null, delaySeconds)
    }

    private fun sendMessage(
        queueName: String,
        body: Any,
        resourceType: OpenFinanceResourceType? = null,
        delaySeconds: Int? = null,
    ) {
        val markers = Markers.append("queueName", queueName)
            .andAppend("messageBody", body)

        val queueUrl = getQueueURL(queueName)
        val queueMessage = getObjectMapper().writeValueAsString(body)
        val sendMessageRequestBuilder = SendMessageRequest.builder()
            .queueUrl(queueUrl)
            .messageBody(queueMessage)

        resourceType?.let {
            sendMessageRequestBuilder.messageAttributes(
                mapOf(RESOURCE_TYPE to MessageAttributeValue.builder().dataType("String").stringValue(it.name).build()),
            )
        }

        delaySeconds?.let {
            sendMessageRequestBuilder.delaySeconds(it)
        }

        LOG.info(markers, "SQSMessagePublisher")
        amazonSQS.sendMessage(sendMessageRequestBuilder.build())
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSMessagePublisher::class.java)
        private const val RESOURCE_TYPE = "resourceType"
    }
}