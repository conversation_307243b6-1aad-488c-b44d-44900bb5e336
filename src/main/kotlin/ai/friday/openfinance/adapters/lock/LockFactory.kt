package ai.friday.openfinance.adapters.lock

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.app.integrations.InternalLock
import ai.friday.openfinance.app.integrations.LockReleaser
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Parameter
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.Duration
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.core.SimpleLock
import net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBLockProvider
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest

/*
    Para criar um lock novo é necessario adicionar no application.yml (lock.configuration.***)
 */
// const val cashInLockProvider = "cash-in"
// const val internalBankServiceLockProvider = "internal-bank-service"
// const val updateAccountStatusLockProvider = "update-account-status"
// const val walletDailyPaymentLimitLockProvider = "wallet-daily-payment-limit"
// const val undoInvoiceLockProvider = "undo-invoice"
// const val onePixPayNotification = "one-pix-pay-notification"
// const val billComingDueLastWarnNotification = "bill-coming-due-last-warn-notification"
// const val billTrackingCalculate = "bill-tracking-calculate"
// const val billTrackingQuery = "bill-tracking-query"
// const val limitLockProvider = "limit-lock-provider"
// const val issuedTokenLockProvider = "issued-token-lock-provider"
// const val transactionLockProvider = "transaction-lock-provider"

@Factory
class LockFactory(
    private val dynamoDbClient: DynamoDbClient,
    @Property(name = "lock.tableName") private val tableName: String,
) {

    @Singleton
    fun lockProvider(dynamoDbClient: DynamoDbClient): LockProvider =
        DynamoDBLockProvider(dynamoDbClient, tableName)

    @EachBean(InternalLockConfiguration::class)
    internal fun lockProvider(configuration: InternalLockConfiguration): InternalLockProvider {
        return InternalLockProvider(lockProvider(dynamoDbClient), configuration)
    }

    @EachBean(InternalLockConfiguration::class)
    internal fun lockReleaser(configuration: InternalLockConfiguration) =
        InternalLockReleaser(dynamoDbClient, configuration, tableName)
}

class InternalLockProvider(
    private val lockProvider: LockProvider,
    private val lockConfigurationConfiguration: InternalLockConfiguration,
) : InternalLock {

    override fun acquireLock(
        lockName: String,
        minDuration: Duration?,
        maxDuration: Duration?,
        simultaneousLock: Int,
    ): SimpleLock? {
        repeat(times = simultaneousLock) { index ->
            val suffix = if (index > 0) {
                "#${index + 1}"
            } else {
                ""
            }
            val lock: SimpleLock? = lockProvider.lock(
                LockConfiguration(
                    getZonedDateTime().toInstant(),
                    "${lockConfigurationConfiguration.prefix}$lockName$suffix",
                    maxDuration ?: lockConfigurationConfiguration.maxDuration,
                    minDuration ?: lockConfigurationConfiguration.minDuration,
                ),
            ).orElse(null)

            if (lock != null) {
                return lock
            }
        }
        return null
    }

    override fun waitForAcquireLock(lockName: String): SimpleLock {
        while (true) {
            val lock = lockProvider.lock(
                LockConfiguration(
                    getZonedDateTime().toInstant(),
                    "${lockConfigurationConfiguration.prefix}$lockName",
                    lockConfigurationConfiguration.maxDuration,
                    lockConfigurationConfiguration.minDuration,
                ),
            ).orElse(null)
            if (lock != null) return lock
        }
    }
}

class InternalLockReleaser(
    private val dynamoDbClient: DynamoDbClient,
    private val configuration: InternalLockConfiguration,
    private val tableName: String,
) : LockReleaser {
    override fun release(lockName: String): Result<Unit> = runCatching {
        dynamoDbClient.deleteItem(
            DeleteItemRequest.builder()
                .tableName(tableName)
                .key(mapOf("_id" to AttributeValue.builder().s("${configuration.prefix}$lockName").build())).build(),
        )
    }
}

@EachProperty("lock.configuration")
class InternalLockConfiguration @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val maxDuration: Duration,
    val minDuration: Duration,
    val prefix: String,
)