package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.LocalDateTime
import javax.security.auth.login.LoginException
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class IniciadorAuthenticationManager(
    @param:Client(value = "\${integrations.iniciador.host}") private val httpClient: RxHttpClient,
    private val configuration: IniciadorConfiguration,
) {

    private var token: String? = null
    private var expiresIn: LocalDateTime? = null
    private val expireOffset = 1L

    open fun getToken(): String {
        return expiresIn?.let {
            return if (it.minusMinutes(expireOffset).isAfter(getZonedDateTime().toLocalDateTime())) {
                token?.let { token -> return token } ?: getAccessToken()
            } else {
                getAccessToken()
            }
        } ?: getAccessToken()
    }

    private fun getAccessToken(): String {
        val httpRequest = HttpRequest.POST(
            configuration.accessTokenPath,
            mapOf("clientId" to configuration.clientId, "clientSecret" to configuration.clientSecret),
        )
            .contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(AccessTokenTO::class.java),
        )
        return try {
            val response = call.firstOrError().blockingGet()
            expiresIn = getZonedDateTime().toLocalDateTime().plusSeconds(response.expiresIn)
            token = response.accessToken
            response.accessToken
        } catch (e: HttpClientResponseException) {
            val markers = Markers.append("error", "Error on Iniciador access token API")
                .and<LogstashMarker>(Markers.append("status", e.status))
            logger.error(
                markers,
                "IniciadorAuthenticationManager#getAccessToken",
                e,
            )
            cleanTokens()
            throw LoginException("Error on Iniciador access token API")
        }
    }

    fun cleanTokens() {
        token = null
        expiresIn = null
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IniciadorAuthenticationManager::class.java)
    }
}

data class AccessTokenTO(
    @JsonProperty("accessToken") val accessToken: String,
    @JsonProperty("expiresIn") val expiresIn: Long,
)

@ConfigurationProperties(value = "integrations.iniciador")
data class IniciadorConfiguration @ConfigurationInject constructor(
    val accessTokenPath: String,
    val clientId: String,
    val clientSecret: String,
)