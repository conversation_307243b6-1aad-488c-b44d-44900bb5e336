package ai.friday.openfinance.adapters.iniciador

import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.app.integrations.KmsService
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.core.type.Argument
import io.micronaut.core.util.StringUtils.EMPTY_STRING
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

abstract class IniciadorAdapter(
    open val httpClient: RxHttpClient,
    open val iniciadorAuthenticationManager: IniciadorAuthenticationManager,
    open val kmsService: KmsService,
) {
    inline fun <reified T : WithRaw> handleRequest(uri: String, markersMap: Map<String, Any>, logName: String): Either<Exception, List<T>> {
        return generateSequence(
            seed = request(
                uri = uri,
                markersMap = markersMap,
                logName = logName,
            ).getOrElse {
                return it.left()
            },
        ) { response ->
            response.links.next?.let { nextPage ->
                if (nextPage == uri || nextPage == response.links.self) {
                    val markers = Markers.append("responseLinks", response.links)
                        .andAppend("responseMeta", response.meta)
                        .andAppend("uri", uri)
                    logger.warn(markers, "$logName#invalidNextPage")
                    null
                } else {
                    request(
                        uri = nextPage,
                        markersMap = markersMap,
                        logName = logName,
                    ).getOrNull()
                }
            }
        }.flatMap { it.data.asSequence() }.toList().map { it.to<T>(encryptRawData(it)) }.right()
    }

    fun request(uri: String, markersMap: Map<String, Any>, logName: String): Either<Exception, IniciadorResponseTO> {
        val token = iniciadorAuthenticationManager.getToken()

        val markers = Markers.appendEntries(markersMap).andAppend("uri", uri)

        val httpRequest =
            HttpRequest.GET<IniciadorResponseTO>(uri)
                .header("Authorization", token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(IniciadorResponseTO::class.java),
            )

        return try {
            val response = call.firstOrError().blockingGet()
            markers.andAppend("responseLinks", response.links)
                .andAppend("responseMeta", response.meta)
            logger.info(markers, logName)
            response.right()
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(
                markers.andAppend("response", response).andAppend("status", e.response.status.code),
                logName,
                e,
            )
            e.left()
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            e.left()
        }
    }

    fun encryptRawData(data: Map<String, Any>): String {
        val encryptData = runBlocking {
            kmsService.encryptData(getObjectMapper().writeValueAsString(data))
        }
        return encryptData
    }

    companion object {
        val logger = LoggerFactory.getLogger(IniciadorAdapter::class.java)
    }
}

data class IniciadorResponseTO(
    @JsonFormat(with = [JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY])
    @JsonProperty("data")
    val data: List<Map<String, Any>>,
    @JsonProperty("links") val links: LinksTO,
    @JsonProperty("meta") val meta: MetaTO,
)

data class LinksTO(
    val self: String,
    val first: String? = null,
    val prev: String? = null,
    val next: String? = null,
    val last: String? = null,
)

data class MetaTO(
    val totalRecords: Int? = null,
    val totalPages: Int? = null,
    val requestDateTime: String,
)

open class WithRaw(open var raw: String = EMPTY_STRING)

inline fun <reified T : WithRaw> Map<String, Any>.to(raw: String): T {
    val obj = jacksonObjectMapper().convertValue<T>(this)
    obj.raw = raw
    return obj
}