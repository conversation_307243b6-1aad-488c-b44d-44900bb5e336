package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.date.dateFormat
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.creditcard.CreditCard
import ai.friday.openfinance.app.creditcard.CreditCardBillAmount
import ai.friday.openfinance.app.creditcard.CreditCardExternalId
import ai.friday.openfinance.app.creditcard.CreditCardFinanceCharges
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoice
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceExternalId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardPayment
import ai.friday.openfinance.app.creditcard.CreditCardTransaction
import ai.friday.openfinance.app.creditcard.CreditCardTransactionExternalId
import ai.friday.openfinance.app.integrations.KmsService
import ai.friday.openfinance.app.integrations.OpenFinanceCreditCardAdapter
import arrow.core.Either
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.partiql.lang.util.times

@Singleton
class IniciadorCreditCardAdapter(
    override val iniciadorAuthenticationManager: IniciadorAuthenticationManager,
    @param:Client(value = "\${integrations.iniciador.host}") override val httpClient: RxHttpClient,
    override val kmsService: KmsService,
) : OpenFinanceCreditCardAdapter, IniciadorAdapter(httpClient, iniciadorAuthenticationManager, kmsService) {

    override fun getCreditCards(dataConsentId: DataConsentId): Either<Exception, List<CreditCard>> {
        return handleRequest<OFCreditCardDataTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/credit-cards",
            markersMap = mapOf("dataConsentId" to dataConsentId.value),
            logName = "IniciadorCreditCardAdapter#getCreditCards",
        ).map { list ->
            list.map {
                it.toDomain(dataConsentId)
            }
        }
    }

    override fun getCreditCardTransactions(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId, startDate: LocalDate, endDate: LocalDate): Either<Exception, List<CreditCardTransaction>> {
        return handleRequest<OFTransactionTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/credit-cards/${creditCardExternalId.value}/transactions?fromTransactionDate=${startDate.format(dateFormat)}&toTransactionDate=${
            endDate.format(
                dateFormat,
            )
            }",
            markersMap = mapOf(
                "dataConsentId" to dataConsentId.value,
                "creditCardExternalId" to creditCardExternalId.value,
                "startDate" to startDate.format(dateFormat),
                "endDate" to endDate.format(dateFormat),
            ),
            logName = "IniciadorCreditCardAdapter#getCreditCardTransactions",
        ).map { list ->
            list.map {
                it.toDomain(dataConsentId)
            }
        }
    }

    override fun getCreditCardInvoices(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId): Either<Exception, List<CreditCardInvoice>> {
        return handleRequest<OFCreditCardInvoiceDataTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/credit-cards/${creditCardExternalId.value}/bills",
            markersMap = mapOf(
                "dataConsentId" to dataConsentId.value,
                "creditCardExternalId" to creditCardExternalId.value,
            ),
            logName = "IniciadorCreditCardAdapter#getCreditCardInvoices",
        ).map { list ->
            list.map {
                it.toDomain()
            }
        }
    }

    override fun getCreditCardInvoiceTransactions(dataConsentId: DataConsentId, creditCardExternalId: CreditCardExternalId, invoiceExternalId: CreditCardInvoiceExternalId): Either<Exception, List<CreditCardTransaction>> {
        return handleRequest<OFTransactionTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/credit-cards/${creditCardExternalId.value}/bills/${invoiceExternalId.value}/transactions",
            markersMap = mapOf(
                "dataConsentId" to dataConsentId.value,
                "creditCardExternalId" to creditCardExternalId.value,
                "invoiceExternalId" to invoiceExternalId.value,
            ),
            logName = "IniciadorCreditCardAdapter#getCreditCardInvoiceDetails",
        ).map { list ->
            list.map {
                it.toDomain(dataConsentId)
            }
        }
    }
}

private fun OFBillAmountTO.toDomain(): CreditCardBillAmount {
    return CreditCardBillAmount(
        amount = amount.parseAsLong(),
        currency = currency,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFTransactionTO(
    val transactionId: String,
    val identificationNumber: String,
    val transactionName: String,
    val billId: String?,
    val creditDebitType: String,
    val transactionType: String,
    val transactionalAdditionalInfo: String?,
    val paymentType: String?,
    val feeType: String?,
    val feeTypeAdditionalInfo: String?,
    val otherCreditsType: String?,
    val otherCreditsAdditionalInfo: String?,
    val chargeIdentificator: String?,
    val chargeNumber: String?,
    val brazilianAmount: OFBillAmountTO,
    val amount: OFBillAmountTO,
    val transactionDateTime: String,
    val billPostDate: String,
    val payeeMCC: Int,
) : WithRaw() {
    fun toDomain(dataConsentId: DataConsentId): CreditCardTransaction {
        return CreditCardTransaction(
            transactionExternalId = CreditCardTransactionExternalId(transactionId),
            dataConsentId = dataConsentId,
            identificationNumber = identificationNumber,
            transactionName = transactionName,
            invoiceId = billId?.let { CreditCardInvoiceId(it) },
            creditDebitType = creditDebitType,
            transactionType = transactionType,
            transactionalAdditionalInfo = transactionalAdditionalInfo,
            paymentType = paymentType,
            feeType = feeType,
            feeTypeAdditionalInfo = feeTypeAdditionalInfo,
            otherCreditsType = otherCreditsType,
            otherCreditsAdditionalInfo = otherCreditsAdditionalInfo,
            chargeIdentificator = chargeIdentificator,
            chargeNumber = chargeNumber,
            amount = brazilianAmount.amount.parseAsLong(),
            originalAmount = amount.toDomain(),
            transactionDateTime = ZonedDateTime.parse(transactionDateTime, DateTimeFormatter.ISO_DATE_TIME),
            billPostDate = billPostDate,
            payeeMCC = payeeMCC,
            raw = raw,
            provider = OFProvider.INICIADOR,
        )
    }
}

data class OFCreditCardInvoiceDataTO(
    val billId: String,
    val dueDate: String,
    val billTotalAmount: OFBillAmountTO,
    val billMinimumAmount: OFBillAmountTO,
    val isInstalment: Boolean,
    val financeCharges: List<OFFinanceChargesTO>? = null,
    val payments: List<OFPaymentTO>,
) : WithRaw() {
    fun toDomain(): CreditCardInvoice {
        return CreditCardInvoice(
            invoiceId = CreditCardInvoiceId(),
            invoiceExternalId = CreditCardInvoiceExternalId(billId),
            dueDate = LocalDate.parse(dueDate, dateFormat),
            billTotalAmount = billTotalAmount.amount.parseAsLong(),
            billMinimumAmount = billMinimumAmount.amount.parseAsLong(),
            billCurrency = billTotalAmount.currency,
            isInstalment = isInstalment,
            financeCharges = financeCharges?.map { it.toDomain() } ?: emptyList(),
            payments = payments.map { it.toDomain() },
            provider = OFProvider.INICIADOR,
        )
    }
}

val ONE_HUNDRED = BigDecimal(100)
private fun String.parseAsLong(): Long {
    return BigDecimal(this).times(ONE_HUNDRED).toLong()
}

data class OFBillAmountTO(
    val amount: String,
    val currency: String,
)

data class OFFinanceChargesTO(
    val type: String,
    val additionalInfo: String,
    val amount: String,
    val currency: String,
) {
    fun toDomain(): CreditCardFinanceCharges {
        return CreditCardFinanceCharges(
            type = type,
            additionalInfo = additionalInfo,
            amount = amount.parseAsLong(),
            currency = currency,
        )
    }
}

data class OFPaymentTO(
    val valueType: String,
    val paymentDate: String,
    val paymentMode: String,
    val amount: String,
    val currency: String,
) {
    fun toDomain(): CreditCardPayment {
        return CreditCardPayment(
            valueType = valueType,
            paymentDate = LocalDate.parse(paymentDate, dateFormat),
            paymentMode = paymentMode,
            amount = amount.parseAsLong(),
            currency = currency,
        )
    }
}

data class OFCreditCardDataTO(
    val creditCardAccountId: String,
    val brandName: String,
    val companyCnpj: String,
    val name: String,
    val productType: String,
    val productAdditionalInfo: String? = null,
    val creditCardNetwork: String,
    val networkAdditionalInfo: String? = null,
) : WithRaw() {
    fun toDomain(dataConsentId: DataConsentId) = CreditCard(
        creditCardId = CreditCardId(),
        creditCardExternalId = CreditCardExternalId(creditCardAccountId),
        brandName = brandName,
        companyCnpj = companyCnpj,
        name = name,
        productType = productType,
        productAdditionalInfo = productAdditionalInfo,
        creditCardNetwork = creditCardNetwork,
        networkAdditionalInfo = networkAdditionalInfo,
        dataConsentId = dataConsentId,
        provider = OFProvider.INICIADOR,
    )
}