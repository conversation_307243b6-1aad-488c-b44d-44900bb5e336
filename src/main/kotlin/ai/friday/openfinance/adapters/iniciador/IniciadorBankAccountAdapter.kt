package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.openfinance.app.AccountSubType
import ai.friday.openfinance.app.AccountType
import ai.friday.openfinance.app.BankAccountAmount
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.CompletedAuthorisedPaymentType
import ai.friday.openfinance.app.CreditDebitType
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.TransactionPersonType
import ai.friday.openfinance.app.TransactionType
import ai.friday.openfinance.app.integrations.BankAccountAdapter
import ai.friday.openfinance.app.integrations.KmsService
import ai.friday.openfinance.app.integrations.OFBankAccount
import ai.friday.openfinance.app.integrations.OFBankAccountBalance
import ai.friday.openfinance.app.integrations.OFBankAccountData
import ai.friday.openfinance.app.integrations.OFBankAccountLimits
import ai.friday.openfinance.app.integrations.OFBankAccountTransaction
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory

@Singleton
class IniciadorBankAccountAdapter(
    override val iniciadorAuthenticationManager: IniciadorAuthenticationManager,
    @param:Client(value = "\${integrations.iniciador.host}") override val httpClient: RxHttpClient,
    override val kmsService: KmsService,
) : BankAccountAdapter, IniciadorAdapter(httpClient, iniciadorAuthenticationManager, kmsService) {
    override fun getBankAccounts(dataConsentId: DataConsentId): Either<Exception, List<OFBankAccount>> {
        return handleRequest<OFBankAccountTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/accounts",
            markersMap = mapOf("dataConsentId" to dataConsentId.value),
            logName = "IniciadorBankAccountAdapter#getBankAccounts",
        ).map { list ->
            list.map {
                it.toDomain()
            }
        }
    }

    override fun getBankAccountData(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountData> {
        return handleRequest<OFBankAccountDataTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/accounts/${bankAccountId.value}",
            markersMap = mapOf("dataConsentId" to dataConsentId.value, "bankAccountId" to bankAccountId.value),
            logName = "IniciadorBankAccountAdapter#getBankAccountData",
        ).fold(
            ifRight = { it.first().toDomain(bankAccountId).right() },
            ifLeft = { it.left() },
        )
    }

    override fun getBankAccountBalance(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountBalance> {
        return handleRequest<OFBankAccountBalanceTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/accounts/${bankAccountId.value}/balances",
            markersMap = mapOf("dataConsentId" to dataConsentId.value, "bankAccountId" to bankAccountId.value),
            logName = "IniciadorBankAccountAdapter#getBankAccountBalance",
        ).fold(
            ifRight = { it.first().toDomain().right() },
            ifLeft = { it.left() },
        )
    }

    override fun getBankAccountLimits(dataConsentId: DataConsentId, bankAccountId: BankAccountId): Either<Exception, OFBankAccountLimits> {
        return handleRequest<OFBankAccountLimitsTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/accounts/${bankAccountId.value}/limits",
            markersMap = mapOf("dataConsentId" to dataConsentId.value, "bankAccountId" to bankAccountId.value),
            logName = "IniciadorBankAccountAdapter#getBankAccountLimits",
        ).fold(
            ifRight = { it.first().toDomain().right() },
            ifLeft = { it.left() },
        )
    }

    override fun getBankAccountTransactions(recentTransactions: Boolean, dataConsentId: DataConsentId, bankAccountId: BankAccountId, startDate: LocalDate?, endDate: LocalDate): Either<Exception, List<OFBankAccountTransaction>> {
        val endDateParameter = endDate.let { BrazilZonedDateTimeSupplier.formatWithBrazilTimeZone(it) }

        val (transactionParameter, startDateParameter) = if (recentTransactions) {
            Pair("transactions-current", BrazilZonedDateTimeSupplier.formatWithBrazilTimeZone(startDate ?: LocalDate.now().minusDays(6)))
        } else {
            Pair("transactions", BrazilZonedDateTimeSupplier.formatWithBrazilTimeZone(startDate ?: LocalDate.now().minusYears(1)))
        }

        val dateRange = "fromBookingDate=$startDateParameter&toBookingDate=$endDateParameter"
        return handleRequest<OFBankAccountTransactionTO>(
            uri = "/v1/data/links/${dataConsentId.value}/data/accounts/${bankAccountId.value}/$transactionParameter?$dateRange",
            markersMap = mapOf("dataConsentId" to dataConsentId.value, "bankAccountId" to bankAccountId.value),
            logName = "IniciadorBankAccountAdapter#getBankAccountTransactions",
        ).map { list ->
            list.map {
                it.toDomain()
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IniciadorBankAccountAdapter::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBankAccountTO(
    val type: AccountType,
    val compeCode: String,
    val branchCode: String?,
    val number: String,
    val checkDigit: String,
    val brandName: String,
    val accountId: String,
    val companyCnpj: String,
) : WithRaw() {
    fun toDomain() = OFBankAccount(
        bankAccountId = BankAccountId(accountId),
        type = type,
        compeCode = compeCode,
        branchCode = branchCode ?: "0001", // CONTA_PAGAMENTO_PRE_PAGA nao tem numero de agencia
        number = number,
        checkDigit = checkDigit,
        brandName = brandName,
        companyCnpj = companyCnpj,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBankAccountDataTO(
    val type: AccountType,
    val compeCode: String,
    val branchCode: String?,
    val number: String,
    val checkDigit: String,
    val subtype: AccountSubType,
    val currency: String,
) : WithRaw() {
    fun toDomain(bankAccountId: BankAccountId) = OFBankAccountData(
        bankAccountId = bankAccountId,
        type = type,
        compeCode = compeCode,
        branchCode = branchCode ?: "0001", // CONTA_PAGAMENTO_PRE_PAGA nao tem numero de agencia
        number = number,
        checkDigit = checkDigit,
        subtype = subtype,
        currency = currency,
        raw = raw,
    )
}

data class OFBankAccountAmountTO(
    val amount: String,
    val currency: String,
) {
    fun toDomain() = BankAccountAmount(
        amount = amount,
        currency = currency,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBankAccountBalanceTO(
    val availableAmount: OFBankAccountAmountTO,
    val blockedAmount: OFBankAccountAmountTO,
    val automaticallyInvestedAmount: OFBankAccountAmountTO,
    val updateDateTime: String,
) : WithRaw() {
    fun toDomain() = OFBankAccountBalance(
        availableAmount = availableAmount.toDomain(),
        blockedAmount = blockedAmount.toDomain(),
        automaticallyInvestedAmount = automaticallyInvestedAmount.toDomain(),
        updateDateTime = LocalDateTime.parse(updateDateTime, DateTimeFormatter.ISO_DATE_TIME).atZone(ZoneId.of("UTC")),
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBankAccountTransactionTO(
    val completedAuthorisedPaymentType: CompletedAuthorisedPaymentType,
    val partieBranchCode: String? = null,
    val transactionName: String,
    val transactionId: String,
    val partieCnpjCpf: String? = null,
    val type: TransactionType,
    val transactionDateTime: String,
    val partieCompeCode: String? = null,
    val transactionAmount: OFBankAccountAmountTO,
    val partiePersonType: TransactionPersonType? = null,
    val creditDebitType: CreditDebitType,
    val partieNumber: String? = null,
    val partieCheckDigit: String? = null,
) : WithRaw() {
    fun toDomain() = OFBankAccountTransaction(
        completedAuthorisedPaymentType = completedAuthorisedPaymentType,
        partieBranchCode = partieBranchCode,
        transactionName = transactionName,
        transactionId = transactionId,
        partieCnpjCpf = partieCnpjCpf,
        type = type,
        transactionDateTime = transactionDateTime,
        partieCompeCode = partieCompeCode,
        transactionAmount = transactionAmount.toDomain(),
        partiePersonType = partiePersonType,
        creditDebitType = creditDebitType,
        partieNumber = partieNumber,
        partieCheckDigit = partieCheckDigit,
        raw = raw,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBankAccountLimitsTO(
    val overdraftContractedLimit: OFBankAccountAmountTO?,
    val overdraftUsedLimit: OFBankAccountAmountTO?,
    val unarrangedOverdraftAmount: OFBankAccountAmountTO?,
) : WithRaw() {
    fun toDomain() = OFBankAccountLimits(
        overdraftContractedLimit = overdraftContractedLimit?.toDomain(),
        overdraftUsedLimit = overdraftUsedLimit?.toDomain(),
        unarrangedOverdraftAmount = unarrangedOverdraftAmount?.toDomain(),
    )
}