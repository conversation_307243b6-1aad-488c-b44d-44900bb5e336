package ai.friday.openfinance.adapters.iniciador.sweeping

import ai.friday.openfinance.adapters.iniciador.IniciadorSweepingAuthenticationManager
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.integrations.SweepingParticipantAdapter
import ai.friday.openfinance.app.utils.andAppend
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class IniciadorSweepingParticipantAdapter(
    private val authenticationManager: IniciadorSweepingAuthenticationManager,
    @param:Client(value = "\${integrations.iniciador.sweeping.host}") private val httpClient: RxHttpClient,
) : SweepingParticipantAdapter {

    @Cacheable(cacheNames = ["participants"])
    open override fun getParticipants(participantId: ParticipantId): Map<String, Any?> {
        try {
            val participant = getParticipantsList(participantId)
            return mapOf(
                "id" to participant.id,
                "name" to participant.name,
                "outage" to participant.outage,
                "isSweepingAvailable" to participant.isSweepingAvailable,
                "sweeping" to participant.sweeping,
            )
        } catch (_: Exception) {
            return emptyMap()
        }
    }

    fun getParticipantsList(participantId: ParticipantId): ParticipantTO {
        val logName = "IniciadorSweepingParticipantAdapter#getParticipantsList"
        val uri = "/v1/sweeping/participants?id=${participantId.value}"
        val markers = Markers.append("uri", uri)
        try {
            val token = authenticationManager.getToken()
            val httpRequest =
                HttpRequest.GET<ParticipantDataTO>(uri)
                    .header("Authorization", "Bearer $token")
                    .accept(MediaType.APPLICATION_JSON_TYPE)
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(ParticipantDataTO::class.java),
                )

            return call.firstOrError().blockingGet().data.first().also {
                LOG.info(markers, logName)
            }
        } catch (e: HttpClientResponseException) {
            markers.andAppend("status", e.status)
                .andAppend("response", e.response.body())
            LOG.error(markers, logName)
            throw e
        } catch (e: Exception) {
            markers.andAppend("error", e)
            LOG.error(markers, logName)
            throw e
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(IniciadorSweepingParticipantAdapter::class.java)
    }
}