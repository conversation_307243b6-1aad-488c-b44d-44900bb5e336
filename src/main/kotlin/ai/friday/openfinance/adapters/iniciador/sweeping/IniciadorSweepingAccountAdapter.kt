package ai.friday.openfinance.adapters.iniciador.sweeping

import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.dateFormat
import ai.friday.openfinance.adapters.api.PeriodicLimitTO
import ai.friday.openfinance.adapters.iniciador.IniciadorSweepingAuthenticationManager
import ai.friday.openfinance.adapters.iniciador.iniciadorDateTimeFormatOnly
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.integrations.CreateSweepingPaymentAdapterError
import ai.friday.openfinance.app.integrations.GetSweepingPaymentStatusError
import ai.friday.openfinance.app.integrations.LimitType
import ai.friday.openfinance.app.integrations.SweepingAccountAdapter
import ai.friday.openfinance.app.integrations.SweepingPaymentExternalId
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.sweepingaccount.AutomaticSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.CreateConsentRequest
import ai.friday.openfinance.app.sweepingaccount.CreateConsentResponse
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorRequest
import ai.friday.openfinance.app.sweepingaccount.CreateCreditorResponse
import ai.friday.openfinance.app.sweepingaccount.CreateSweepingPaymentRequest
import ai.friday.openfinance.app.sweepingaccount.CreditorAccountType
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.CreditorType
import ai.friday.openfinance.app.sweepingaccount.ManualSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.ParticipantCreditor
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimit
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimits
import ai.friday.openfinance.app.sweepingaccount.ScreenDimensions
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingDebtor
import ai.friday.openfinance.app.sweepingaccount.SweepingLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingParticipant
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentStatusError
import ai.friday.openfinance.app.sweepingaccount.SweepingPaymentStatusResponse
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignalsType
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import ai.friday.openfinance.app.utils.andAppend
import ai.friday.openfinance.app.utils.openFinanceDateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrElse
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class IniciadorSweepingAccountAdapter(
    private val authenticationManager: IniciadorSweepingAuthenticationManager,
    @param:Client(id = "iniciadorSweeping") private val httpClient: RxHttpClient,
    private val iniciadorSweepingParticipantAdapter: IniciadorSweepingParticipantAdapter,
) : SweepingAccountAdapter {

    @field:Property(name = "integrations.iniciador.sweeping.triggerId")
    lateinit var triggerId: String

    @field:Property(name = "sweeping.additionalInformation")
    lateinit var sweepingAdditionalInfo: String

    @field:Property(name = "sweeping.redirectUrl")
    lateinit var sweepingRedirectURL: String

    fun getRecurringTriggers(): List<RecurringTriggerTO> {
        val token = authenticationManager.getToken()

        val httpRequest =
            HttpRequest.GET<RecurringTriggerDataTO>("/v1/sweeping/recurring-triggers")
                .header("Authorization", "Bearer $token")
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(RecurringTriggerDataTO::class.java),
        )

        return call.firstOrError().blockingGet().data
    }

    override fun revokeConsent(consentId: SweepingConsentId): ConsentStatus {
        val logName = "IniciadorSweepingAccountAdapter#revokeConsent"
        val markers = Markers.append("consentId", consentId)

        val token = authenticationManager.getToken()
        try {
            val revokeConsentTO = RevokeConsentTO()
            val uri = "v1/sweeping/authorizations/${consentId.value}"

            markers.andAppend("revokeConsentTO", revokeConsentTO)
                .andAppend("uri", uri)

            val httpRequest = HttpRequest.PATCH(uri, revokeConsentTO)
                .header("Authorization", "Bearer $token")
                .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.exchange(
                httpRequest,
                Argument.of(RevokeConsentResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val sweepingCreditor = call.firstOrError().blockingGet().body()
            markers.andAppend("revokeConsentResponseTO", sweepingCreditor)
            LOG.info(markers, logName)
            return sweepingCreditor.status
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            }

            markers.andAppend("status", e.status)
                .andAppend("response", e.response.getBody(String::class.java).getOrNull())
            LOG.error(markers, logName, e)
            throw e
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            throw e
        }
    }

    fun getCreditors(): List<CreditorTO> {
        val token = authenticationManager.getToken()

        val httpRequest =
            HttpRequest.GET<CreditorDataTO>("/v1/sweeping/user-creditors")
                .header("Authorization", "Bearer $token")
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(CreditorDataTO::class.java),
            )

        return call.firstOrError().blockingGet().data
    }

    override fun createCreditor(request: CreateCreditorRequest): CreateCreditorResponse {
        val logName = "IniciadorSweepingAccountAdapter#createCreditor"
        val token = authenticationManager.getToken()
        val createCreditorTO = request.toCreateCreditorTO()
        val markers = Markers.append("creditor", createCreditorTO)
        try {
            val httpRequest = HttpRequest.POST("/v1/sweeping/user-creditors", createCreditorTO)
                .header("Authorization", "Bearer $token")
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(CreditorTO::class.java),
                Argument.of(String::class.java),
            )
            val sweepingCreditor = call.firstOrError().blockingGet().body().toSweepingCreditor()
            LOG.info(markers.andAppend("createdCreditor", sweepingCreditor), "IniciadorSweepingAccountAdapter#createCreditor")
            return sweepingCreditor
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            }

            markers.andAppend("status", e.status)
                .andAppend("response", e.response.body())
            LOG.error(markers, logName)
            throw e
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            throw e
        }
    }

    fun getCreditor(id: String): CreditorTO {
        val token = authenticationManager.getToken()

        val httpRequest = HttpRequest.GET<CreditorTO>("/v1/sweeping/user-creditors/$id")
            .header("Authorization", "Bearer $token")

        val call = httpClient.exchange(httpRequest, Argument.of(CreditorTO::class.java))
        return call.firstOrError().blockingGet().body()
    }

    override fun requestSweepingConsent(request: CreateConsentRequest): CreateConsentResponse {
        val logName = "IniciadorSweepingAccountAdapter#requestSweepingConsent"
        val markers = Markers.append("request", request)

        val token = authenticationManager.getToken()
        val participant = iniciadorSweepingParticipantAdapter.getParticipants(request.participantId)
        markers.andAppend("participantName", participant["name"])
        markers.andAppend("participant", participant)
        try {
            val createSweepingConsentTO = RequestSweepingConsentRequestTO(
                user = UserConsentTO(request.user.name, request.user.taxId.value),
                participantId = request.participantId.value,
                creditors = request.creditors.map { it.value },
                configuration = request.sweepingLimits.toConsentConfigurationTO(),
                triggers = listOf(triggerId),
                additionalInformation = sweepingAdditionalInfo,
                redirectURL = "$sweepingRedirectURL/${request.participantId.value}",
            )
            markers.andAppend("createConsent", createSweepingConsentTO)

            val httpRequest = HttpRequest.POST(
                "/v1/sweeping/authorizations",
                createSweepingConsentTO,
            )
                .bearerAuth(token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(httpRequest, Argument.of(SweepingConsentResponseTO::class.java), Argument.of(String::class.java))
            val consent = call.firstOrError().blockingGet().body()

            LOG.info(markers.andAppend("consent", consent), logName)
            return consent.toCreateConsentResponse(request.sweepingLimits)
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            }

            LOG.error(
                markers.andAppend("status", e.status)
                    .andAppend("response", e.response.getBody(String::class.java)),
                logName,
            )
            throw e
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            throw e
        }
    }

    private fun SweepingRiskSignals.toSweepingRiskSignalsTO(): SweepingRiskSignalsTO {
        return when (this) {
            is AutomaticSweepingRiskSignals -> AutomaticSweepingRiskSignalsTO(
                lastLoginDateTime = lastLoginDateTime.format(iniciadorDateTimeFormatOnly),
                pixKeyRegistrationDateTime = pixKeyRegistrationDateTime?.format(iniciadorDateTimeFormatOnly),
            )
            is ManualSweepingRiskSignals -> ManualSweepingRiskSignalsTO(
                deviceId = deviceId,
                isRootedDevice = isRootedDevice,
                screenBrightness = screenBrightness,
                elapsedTimeSinceBoot = elapsedTimeSinceBoot?.toMillis(),
                osVersion = osVersion,
                userTimeZoneOffset = userTimeZoneOffset,
                language = language,
                screenDimensions = screenDimensions?.toScreenDimensionsTO(),
                accountTenure = accountTenure?.format(dateFormat),
            )
        }
    }

    private fun ScreenDimensions.toScreenDimensionsTO() = ScreenDimensionsTO(
        width = width,
        height = height,
    )

    override fun createPayment(request: CreateSweepingPaymentRequest): Either<CreateSweepingPaymentAdapterError, SweepingPaymentResponse> {
        val logName = "IniciadorSweepingAccountAdapter#createPayment"
        val markers = Markers.append("request", request)

        val token = authenticationManager.getToken()

        try {
            val uri = "/v1/sweeping/authorizations/${request.consentId.value}/payments"
            markers.andAppend("uri", uri)

            val requestTO = CreateSweepingPaymentTO(
                amount = request.amount,
                description = request.description,
                creditorId = request.creditorId.value,
                triggerId = triggerId,
                externalId = request.id.value,
                riskSignals = request.riskSignals.toSweepingRiskSignalsTO(),
            )
            markers.andAppend("requestTO", requestTO)

            val httpRequest = HttpRequest.POST(uri, requestTO)
                .bearerAuth(token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(httpRequest, Argument.of(SweepingPaymentResponseTO::class.java), Argument.of(String::class.java))

            val responseTO = call.firstOrError().blockingGet().body()
            LOG.info(markers.andAppend("responseTO", responseTO), logName)
            return responseTO.toSweepingPaymentResponse(request.id, request.riskSignals).right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            }

            val responseBody = e.response.getBody(String::class.java).getOrElse { "" }
            markers.andAppend("status", e.status)
                .andAppend("response", responseBody)

            if (responseBody.contains("limit exceeded", ignoreCase = true)) {
                LOG.warn(markers, logName)
                val limitType = when {
                    responseBody.contains("Day", ignoreCase = true) -> LimitType.DAILY
                    responseBody.contains("Week", ignoreCase = true) -> LimitType.WEEKLY
                    responseBody.contains("Month", ignoreCase = true) -> LimitType.MONTHLY
                    responseBody.contains("Year", ignoreCase = true) -> LimitType.YEARLY
                    responseBody.contains("Global Amount", ignoreCase = true) -> LimitType.GLOBAL
                    responseBody.contains("Global Transaction", ignoreCase = true) -> LimitType.TRANSACTION
                    else -> LimitType.UNKNOWN
                }
                return CreateSweepingPaymentAdapterError.LimitExceededError(limitType).left()
            }

            LOG.error(markers, logName, e)
            return if (responseBody.contains("invalid status", ignoreCase = true)) {
                CreateSweepingPaymentAdapterError.InvalidConsentStatus.left()
            } else {
                CreateSweepingPaymentAdapterError.GenericError(e).left()
            }
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return CreateSweepingPaymentAdapterError.GenericError(e).left()
        }
    }

    override fun getPayments(authId: String): String {
        val token = authenticationManager.getToken()

        val httpRequest = HttpRequest.GET<String>("/v1/sweeping/authorizations/$authId/payments")
            .bearerAuth(token)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(httpRequest, Argument.of(String::class.java))

        return call.firstOrError().blockingGet()
    }

    override fun getPaymentStatus(consentId: SweepingConsentId, paymentRequestId: SweepingPaymentRequestId): Either<GetSweepingPaymentStatusError, SweepingPaymentStatusResponse> {
        val logName = "IniciadorSweepingAccountAdapter#getPaymentStatus"
        val markers = Markers.append("consentId", consentId.value)
            .andAppend("paymentRequestId", paymentRequestId.value)

        val token = authenticationManager.getToken()

        try {
            val uri = "/v1/sweeping/authorizations/${consentId.value}/payments/${paymentRequestId.value}/status"
            markers.andAppend("uri", uri)

            val httpRequest = HttpRequest.GET<String>(uri)
                .bearerAuth(token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(httpRequest, Argument.of(SweepingPaymentStatusResponseTO::class.java))

            val responseTO = call.firstOrError().blockingGet()
            markers.andAppend("responseTO", responseTO)

            return responseTO.toSweepingPaymentStatusResponse(paymentRequestId).right()
        } catch (e: HttpClientResponseException) {
            markers.andAppend("status", e.status).andAppend("response", e.response.body())
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            } else if (e.status == HttpStatus.NOT_FOUND) {
                LOG.warn(markers, logName, e)
                return GetSweepingPaymentStatusError.PaymentNotFound(e.localizedMessage).left()
            }
            LOG.error(markers, logName, e)
            return GetSweepingPaymentStatusError.GenericError(e).left()
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return GetSweepingPaymentStatusError.GenericError(e).left()
        }
    }

    override fun getConsentStatus(consentId: SweepingConsentId): ConsentStatus {
        return getConsent(consentId).status
    }

    override fun getConsent(consentId: SweepingConsentId): CreateConsentResponse {
        val logName = "IniciadorSweepingAccountAdapter#getConsent"
        val markers = Markers.append("consentId", consentId)
        val token = authenticationManager.getToken()
        try {
            val httpRequest = HttpRequest.GET<String>("/v1/sweeping/authorizations/${consentId.value}")
                .bearerAuth(token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(httpRequest, Argument.of(SweepingConsentResponseTO::class.java), Argument.of(String::class.java))
            val consent = call.firstOrError().blockingGet().body()

            LOG.info(markers.andAppend("consent", consent), logName)

            return consent.toCreateConsentResponse(null)
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                authenticationManager.cleanTokens()
            }

            LOG.error(markers.andAppend("status", e.status).andAppend("response", e.response.body()), logName, e)
            throw e
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            throw e
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(IniciadorSweepingAccountAdapter::class.java)
    }
}

fun CreditorTO.toSweepingCreditor() = CreateCreditorResponse(
    id = CreditorId(this.id),
    type = this.type,
    userTaxId = Document(this.userTaxId),
    creditorTaxId = Document(this.creditorTaxId),
    name = this.name,
    ispb = this.ispb,
    issuer = this.issuer,
    number = this.number,
    accountType = this.accountType,
    status = this.status,
    createdAt = ZonedDateTime.parse(this.createdAt, openFinanceDateTimeFormat),
    updatedAt = ZonedDateTime.parse(this.updatedAt, openFinanceDateTimeFormat),
    participant = this.participant?.let { ParticipantCreditor(it.name) },
)

data class ParticipantDataTO(
    val data: List<ParticipantTO>,
    val cursor: CursorTO,
)

data class ParticipantVersionTO(
    val version: String? = null,
    val outage: Boolean,
    val outageReason: String? = null,
    val status: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ParticipantTO(
    val id: String,
    val name: String,
    val tradeName: String?,
    val description: String? = null,
    val personType: String?,
    val status: String?,
    val slug: String? = null,
    val avatar: String? = null,
    val outage: Boolean?,
    val outageReason: String? = null,
    val isInstantAvailable: Boolean?,
    val isScheduledAvailable: Boolean?,
    val isSweepingAvailable: Boolean?,
    val isEnrollmentAvailable: Boolean?,
    val scheduled: ParticipantVersionTO?,
    val sweeping: ParticipantVersionTO?,
    val instant: ParticipantVersionTO?,
    val enrollment: ParticipantVersionTO?,
)

data class RecurringTriggerDataTO(
    val data: List<RecurringTriggerTO>,
    val cursor: CursorTO,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class RecurringTriggerTO(
    val id: String,
    val type: String?,
    val status: String?,
    val description: String,
    val createdAt: String?,
    val updatedAt: String?,
)

data class CreditorDataTO(
    val data: List<CreditorTO>,
    val cursor: CursorTO,
)

data class RevokeConsentTO(
    val status: ConsentStatus = ConsentStatus.REVOKED,
    val rejection: RejectionTO =
        RejectionTO(
            rejectedBy = "INICIADORA",
            rejectedFrom = "INICIADORA",
            reason = RejectionReasonTO(code = "REJEITADO_USUARIO", detail = "Rejeitado pelo usuário"),
        ),
    val riskSignals: Map<String, String> = mapOf(
        "type" to "AUTOMATIC",
        "lastLoginDateTime" to "2024-10-07T14:54:00Z",
        "pixKeyRegistrationDateTime" to "2024-10-07T14:54:00Z",
    ),
)

data class RejectionTO(
    val rejectedBy: String,
    val rejectedFrom: String,
    val reason: RejectionReasonTO,
)

data class RejectionReasonTO(
    val code: String,
    val detail: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RevokeConsentResponseTO(
    val authorizationId: String,
    val type: String,
    val participantId: String,
    val status: ConsentStatus = ConsentStatus.REVOKED,
    val user: BusinessEntityTO?,
    val creditors: List<ConsentCreditorTO>,
    val additionalInformation: String? = null,
    val startDateTime: String? = null,
    val expirationDateTime: String? = null,
    val error: String? = null,
    val statusUpdateDateTime: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class CreateCreditorRequestTO(
    val type: CreditorType,
    val userTaxId: String,
    val creditorTaxId: String,
    val pixKey: String?,
    val name: String?,
    val ispb: String?,
    val issuer: String?,
    val number: String?,
    val accountType: CreditorAccountType?,
)

data class CreditorTO(
    val id: String,
    val type: CreditorType,
    val customerId: String,
    val userTaxId: String,
    val creditorTaxId: String,
    val name: String? = null,
    val ispb: String? = null,
    val issuer: String? = null,
    val number: String? = null,
    val accountType: CreditorAccountType? = null,
    val participant: ParticipantCreditorTO? = null,
    val status: String,
    val createdAt: String,
    val updatedAt: String,
)

fun PeriodicLimitsTO.toPeriodicLimits(): PeriodicLimits {
    return PeriodicLimits(
        day = PeriodicLimit(day.quantityLimit, day.transactionLimit),
        week = PeriodicLimit(week.quantityLimit, week.transactionLimit),
        month = PeriodicLimit(month.quantityLimit, month.transactionLimit),
        year = PeriodicLimit(year.quantityLimit, year.transactionLimit),
    )
}

fun ConsentResponseConfigurationTO?.toConsentConfiguration(defaultSweepingLimits: SweepingLimits?, authorizationId: String): SweepingLimits? {
    if (this == null) return defaultSweepingLimits ?: throw IllegalStateException("consent missing limits: $authorizationId")
    return SweepingLimits(
        totalAllowedAmount = this.totalAllowedAmount,
        transactionLimit = this.transactionLimit,
        periodicLimits = this.periodicLimits?.toPeriodicLimits() ?: defaultSweepingLimits?.periodicLimits ?: throw IllegalStateException("consent missing limits: $authorizationId"),
    )
}

fun ConsentResponsePeriodicLimitsTO.toPeriodicLimits(): PeriodicLimits {
    return PeriodicLimits(
        day = this.day.toPeriodicLimit(),
        week = this.week.toPeriodicLimit(),
        month = this.month.toPeriodicLimit(),
        year = this.year.toPeriodicLimit(),
    )
}

fun ConsentResponsePeriodicLimitTO.toPeriodicLimit(): PeriodicLimit {
    return PeriodicLimit(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

private fun SweepingLimits.toConsentConfigurationTO(): ConsentConfigurationTO {
    return ConsentConfigurationTO(
        totalAllowedAmount = this.totalAllowedAmount,
        transactionLimit = this.transactionLimit,
        periodicLimits = this.periodicLimits?.toPeriodicLimitsTO(),
    )
}

private fun PeriodicLimits?.toPeriodicLimitsTO(): PeriodicLimitsTO? {
    if (this == null) return null
    return PeriodicLimitsTO(
        day = DayPeriodicLimitTO(quantityLimit = this.day.quantityLimit, transactionLimit = this.day.transactionLimit),
        week = WeekPeriodicLimitTO(quantityLimit = this.week.quantityLimit, transactionLimit = this.week.transactionLimit),
        month = MonthPeriodicLimitTO(quantityLimit = this.month.quantityLimit, transactionLimit = this.month.transactionLimit),
        year = YearPeriodicLimitTO(quantityLimit = this.year.quantityLimit, transactionLimit = this.year.transactionLimit),
    )
}

private fun PeriodicLimit.toTO(): PeriodicLimitTO {
    return PeriodicLimitTO(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

fun CreateCreditorRequest.toCreateCreditorTO() = CreateCreditorRequestTO(
    type = this.type,
    userTaxId = this.userTaxId.value,
    creditorTaxId = this.creditorTaxId.value,
    pixKey = null,
    name = this.name,
    ispb = this.ispb,
    issuer = this.issuer,
    number = this.number,
    accountType = this.accountType,
)

fun SweepingConsentResponseTO.toCreateConsentResponse(defaultSweepingLimits: SweepingLimits?): CreateConsentResponse {
    return CreateConsentResponse(
        consentId = SweepingConsentId(this.authorizationId),
        participant = SweepingParticipant(id = this.participantId, name = this.participant.name, avatar = this.participant.avatar),
        status = this.status,
        authUrl = this.authUrl ?: "",
        user = UserConsent(Document(this.user.taxId), this.user.name),
        businessEntity = this.businessEntity?.let { UserConsent(Document(it.taxId), it.name) },
        startDateTime = this.startDateTime?.let { ZonedDateTime.parse(this.startDateTime, openFinanceDateTimeFormat) },
        expirationDateTime = this.expirationDateTime?.let { ZonedDateTime.parse(this.expirationDateTime, openFinanceDateTimeFormat) },
        additionalInformation = this.additionalInformation,
        statusUpdateDateTime = ZonedDateTime.parse(this.statusUpdateDateTime, openFinanceDateTimeFormat),
        createdAt = ZonedDateTime.parse(this.createdAt, openFinanceDateTimeFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, openFinanceDateTimeFormat),
        sweepingLimits = this.configuration?.toConsentConfiguration(defaultSweepingLimits, authorizationId),
        debtor = this.debtorAccount.toSweepingDebtor(),
    )
}

fun DebtorAccountTO?.toSweepingDebtor(): SweepingDebtor? {
    if (this == null || ispb == null || issuer == null || number == null || accountType == null) {
        return null
    }

    return SweepingDebtor(
        ispb = this.ispb,
        issuer = this.issuer,
        number = this.number,
        accountType = this.accountType,
    )
}

fun SweepingPaymentResponseTO.toSweepingPaymentResponse(requestId: SweepingPaymentRequestId, riskSignals: SweepingRiskSignals): SweepingPaymentResponse {
    return SweepingPaymentResponse(
        externalId = SweepingPaymentExternalId(this.id),
        createdAt = ZonedDateTime.parse(this.createdAt, openFinanceDateTimeFormat),
        date = BrazilZonedDateTimeSupplier.parseDate(this.date),
        status = this.status.sweepingPaymentStatus,
        description = this.description,
        userTaxId = this.user.taxId,
        businessEntityTaxId = this.businessEntity?.taxId,
        endToEndId = this.endToEndId,
        participantId = this.participantId,
        amount = this.amount,
        fee = this.fee,
        method = this.method,
        consentId = SweepingConsentId(this.consentId),
        riskSignals = riskSignals,
        updatedAt = ZonedDateTime.parse(this.createdAt, openFinanceDateTimeFormat),
        requestId = externalId?.let { SweepingPaymentRequestId(it) } ?: requestId,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingPaymentStatusResponseTO(
    val id: String,
    val status: IniciadorPaymentStatus,
    val error: SweepingPaymentResponseErrorTO? = null,
    val description: String,
    val date: String,
    val createdAt: String,
    val updatedAt: String?,
    val consentId: String,
    val endToEndId: String?,
    val amount: Long,
    val fee: Long?,
    val method: String?,
    val participantId: String,
    val businessEntityTaxId: String?,
    val userTaxId: String?,
) {
    fun toSweepingPaymentStatusResponse(paymentRequestId: SweepingPaymentRequestId): SweepingPaymentStatusResponse {
        return SweepingPaymentStatusResponse(
            requestId = paymentRequestId,
            externalId = SweepingPaymentExternalId(this.id),
            status = this.status.sweepingPaymentStatus,
            error = this.error?.let {
                SweepingPaymentStatusError(
                    code = it.code,
                    title = it.title,
                    detail = it.detail,
                )
            },
            description = description,
            date = BrazilZonedDateTimeSupplier.parseDate(this.date),
            createdAt = ZonedDateTime.parse(this.createdAt, openFinanceDateTimeFormat),
            updatedAt = this.updatedAt?.let { ZonedDateTime.parse(it, openFinanceDateTimeFormat) },
            consentId = SweepingConsentId(consentId),
            endToEndId = endToEndId,
            amount = amount,
            fee = fee,
            method = method,
            participantId = participantId,
            businessEntityTaxId = businessEntityTaxId,
            userTaxId = userTaxId,
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingPaymentResponseTO(
    val id: String,
    val createdAt: String,
    val date: String,
    val status: IniciadorPaymentStatus,
    val description: String,
    val clientId: String,
    val user: BusinessEntityTO,
    val businessEntity: BusinessEntityTO? = null,
    val customerId: String,
    val endToEndId: String,
    val participantId: String,
    val amount: Long,
    val fee: Long,
    val method: String,
    val consentId: String,
    val creditor: TransactionCreditorTO,
    val authorizationType: String,
    val externalId: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingPaymentResponseErrorTO(
    val code: String?,
    val title: String?,
    val detail: String?,
)

data class TransactionCreditorTO(
    val ispb: String,
    val issuer: String,
    val number: String,
    val accountType: String,
)

data class BusinessEntityTO(val taxId: String)

data class RequestSweepingConsentRequestTO(
    val user: UserConsentTO,
    val participantId: String,
    val creditors: List<String>,
    val triggers: List<String>,
    val startDateTime: String? = null,
    val expirationDateTime: String? = null, // se não for informado, o consentimento não expira
    val redirectURL: String,
    val redirectOnErrorURL: String? = null,
    val additionalInformation: String,
    val configuration: ConsentConfigurationTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingConsentResponseTO(
    val authorizationId: String,
    val participantId: String,
    val participant: ConsentParticipantTO,
    val status: ConsentStatus,
    val authUrl: String? = null,
    val user: UserConsentTO,
    val businessEntity: UserConsentTO? = null,
    val creditors: List<ConsentCreditorTO>,
    val debtorAccount: DebtorAccountTO? = null,
    val startDateTime: String? = null,
    val expirationDateTime: String? = null,
    val additionalInformation: String?,
    val configuration: ConsentResponseConfigurationTO?,
    val statusUpdateDateTime: String,
    val triggers: List<RecurringTriggerTO>,
    val createdAt: String,
    val updatedAt: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DebtorAccountTO(
    val ispb: String?,
    val issuer: String?,
    val number: String?,
    val accountType: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConsentResponseConfigurationTO(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: ConsentResponsePeriodicLimitsTO?,
)

data class ConsentResponsePeriodicLimitsTO(
    val day: ConsentResponsePeriodicLimitTO,
    val week: ConsentResponsePeriodicLimitTO,
    val month: ConsentResponsePeriodicLimitTO,
    val year: ConsentResponsePeriodicLimitTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConsentResponsePeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class ConsentCreditorTO(
    val taxId: String?,
    val name: String,
    val participant: ParticipantCreditorTO?,
)

data class ConsentParticipantTO(
    val name: String,
    val avatar: String?,
)

data class UserConsentTO(
    val name: String,
    val taxId: String,
)

data class ConsentConfigurationTO(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimitsTO?,
)

data class PeriodicLimitsTO(
    val day: DayPeriodicLimitTO,
    val week: WeekPeriodicLimitTO,
    val month: MonthPeriodicLimitTO,
    val year: YearPeriodicLimitTO,
)

data class DayPeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class WeekPeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class MonthPeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

data class YearPeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CreateSweepingPaymentTO(
    val amount: Long,
    val description: String,
    val creditorId: String,
    val triggerId: String,
    val externalId: String?,
    val riskSignals: SweepingRiskSignalsTO,
)

enum class PaymentMethod {
    PIX_MANU, PIX_DICT
}

data class ParticipantCreditorTO(val name: String)

data class CursorTO(val afterCursor: String?, val beforeCursor: String?)

sealed interface SweepingRiskSignalsTO {
    val type: SweepingRiskSignalsType
}

data class AutomaticSweepingRiskSignalsTO(
    val lastLoginDateTime: String,
    val pixKeyRegistrationDateTime: String?,
) : SweepingRiskSignalsTO {
    override val type = SweepingRiskSignalsType.AUTOMATIC
}

data class ManualSweepingRiskSignalsTO(
    val deviceId: String,
    val isRootedDevice: Boolean?,
    val screenBrightness: Number?,
    val elapsedTimeSinceBoot: Long?,
    val osVersion: String?,
    val userTimeZoneOffset: String?,
    val language: String?,
    val screenDimensions: ScreenDimensionsTO?,
    val accountTenure: String?,
) : SweepingRiskSignalsTO {
    override val type = SweepingRiskSignalsType.MANUAL
}

data class ScreenDimensionsTO(
    val height: Int,
    val width: Int,
)

enum class IniciadorPaymentStatus(val sweepingPaymentStatus: SweepingPaymentStatus) {
    STARTED(SweepingPaymentStatus.PROCESSING),
    ENQUEUED(SweepingPaymentStatus.PROCESSING),
    CONSENT_AWAITING_AUTHORIZATION(SweepingPaymentStatus.PROCESSING),
    CONSENT_AUTHORIZED(SweepingPaymentStatus.PROCESSING),
    CONSENT_REJECTED(SweepingPaymentStatus.FAILED),
    PAYMENT_PENDING(SweepingPaymentStatus.PROCESSING),
    PAYMENT_PARTIALLY_ACCEPTED(SweepingPaymentStatus.PROCESSING),
    PAYMENT_SETTLEMENT_PROCESSING(SweepingPaymentStatus.PROCESSING),
    PAYMENT_SETTLEMENT_DEBTOR_ACCOUNT(SweepingPaymentStatus.PROCESSING),
    PAYMENT_COMPLETED(SweepingPaymentStatus.SUCCESS),
    PAYMENT_REJECTED(SweepingPaymentStatus.FAILED),
    CANCELED(SweepingPaymentStatus.FAILED),
    ERROR(SweepingPaymentStatus.FAILED),
    EXPIRED(SweepingPaymentStatus.FAILED),
    PAYMENT_SCHEDULED(SweepingPaymentStatus.PROCESSING),
}