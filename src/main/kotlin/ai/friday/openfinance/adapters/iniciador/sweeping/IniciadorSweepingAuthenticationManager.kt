package ai.friday.openfinance.adapters.iniciador

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.openfinance.app.utils.andAppend
import com.fasterxml.jackson.annotation.JsonProperty
import com.nimbusds.jwt.JWTParser
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import javax.security.auth.login.LoginException
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class IniciadorSweepingAuthenticationManager(
    @param:Client(value = "\${integrations.iniciador.sweeping.host}") private val httpClient: RxHttpClient,
    private val configuration: IniciadorSweepingConfiguration,
) {

    private var token: String? = null
    private var expiresAt: ZonedDateTime? = null
    private val expireOffset = 1L

    fun getToken(): String = if (tokenValid()) {
        token
    } else {
        null
    } ?: getAccessToken()

    private fun tokenValid() = expiresAt?.let { it.minusMinutes(expireOffset) > getZonedDateTime() } ?: false

    private fun getAccessToken(): String {
        val logName = "IniciadorAuthenticationManager#getAccessToken"
        return try {
            val httpRequest = HttpRequest.POST(
                configuration.accessTokenPath,
                mapOf("clientId" to configuration.clientId, "clientSecret" to configuration.clientSecret),
            )
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(SweepingAccessTokenTO::class.java),
            )
            val response = call.firstOrError().blockingGet()
            token = response.accessToken
            expiresAt = response.parseExpirationTime()

            val markers = Markers.append("expiresAt", expiresAt.toString())
            logger.info(markers, logName)
            response.accessToken
        } catch (e: Exception) {
            val markers = Markers.append("error", "Error on Iniciador access token API")
            logger.error(markers, logName, e)
            cleanTokens()
            throw LoginException("Error on Iniciador access token API")
        }
    }

    private fun SweepingAccessTokenTO.parseExpirationTime(): ZonedDateTime? {
        val logName = "IniciadorAuthenticationManager#parseExpirationTime"
        val markers = Markers.append("accessTokenTO", this)
        return try {
            val jwtClaimsSet = JWTParser.parse(accessToken).jwtClaimsSet

            markers.andAppend("issueTime", jwtClaimsSet.issueTime.toString())
            markers.andAppend("expirationTime", jwtClaimsSet.expirationTime.toString())

            val tokenDuration = Duration.between(jwtClaimsSet.issueTime.toInstant(), jwtClaimsSet.expirationTime.toInstant())
            markers.andAppend("tokenDuration", tokenDuration)

            val expiresAt = getZonedDateTime().plusSeconds(tokenDuration.toSeconds())
            markers.andAppend("expiresAt", expiresAt.toString())

            logger.info(markers, logName)
            expiresAt
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            null
        }
    }

    fun cleanTokens() {
        token = null
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IniciadorAuthenticationManager::class.java)
    }
}

data class SweepingAccessTokenTO(
    @JsonProperty("accessToken") val accessToken: String,
)

@ConfigurationProperties(value = "integrations.iniciador.sweeping")
data class IniciadorSweepingConfiguration @ConfigurationInject constructor(
    val accessTokenPath: String,
    val clientId: String,
    val clientSecret: String,
)