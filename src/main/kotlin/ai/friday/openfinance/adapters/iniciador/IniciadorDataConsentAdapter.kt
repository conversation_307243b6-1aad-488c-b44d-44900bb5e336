package ai.friday.openfinance.adapters.iniciador

import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentLink
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.OpenFinanceDataConsentStatus
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.connection.ExternalLink
import ai.friday.openfinance.app.integrations.DataConsentAdapter
import ai.friday.openfinance.app.utils.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class IniciadorDataConsentAdapter(
    private val iniciadorAuthenticationManager: IniciadorAuthenticationManager,
    @param:Client(value = "\${integrations.iniciador.host}") private val httpClient: RxHttpClient,
) : DataConsentAdapter {

    @field:Property(name = "data.redirectUrl")
    lateinit var dataRedirectURL: String

    override fun requestDataConsent(
        externalId: DataConsentRequestId,
        userTaxId: Document,
        participantId: ParticipantId,
        permissions: List<DataConsentPermission>,
    ): Either<Exception, DataConsentLink> {
        val logName = "IniciadorLinkAdapter#requestDataConsent"
        val markers = Markers.append("externalId", externalId.value)
            .andAppend("userTaxId", userTaxId.value)
            .andAppend("participantId", participantId.value)

        val requestBody = RequestDataConsentRequestTO(
            externalId = externalId.value,
            userTaxId = userTaxId.value,
            permissions = permissions,
            participantId = participantId.value,
            redirectURL = "$dataRedirectURL/${participantId.value}",
        )

        val httpRequest = HttpRequest.POST(
            "/v1/data/consents",
            requestBody,
        )
            .header("Authorization", iniciadorAuthenticationManager.getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        markers.andAppend("requestBody", requestBody)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(RequestDataConsentResponseTO::class.java),
        )

        return try {
            val response = call.firstOrError().blockingGet()
            markers.andAppend("response", response)
            logger.info(markers, logName)
            DataConsentLink(DataConsentId(response.id), response.consentRedirectURL).right()
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(
                markers.andAppend("response", response).andAppend("status", e.response.status.code),
                logName,
                e,
            )
            e.left()
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            e.left()
        }
    }

    override fun revokeDataConsent(dataConsentId: DataConsentId): Either<Exception, Unit> {
        val logName = "IniciadorLinkAdapter#revokeDataConsent"
        val markers = Markers.append("dataConsentId", dataConsentId.value)

        val httpRequest = HttpRequest.DELETE<Unit>("/v1/data/links/${dataConsentId.value}/revoke")
            .header("Authorization", iniciadorAuthenticationManager.getToken())
            .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(String::class.java),
            )
            call.firstOrError().blockingGet()
            logger.info(markers, logName)
            Unit.right()
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(
                markers.andAppend("response", response).andAppend("status", e.response.status.code),
                logName,
                e,
            )
            e.left()
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            e.left()
        }
    }

    override fun getResources(dataConsentId: DataConsentId): String {
        /*
        curl --request GET \
     --url 'https://data.sandbox.iniciador.com.br/v1/data/links/id/data/resources?page=1&page-size=100' \
     --header 'accept: application/json'

     id: fe589ffa-3d97-4ec8-a349-1f75912a037b
         */

        val token = iniciadorAuthenticationManager.getToken()

        val httpRequest =
            HttpRequest.GET<Unit>("/v1/data/links/${dataConsentId.value}/data/resources?page=1&page-size=100")
                .header("Authorization", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(String::class.java),
            )
        return call.firstOrError().blockingGet()
    }

    override fun getIdentifications(dataConsentId: DataConsentId): String {
        /*
        curl --request GET \
     --url 'https://data.sandbox.iniciador.com.br/v1/data/links/id/data/identifications?page=1&page-size=100' \
     --header 'accept: application/json'
         */

        val token = iniciadorAuthenticationManager.getToken()

        val httpRequest =
            HttpRequest.GET<Unit>("/v1/data/links/${dataConsentId.value}/data/identifications?page=1&page-size=100")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(String::class.java),
            )
        return call.firstOrError().blockingGet()
    }

    override fun getQualifications(dataConsentId: DataConsentId): String {
        TODO("Not yet implemented")
    }

    override fun getFinancialRelations(dataConsentId: DataConsentId): String {
        TODO("Not yet implemented")
    }

    override fun getLink(dataConsentId: DataConsentId): ExternalLink {
        // TODO melhorar o retorno e tratar exceções
        val token = iniciadorAuthenticationManager.getToken()

        val httpRequest =
            HttpRequest.GET<LinkCreatedTO>("/v1/data/links/${dataConsentId.value}")
                .header("Authorization", token)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(LinkCreatedTO::class.java),
        )

        return call.firstOrError().blockingGet().toExternalLink()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IniciadorDataConsentAdapter::class.java)
    }
}

data class LinkTO(
    val interfaceURL: String,
    val accessToken: String,
    val id: String,
)

data class RequestDataConsentRequestTO(
    val externalId: String,
    val userTaxId: String,
    val permissions: List<DataConsentPermission>,
    val participantId: String,
    val redirectURL: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class RequestDataConsentResponseTO(
    val id: String,
    val consentRedirectURL: String,
)

data class LinkCreatedTO(
    val id: String,
    val status: OpenFinanceDataConsentStatus,
    val redirectUrl: String,
    val externalId: String,
    val permissions: List<DataConsentPermission>,
    val permissionsGranted: List<DataConsentPermission>,
    val createdAt: String,
    val statusUpdatedAt: String,
    val bank: BankTO,
    val updatedAt: String?,
)

data class BankTO(
    val id: String,
    val name: String,
    val avatar: String,
)

fun LinkCreatedTO.toExternalLink(): ExternalLink {
    return ExternalLink(
        dataConsentId = DataConsentId(id),
        status = status.internalStatus,
        redirectUrl = redirectUrl,
        requestConsentId = DataConsentRequestId(externalId),
        bankId = bank.id,
        bankName = bank.name,
        permissions = permissions,
        permissionsGranted = permissionsGranted,
    )
}