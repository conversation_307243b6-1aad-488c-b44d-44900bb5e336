package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.DataConsent
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.DataConsentPermission
import ai.friday.openfinance.app.DataConsentRequestId
import ai.friday.openfinance.app.DataConsentStatus
import ai.friday.openfinance.app.ParticipantId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.DataConsentRepository
import ai.friday.openfinance.app.integrations.ItemNotFoundException
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private val DATA_CONSENT_PREFIX = "DATA-CONSENT-REQUEST" // TODO - foi feito assim porque estava sem apendar nada. ai dava clash

@Singleton
class DataConsentDbRepository(private val dynamoDbDAO: DataConsentDynamoDAO) : DataConsentRepository {

    override fun save(dataConsent: DataConsent): DataConsent {
        val currentTime = getZonedDateTime()

        val entity = DataConsentEntity().apply {
            partitionKey = dataConsent.id.value
            rangeKey = "OPEN_FINANCE_CONNECTION"
            gSIndex1PartitionKey = dataConsent.userAccountId.value
            gSIndex1RangeKey = "$DATA_CONSENT_PREFIX${dataConsent.requestId.value.removePrefix(DATA_CONSENT_PREFIX)}"
            gSIndex2PartitionKey = dataConsent.status.name
            gSIndex2RangeKey = "OPEN_FINANCE_CONNECTION_STATUS"
            status = dataConsent.status.name
            permissions = dataConsent.permissions.map { it.name }
            permissionsGranted = dataConsent.permissionsGranted.map { it.name }
            bankId = dataConsent.bankId
            participantId = dataConsent.participantId.value
            bankName = dataConsent.bankName
            createdAt = dataConsent.createdAt.format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
        }

        dynamoDbDAO.save(entity)

        return dataConsent.copy(updatedAt = currentTime)
    }

    override fun find(dataConsentId: DataConsentId): DataConsent {
        return dynamoDbDAO.findByPartitionKey(dataConsentId.value).firstOrNull()?.toOpenFinanceConnection() ?: throw ItemNotFoundException("Connection ${dataConsentId.value} not found")
    }

    override fun findByUserAccountId(userAccountId: UserAccountId): List<DataConsent> {
        return dynamoDbDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex1, userAccountId.value, DATA_CONSENT_PREFIX).map { it.toOpenFinanceConnection() }
    }

    override fun findAllByStatus(status: DataConsentStatus): List<DataConsent> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, status.name).map { it.toOpenFinanceConnection() }
    }

    private fun DataConsentEntity.toOpenFinanceConnection(): DataConsent {
        return DataConsent(
            userAccountId = UserAccountId(this.gSIndex1PartitionKey),
            id = DataConsentId(this.partitionKey),
            requestId = DataConsentRequestId(this.gSIndex1RangeKey),
            status = DataConsentStatus.valueOf(this.status),
            permissions = this.permissions.map { DataConsentPermission.valueOf(it) },
            permissionsGranted = this.permissionsGranted.map { DataConsentPermission.valueOf(it) },
            bankId = this.bankId,
            participantId = ParticipantId(this.participantId ?: this.bankId),
            bankName = this.bankName,
            createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
            updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        )
    }
}

@DynamoDbBean
class DataConsentEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // consentId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // userAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String // requestConsentId

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // OpenFinanceConnectionStatus

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2RangeKey: String

    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: String

    @get:DynamoDBAttribute(attributeName = "ParticipantId")
    var participantId: String? = null

    @get:DynamoDBAttribute(attributeName = "BankId")
    lateinit var bankId: String

    @get:DynamoDBAttribute(attributeName = "BankName")
    lateinit var bankName: String

    @get:DynamoDBAttribute(attributeName = "Permissions")
    var permissions: List<String> = listOf()

    @get:DynamoDBAttribute(attributeName = "PermissionsGranted")
    var permissionsGranted: List<String> = listOf()

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}