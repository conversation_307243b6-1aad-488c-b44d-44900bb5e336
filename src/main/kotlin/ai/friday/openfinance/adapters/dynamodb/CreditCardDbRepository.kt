package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCard
import ai.friday.openfinance.app.creditcard.CreditCardExternalId
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.integrations.CreditCardRepository
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class CreditCardDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<CreditCardEntity>(cli) {
    override fun args() = OPEN_FINANCE_TABLE_NAME to CreditCardEntity::class.java
}

private val RANGE_KEY_PREFIX = "CREDIT_CARD_ENTITY"

@Singleton
class CreditCardDbRepository(private val dynamoDbDAO: CreditCardDynamoDAO) : CreditCardRepository {

    override fun save(creditCard: CreditCard, userAccountId: UserAccountId): CreditCard {
        val entity = CreditCardEntity().apply {
            partitionKey = creditCard.creditCardId.value
            rangeKey = "$RANGE_KEY_PREFIX#${creditCard.provider.name}"
            gSIndex1PartitionKey = userAccountId.value
            gSIndex1ScanKey = RANGE_KEY_PREFIX
            gSIndex2PartitionKey = creditCard.dataConsentId.value
            gSIndex2ScanKey = RANGE_KEY_PREFIX
            creditCardId = creditCard.creditCardId.value
            creditCardExternalId = creditCard.creditCardExternalId.value
            this.userAccountId = userAccountId.value
            dataConsentId = creditCard.dataConsentId.value
            provider = creditCard.provider.name
            brandName = creditCard.brandName
            companyCnpj = creditCard.companyCnpj
            name = creditCard.name
            productType = creditCard.productType
            productAdditionalInfo = creditCard.productAdditionalInfo
            creditCardNetwork = creditCard.creditCardNetwork
            networkAdditionalInfo = creditCard.networkAdditionalInfo
            updatedAt = getZonedDateTime().format(dateTimeFormat)
        }
        dynamoDbDAO.save(entity)
        return entity.toOpenFinanceCreditCard()
    }

    override fun find(id: CreditCardId): Either<RepositoryError, CreditCard> {
        val result = dynamoDbDAO.findBeginsWithOnScanKey(id.value, RANGE_KEY_PREFIX).firstOrNull() ?: return RepositoryError.ItemNotFound("BankAccount $id not found").left()
        return result.toOpenFinanceCreditCard().right()
    }

    override fun findByConsentId(dataConsentId: DataConsentId): List<CreditCard> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, dataConsentId.value, RANGE_KEY_PREFIX).map { it.toOpenFinanceCreditCard() }
    }
}

private fun CreditCardEntity.toOpenFinanceCreditCard(): CreditCard {
    return CreditCard(
        creditCardId = CreditCardId(creditCardId),
        creditCardExternalId = CreditCardExternalId(creditCardExternalId),
        dataConsentId = DataConsentId(dataConsentId),
        brandName = brandName,
        companyCnpj = companyCnpj,
        name = name,
        productType = productType,
        productAdditionalInfo = productAdditionalInfo,
        creditCardNetwork = creditCardNetwork,
        networkAdditionalInfo = networkAdditionalInfo,
        provider = OFProvider.valueOf(provider),
    )
}

@DynamoDbBean
class CreditCardEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_PARTITION_KEY)
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_RANGE_KEY)
    var gSIndex1ScanKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_PARTITION_KEY)
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_RANGE_KEY)
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute("CreditCardId")
    lateinit var creditCardId: String

    @get:DynamoDbAttribute("CreditCardExternalId")
    lateinit var creditCardExternalId: String

    @get:DynamoDbAttribute("UserAccountId")
    lateinit var userAccountId: String

    @get:DynamoDbAttribute("DataConsentId")
    lateinit var dataConsentId: String

    @get:DynamoDbAttribute("Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute("BrandName")
    lateinit var brandName: String

    @get:DynamoDbAttribute("CompanyCnpj")
    lateinit var companyCnpj: String

    @get:DynamoDbAttribute("Name")
    lateinit var name: String

    @get:DynamoDbAttribute("ProductType")
    lateinit var productType: String

    @get:DynamoDbAttribute("ProductAdditionalInfo")
    var productAdditionalInfo: String? = null

    @get:DynamoDbAttribute("CreditCardNetwork")
    lateinit var creditCardNetwork: String

    @get:DynamoDbAttribute("NetworkAdditionalInfo")
    var networkAdditionalInfo: String? = null

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String
}