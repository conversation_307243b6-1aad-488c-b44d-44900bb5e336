package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.SweepingConsentRepository
import ai.friday.openfinance.app.sweepingaccount.ConsentStatus
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.PeriodicLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingConsent
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingDebtor
import ai.friday.openfinance.app.sweepingaccount.SweepingLimits
import ai.friday.openfinance.app.sweepingaccount.SweepingParticipant
import ai.friday.openfinance.app.sweepingaccount.UserConsent
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_NAME = "SWEEPING_CONSENT"

data class SweepingConsentKey(val sweepingConsentId: SweepingConsentId) :
    AbstractKey<SweepingConsentEntity>(partitionKey = sweepingConsentId.value, sortKey = ENTITY_NAME)

@Singleton
class SweepingConsentDbRepository(private val dynamoDbDAO: SweepingConsentDynamoDAO) : SweepingConsentRepository {

    override fun save(consent: SweepingConsent): SweepingConsent {
        val currentTime = getZonedDateTime()
        val objectMapper = getObjectMapper()

        val sweepingConsentEntity = SweepingConsentEntity().apply {
            partitionKey = consent.consentId.value
            rangeKey = ENTITY_NAME
            gSIndex1PartitionKey = consent.userAccountId.value
            gSIndex1ScanKey = "USER_ACCOUNT_ID"
            gSIndex2PartitionKey = consent.user.taxId.value
            gSIndex2ScanKey = "USER_TAX_ID"
            participantId = consent.participant.id
            participantName = consent.participant.name
            status = consent.status
            authUrl = consent.authUrl
            creditors = consent.creditors.map { it.value }
            startDateTime = consent.startDateTime?.format(dateTimeFormat)
            expirationDateTime = consent.expirationDateTime?.format(dateTimeFormat)
            additionalInformation = consent.additionalInformation
            userName = consent.user.name
            businessEntityTaxId = consent.businessEntity?.taxId?.value
            businessEntityName = consent.businessEntity?.name
            statusUpdateDateTime = consent.statusUpdateDateTime.format(dateTimeFormat)
            createdAt = consent.createdAt.format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
            configuration = objectMapper.writeValueAsString(consent.sweepingLimits.toSweepingLimitsEntity())
            debtor = consent.debtor?.let { objectMapper.writeValueAsString(it) }
        }

        dynamoDbDAO.save(sweepingConsentEntity)

        return consent.copy(updatedAt = currentTime)
    }

    override fun find(id: SweepingConsentId): Either<RepositoryError, SweepingConsent> {
        dynamoDbDAO.findByPrimaryKey(SweepingConsentKey(id))?.toSweepingConsent()?.let {
            return it.right()
        } ?: return RepositoryError.ItemNotFound("SweepingConsent $id not found").left()
    }
}

fun SweepingConsentEntity.toSweepingConsent(): SweepingConsent {
    return SweepingConsent(
        userAccountId = UserAccountId(this.gSIndex1PartitionKey),
        consentId = SweepingConsentId(this.partitionKey),
        participant = SweepingParticipant(this.participantId, this.participantName.orEmpty()),
        status = this.status,
        authUrl = this.authUrl,
        user = UserConsent(Document(this.gSIndex2PartitionKey), this.userName),
        businessEntity = if (this.businessEntityName != null && this.businessEntityTaxId != null) {
            UserConsent(
                taxId = Document(this.businessEntityTaxId!!),
                name = this.businessEntityName!!,
            )
        } else {
            null
        },
        creditors = this.creditors.map { CreditorId(it) },
        startDateTime = this.startDateTime?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        expirationDateTime = this.expirationDateTime?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        additionalInformation = this.additionalInformation,
        statusUpdateDateTime = ZonedDateTime.parse(this.statusUpdateDateTime, dateTimeFormat),
        createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        sweepingLimits = parseObjectFrom<SweepingLimitsEntity>(this.configuration).toDomain(),
        debtor = this.debtor?.let { parseObjectFrom<SweepingDebtor>(it) },
    )
}

@DynamoDbBean
class SweepingConsentEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // sweepingConsentId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String // status

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // userAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1ScanKey: String? = null // TODO

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // user.TaxId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    var gSIndex2ScanKey: String? = null // TODO

    @get:DynamoDBAttribute(attributeName = "ParticipantId")
    lateinit var participantId: String

    @get:DynamoDBAttribute(attributeName = "ParticipantName")
    var participantName: String? = null

    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: ConsentStatus

    @get:DynamoDBAttribute(attributeName = "AuthUrl")
    lateinit var authUrl: String

    @get:DynamoDBAttribute(attributeName = "Creditors")
    var creditors: List<String> = listOf()

    @get:DynamoDBAttribute(attributeName = "StartDateTime")
    var startDateTime: String? = null

    @get:DynamoDBAttribute(attributeName = "ExpirationDateTime")
    var expirationDateTime: String? = null

    @get:DynamoDBAttribute(attributeName = "AdditionalInformation")
    var additionalInformation: String? = null

    @get:DynamoDBAttribute(attributeName = "Username")
    lateinit var userName: String

    @get:DynamoDBAttribute(attributeName = "BusinessEntityTaxId")
    var businessEntityTaxId: String? = null

    @get:DynamoDBAttribute(attributeName = "BusinessEntityName")
    var businessEntityName: String? = null

    @get:DynamoDBAttribute(attributeName = "StatusUpdateDateTime")
    lateinit var statusUpdateDateTime: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDBAttribute(attributeName = "Configuration")
    lateinit var configuration: String

    @get:DynamoDBAttribute(attributeName = "Debtor")
    var debtor: String? = null
}

data class SweepingLimitsEntity(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimits?,
) {
    fun toDomain() = SweepingLimits(
        totalAllowedAmount = totalAllowedAmount,
        transactionLimit = transactionLimit,
        periodicLimits = periodicLimits ?: PeriodicLimits.ZERO,
    )
}

private fun SweepingLimits.toSweepingLimitsEntity() = SweepingLimitsEntity(
    totalAllowedAmount = totalAllowedAmount,
    transactionLimit = transactionLimit,
    periodicLimits = periodicLimits,
)