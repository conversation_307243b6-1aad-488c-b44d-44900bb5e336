package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCardBillAmount
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardTransaction
import ai.friday.openfinance.app.creditcard.CreditCardTransactionExternalId
import ai.friday.openfinance.app.integrations.CreditCardTransactionRepository
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.YearMonth
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class CreditCardTransactionDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<CreditCardTransactionEntity>(cli) {
    override fun args() = OPEN_FINANCE_TABLE_NAME to CreditCardTransactionEntity::class.java
}

private const val RANGE_KEY_PREFIX = "CREDIT_CARD_TRANSACTION_ENTITY"
private const val PRIMARY_KEY_PREFIX = "CREDIT-CARD-TRANSACTION"

@Singleton
class CreditCardTransactionDbRepository(
    private val dynamoDbDAO: CreditCardTransactionDynamoDAO,
) : CreditCardTransactionRepository {

    private fun buildPartitionKey(transactionExternalId: CreditCardTransactionExternalId, creditCardId: CreditCardId): String {
        return "$PRIMARY_KEY_PREFIX-${transactionExternalId.value}#${creditCardId.value}"
    }

    override fun save(transaction: CreditCardTransaction, userAccountId: UserAccountId, creditCardId: CreditCardId): CreditCardTransaction {
        val transactionDateTimeAsString = transaction.transactionDateTime.format(DateTimeFormatter.ISO_DATE_TIME)
        val entity = CreditCardTransactionEntity().apply {
            partitionKey = buildPartitionKey(transaction.transactionExternalId, creditCardId)
            rangeKey = "$RANGE_KEY_PREFIX#${transaction.provider.name}"
            gSIndex1PartitionKey = userAccountId.value
            gSIndex1ScanKey = "$RANGE_KEY_PREFIX#$transactionDateTimeAsString"
            gSIndex2PartitionKey = creditCardId.value
            gSIndex2ScanKey = "$RANGE_KEY_PREFIX#$transactionDateTimeAsString"
            this.creditCardId = creditCardId.value
            dataConsentId = transaction.dataConsentId.value
            transactionExternalId = transaction.transactionExternalId.value
            identificationNumber = transaction.identificationNumber
            transactionName = transaction.transactionName
            invoiceId = transaction.invoiceId?.value
            creditDebitType = transaction.creditDebitType
            transactionType = transaction.transactionType
            transactionalAdditionalInfo = transaction.transactionalAdditionalInfo
            paymentType = transaction.paymentType
            feeType = transaction.feeType
            feeTypeAdditionalInfo = transaction.feeTypeAdditionalInfo
            otherCreditsType = transaction.otherCreditsType
            otherCreditsAdditionalInfo = transaction.otherCreditsAdditionalInfo
            chargeIdentificator = transaction.chargeIdentificator
            chargeNumber = transaction.chargeNumber
            originalAmount = getObjectMapper().writeValueAsString(transaction.originalAmount)
            amount = transaction.amount
            transactionDateTime = transactionDateTimeAsString
            billPostDate = transaction.billPostDate
            payeeMCC = transaction.payeeMCC
            raw = transaction.raw
            provider = transaction.provider.name
            updatedAt = getZonedDateTime().format(dateTimeFormat)
        }
        dynamoDbDAO.save(entity)
        return transaction
    }

    override fun find(transactionExternalId: CreditCardTransactionExternalId, creditCardId: CreditCardId): Either<RepositoryError, CreditCardTransaction> {
        val result = dynamoDbDAO.findBeginsWithOnScanKey(buildPartitionKey(transactionExternalId, creditCardId), RANGE_KEY_PREFIX).firstOrNull()
            ?: return RepositoryError.ItemNotFound("CreditCardTransaction ${transactionExternalId.value} not found on cardId ${creditCardId.value}").left()

        return result.toCreditCardTransaction().right()
    }

    override fun findByCreditCardId(creditCardId: CreditCardId, yearMonth: YearMonth?): List<CreditCardTransaction> {
        val prefix = if (yearMonth != null) {
            "$RANGE_KEY_PREFIX#${yearMonth.year}-${yearMonth.monthValue.toString().padStart(2, '0')}"
        } else {
            RANGE_KEY_PREFIX
        }
        return dynamoDbDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex2, creditCardId.value, prefix).map { it.toCreditCardTransaction() }
    }
}

private fun CreditCardTransactionEntity.toCreditCardTransaction(): CreditCardTransaction {
    return CreditCardTransaction(
        transactionExternalId = CreditCardTransactionExternalId(transactionExternalId),
        dataConsentId = DataConsentId(dataConsentId),
        identificationNumber = identificationNumber,
        transactionName = transactionName,
        invoiceId = invoiceId?.let { CreditCardInvoiceId(it) },
        creditDebitType = creditDebitType,
        transactionType = transactionType,
        transactionalAdditionalInfo = transactionalAdditionalInfo,
        paymentType = paymentType,
        feeType = feeType,
        feeTypeAdditionalInfo = feeTypeAdditionalInfo,
        otherCreditsType = otherCreditsType,
        otherCreditsAdditionalInfo = otherCreditsAdditionalInfo,
        chargeIdentificator = chargeIdentificator,
        chargeNumber = chargeNumber,
        amount = amount,
        originalAmount = parseObjectFrom<CreditCardBillAmount>(originalAmount),
        transactionDateTime = ZonedDateTime.parse(transactionDateTime, DateTimeFormatter.ISO_DATE_TIME),
        billPostDate = billPostDate,
        payeeMCC = payeeMCC,
        raw = raw,
        provider = OFProvider.valueOf(provider),
    )
}

@DynamoDbBean
class CreditCardTransactionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_PARTITION_KEY)
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_RANGE_KEY)
    var gSIndex1ScanKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_PARTITION_KEY)
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_RANGE_KEY)
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute("CreditCardId")
    lateinit var creditCardId: String

    @get:DynamoDbAttribute("TransactionExternalId")
    lateinit var transactionExternalId: String

    @get:DynamoDbAttribute("IdentificationNumber")
    lateinit var identificationNumber: String

    @get:DynamoDbAttribute("TransactionName")
    lateinit var transactionName: String

    @get:DynamoDbAttribute("InvoiceId")
    var invoiceId: String? = null

    @get:DynamoDbAttribute("CreditDebitType")
    lateinit var creditDebitType: String

    @get:DynamoDbAttribute("TransactionType")
    lateinit var transactionType: String

    @get:DynamoDbAttribute("TransactionalAdditionalInfo")
    var transactionalAdditionalInfo: String? = null

    @get:DynamoDbAttribute("PayBentType")
    var paymentType: String? = null

    @get:DynamoDbAttribute("FeeType")
    var feeType: String? = null

    @get:DynamoDbAttribute("FeeTypeAdditionalInfo")
    var feeTypeAdditionalInfo: String? = null

    @get:DynamoDbAttribute("OtherCreditsType")
    var otherCreditsType: String? = null

    @get:DynamoDbAttribute("OtherCreditsAdditionalInfo")
    var otherCreditsAdditionalInfo: String? = null

    @get:DynamoDbAttribute("ChargeIdentificator")
    var chargeIdentificator: String? = null

    @get:DynamoDbAttribute("ChargeNumber")
    var chargeNumber: String? = null

    @get:DynamoDbAttribute("OriginalAmount")
    lateinit var originalAmount: String

    @get:DynamoDbAttribute("Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute("TransactionDateTime")
    lateinit var transactionDateTime: String

    @get:DynamoDbAttribute("BillPostDate")
    lateinit var billPostDate: String

    @get:DynamoDbAttribute("PayeeMCC")
    var payeeMCC: Int = 0

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute("DataConsentId")
    lateinit var dataConsentId: String

    @get:DynamoDbAttribute("Raw")
    var raw: String? = null

    @get:DynamoDbAttribute("Provider")
    lateinit var provider: String
}