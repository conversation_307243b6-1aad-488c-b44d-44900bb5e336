package ai.friday.openfinance.adapters.dynamodb

import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedResponse
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.ReturnValue

interface DynamoDAO<T> {
    fun args(): Pair<String, Class<T>>
}

abstract class AbstractDynamoDAO<T>(cli: DynamoDbEnhancedClient) : DynamoDAO<T> {
    private var table: DynamoDbTable<T>

    init {
        val (tableName, type) = this.args()

        table = cli.table(tableName, TableSchema.fromBean(type))
    }

    fun save(item: T) = table.putItem(item)

    fun saveWithResponse(item: T): PutItemEnhancedResponse<T> =
        table.putItemWithResponse(
            PutItemEnhancedRequest.builder<T>(item!!::class.java).item(item).returnValues(ReturnValue.ALL_OLD).build(),
        )

    fun findByPrimaryKey(key: AbstractKey<T>): T? = table.getItem(key)

    fun findByPrimaryKeyOnIndex(index: GlobalSecondaryIndexNames, partitionKey: String, sortKey: String): List<T> =
        table.getItemsByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyOnIndex(index: GlobalSecondaryIndexNames, partitionKey: String): List<T> =
        table.getItemsByIndexOnlyOnPrimaryKey(index, partitionKey)

    fun findLessThanOnIndex(index: GlobalSecondaryIndexNames, partitionKey: String, sortKey: String): List<T> =
        table.getItemsByIndexSortLessThan(index, partitionKey, sortKey)

    fun findByPartitionKeyOnMaxRangeKey(partitionKey: String): T? {
        return table.getItemByPartitionKey(partitionKey).singleOrNull()
    }

    fun findByPartitionKey(partitionKey: String): List<T> {
        return table.getItemByPartitionKey(partitionKey)
    }

    fun findBeginsWithOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.getItemsByIndexSortBeginsWith(index, partitionKey, sortKey)

    fun delete(partitionKey: String, sortKey: String): T =
        table.deleteItem(Key.builder().partitionValue(partitionKey).sortValue(sortKey).build())

    private fun <T> DynamoDbTable<T>.getItem(key: AbstractKey<T>): T? =
        this.item(key.partitionKey, key.sortKey)

    fun findBeginsWithOnScanKey(
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.queryTableOnHashKeyAndRangeKeyBeginsWith(partitionKey, sortKey)

    private fun <T> DynamoDbTable<T>.getItemsByIndex(index: GlobalSecondaryIndexNames, partitionKey: String, sortKey: String): List<T> =
        this.index(index.name)
            .query(partitionKey, sortKey)
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemsByIndexSortLessThan(index: GlobalSecondaryIndexNames, partitionKey: String, sortKey: String): List<T> =
        this.index(index.name)
            .query(
                QueryEnhancedRequest.builder()
                    .queryConditional(QueryConditional.sortLessThan(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                    .build(),
            )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemByPartitionKey(
        partitionKey: String,
    ): List<T> =
        this.query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey))).scanIndexForward(false).limit(1)
                .build(),
        ).first().items()

    fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeyBeginsWith(
        partitionKey: String?,
        scanKey: String?,
    ): List<T> {
        return this.query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith { it.partitionValue(partitionKey).sortValue(scanKey) }).build(),
        ).flatMap { it.items() }
    }

    private fun <T> DynamoDbTable<T>.getItemsByIndexSortBeginsWith(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        this.index(index.name)
            .query(
                QueryEnhancedRequest.builder()
                    .queryConditional(QueryConditional.sortBeginsWith(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                    .build(),
            )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemsByIndexOnlyOnPrimaryKey(index: GlobalSecondaryIndexNames, partitionKey: String): List<T> =
        this.index(index.name)
            .query(
                QueryEnhancedRequest.builder()
                    .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey))).scanIndexForward(false).limit(1)
                    .build(),
            )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.item(partitionKey: String, sortKey: String) =
        this.getItem(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun <T> DynamoDbIndex<T>.query(partitionKey: String, sortKey: String) =
        this.query(queryByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun queryByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
        QueryEnhancedRequest.builder()
            .queryConditional(conditionalByPartitionKeyAndSortKey(partitionKey, sortKey))
            .build()

    private fun conditionalByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
        QueryConditional.keyEqualTo(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun keyByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
        Key.builder()
            .partitionValue(partitionKey)
            .sortValue(sortKey)
            .build()

    private fun keyByPartitionKey(partitionKey: String) =
        Key.builder()
            .partitionValue(partitionKey)
            .build()

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractDynamoDAO::class.java)
    }
}

abstract class AbstractKey<T>(
    open val partitionKey: String,
    open val sortKey: String,
)