package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.adapters.parsers.parseObjectFrom
import ai.friday.openfinance.app.BankAccountAmount
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.BankTransactionMeta
import ai.friday.openfinance.app.CompletedAuthorisedPaymentType
import ai.friday.openfinance.app.TransactionId
import ai.friday.openfinance.app.integrations.BankTransactionRepository
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

data class BankTransactionKey(override val partitionKey: String, override val sortKey: String) :
    AbstractKey<BankTransactionEntity>(partitionKey = partitionKey, sortKey = sortKey)

@Singleton
class BankTransactionDbRepository(private val dynamoDbDAO: BankTransactionDynamoDAO) : BankTransactionRepository {

    override fun save(bankTransaction: BankTransactionMeta): BankTransactionMeta {
        val currentTime = getZonedDateTime()

        val bankTransactionEntity = BankTransactionEntity().apply {
            partitionKey = bankTransaction.transactionId.value
            rangeKey = bankTransaction.bankAccountId.value
            gSIndex1PartitionKey = bankTransaction.bankAccountId.value
            gSIndex1RangeKey = bankTransaction.transactionDate.format(dateFormat)
            gSIndex2PartitionKey = bankTransaction.bankAccountId.value
            gSIndex2RangeKey = bankTransaction.transactionStatus.name
            transactionId = bankTransaction.transactionId.value
            bankAccountId = bankTransaction.bankAccountId.value
            transactionDate = bankTransaction.transactionDate.format(dateTimeFormat)
            transactionStatus = bankTransaction.transactionStatus.name
            raw = bankTransaction.raw
            createdAt = bankTransaction.createdAt?.format(dateTimeFormat) ?: currentTime.format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
        }

        dynamoDbDAO.save(bankTransactionEntity)

        return bankTransactionEntity.toBankTransactionMeta()
    }

    override fun find(transactionId: String, bankAccountId: BankAccountId): Either<RepositoryError, BankTransactionMeta> {
        val key = BankTransactionKey(transactionId, bankAccountId.value)
        val result = dynamoDbDAO.findByPrimaryKey(key) ?: return RepositoryError.ItemNotFound("Transaction $key not found").left()
        return result.toBankTransactionMeta().right()
    }

    override fun findBankAccountTransactions(bankAccountId: BankAccountId): List<BankTransactionMeta> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(index = GlobalSecondaryIndexNames.GSIndex1, bankAccountId.value).map { it.toBankTransactionMeta() }
    }

    private fun BankTransactionEntity.toBankTransactionMeta(): BankTransactionMeta {
        return BankTransactionMeta(
            transactionStatus = CompletedAuthorisedPaymentType.valueOf(transactionStatus),
            bankAccountId = BankAccountId(bankAccountId),
            transactionId = TransactionId(transactionId),
            transactionDate = ZonedDateTime.parse(transactionDate, dateTimeFormat),
            raw = this.raw,
            createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
            updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        )
    }
}

@DynamoDbBean
class BankTransactionEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // transactionId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String // bankAccountId

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // bankAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String // transactionDate

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // bankAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2RangeKey: String // transactionStatus

    @get:DynamoDBAttribute(attributeName = "TransactionId")
    lateinit var transactionId: String

    @get:DynamoDBAttribute(attributeName = "BankAccountId")
    lateinit var bankAccountId: String

    @get:DynamoDBAttribute(attributeName = "TransactionDate")
    lateinit var transactionDate: String

    @get:DynamoDBAttribute(attributeName = "TransactionStatus")
    lateinit var transactionStatus: String

    @get:DynamoDBAttribute(attributeName = "Raw")
    lateinit var raw: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}

class BankAccountAmountConverter : AttributeConverter<BankAccountAmount> {
    private val objectMapper = getObjectMapper()

    override fun transformFrom(amount: BankAccountAmount): AttributeValue {
        return AttributeValue.builder().s(objectMapper.writeValueAsString(amount)).build()
    }

    override fun transformTo(attributeValue: AttributeValue?): BankAccountAmount {
        return attributeValue?.s()?.let { parseObjectFrom(it) }
            ?: throw IllegalArgumentException("Failed to convert to a list of institutions")
    }

    override fun type(): EnhancedType<BankAccountAmount> = EnhancedType.of(BankAccountAmount::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}