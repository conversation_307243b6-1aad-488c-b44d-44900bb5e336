package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.SweepingPayment
import ai.friday.openfinance.app.integrations.SweepingPaymentExternalId
import ai.friday.openfinance.app.integrations.SweepingPaymentRepository
import ai.friday.openfinance.app.integrations.SweepingPaymentRequestId
import ai.friday.openfinance.app.integrations.SweepingPaymentStatus
import ai.friday.openfinance.app.sweepingaccount.AutomaticSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.ManualSweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.ScreenDimensions
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignals
import ai.friday.openfinance.app.sweepingaccount.SweepingRiskSignalsType
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class SweepingPaymentDbRepository(private val dynamoDbDAO: SweepingPaymentDynamoDAO) : SweepingPaymentRepository {

    private val objectMapper = getObjectMapper()

    override fun save(payment: SweepingPayment): SweepingPayment {
        val currentTime = getZonedDateTime()

        val entity = SweepingPaymentEntity().apply {
            partitionKey = payment.requestId.value
            rangeKey = "SWEEPING_PAYMENT"
            gSIndex1PartitionKey = payment.userAccountId.value
            gSIndex1ScanKey = payment.createdAt.format(dateFormat)
            gSIndex2PartitionKey = payment.consentId.value
            gSIndex2ScanKey = "EXTERNAL_ID#" + payment.externalId?.value
            participantId = payment.participantId
            status = payment.status
            businessEntityTaxId = payment.businessEntityTaxId
            createdAt = payment.createdAt.format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
            description = payment.description
            amount = payment.amount
            creditorId = payment.creditorId.value
            endToEndId = payment.endToEndId
            date = payment.date?.format(dateFormat)
            fee = payment.fee
            method = payment.method
            riskSignals = objectMapper.writeValueAsString(payment.riskSignals)
            userTaxId = payment.userTaxId
            externalId = payment.externalId?.value
            error = payment.error
            errorDescription = payment.errorDescription
        }

        dynamoDbDAO.save(entity)

        return payment.copy(updatedAt = currentTime)
    }

    override fun find(id: SweepingPaymentExternalId): Either<RepositoryError, SweepingPayment> {
        TODO("not yet implemented")
    }

    fun findByConsentId(consentId: SweepingConsentId): List<SweepingPayment> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = consentId.value,
        ).mapNotNull {
            try {
                it.toSweepingPayment()
            } catch (e: Exception) {
                println("error mapping payment ${it.partitionKey}")
                null
            }
        }
    }

    override fun find(paymentRequestId: SweepingPaymentRequestId): Either<RepositoryError, SweepingPayment> {
        return dynamoDbDAO.findByPartitionKey(paymentRequestId.value).firstOrNull()?.toSweepingPayment()?.let {
            return it.right()
        } ?: return RepositoryError.ItemNotFound("SweepingPayment $paymentRequestId not found").left()
    }

    fun SweepingPaymentEntity.toSweepingPayment(): SweepingPayment {
        return SweepingPayment(
            requestId = SweepingPaymentRequestId(this.partitionKey),
            userAccountId = UserAccountId(this.gSIndex1PartitionKey),
            consentId = SweepingConsentId(this.gSIndex2PartitionKey),
            externalId = this.externalId?.let { SweepingPaymentExternalId(it) },
            status = this.status,
            createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
            updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
            description = this.description,
            amount = this.amount,
            creditorId = CreditorId(this.creditorId),
            endToEndId = this.endToEndId,
            date = this.date?.let { BrazilZonedDateTimeSupplier.parseDate(it) },
            fee = this.fee,
            method = this.method,
            riskSignals = objectMapper.readValue(this.riskSignals, SweepingRiskSignals::class.java),
            participantId = this.participantId,
            businessEntityTaxId = this.businessEntityTaxId,
            userTaxId = this.userTaxId,
            error = this.error,
            errorDescription = this.errorDescription,
        )
    }
}

@DynamoDbBean
class SweepingPaymentEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // requestId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDBAttribute(attributeName = "ExternalId")
    var externalId: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // userAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1ScanKey: String? = null // date

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // sweepingConsentId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    var gSIndex2ScanKey: String? = null // TODO

    @get:DynamoDBAttribute(attributeName = "Description")
    lateinit var description: String

    @get:DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @get:DynamoDBAttribute(attributeName = "CreditorId")
    lateinit var creditorId: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDBAttribute(attributeName = "EndToEndId")
    var endToEndId: String? = null

    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: SweepingPaymentStatus

    @get:DynamoDBAttribute(attributeName = "TransactionId")
    var transactionId: String? = null

    @get:DynamoDBAttribute(attributeName = "Date")
    var date: String? = null

    @get:DynamoDBAttribute(attributeName = "Fee")
    var fee: Long? = null

    @get:DynamoDBAttribute(attributeName = "Method")
    var method: String? = null

    @get:DynamoDBAttribute(attributeName = "BusinessEntityTaxId")
    var businessEntityTaxId: String? = null

    @get:DynamoDBAttribute(attributeName = "RiskSignals")
    lateinit var riskSignals: String

    @get:DynamoDBAttribute(attributeName = "UserTaxId")
    var userTaxId: String? = null

    @get:DynamoDBAttribute(attributeName = "ParticipantId")
    lateinit var participantId: String

    @get:DynamoDBAttribute(attributeName = "Error")
    var error: String? = null

    @get:DynamoDBAttribute(attributeName = "ErrorDescription")
    var errorDescription: String? = null
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "type",
    visible = false,
)
sealed interface SweepingRiskSignalsEntity {
    val type: SweepingRiskSignalsType
    fun toDomain(): SweepingRiskSignals
}

fun SweepingRiskSignals.toEntity(): SweepingRiskSignalsEntity {
    return when (this) {
        is AutomaticSweepingRiskSignals -> AutomaticSweepingRiskSignalsEntity(
            lastLoginDateTime = this.lastLoginDateTime.format(dateTimeFormat),
            pixKeyRegistrationDateTime = this.pixKeyRegistrationDateTime?.format(dateTimeFormat),
        )
        is ManualSweepingRiskSignals -> ManualSweepingRiskSignalsEntity(
            deviceId = this.deviceId,
            isRootedDevice = this.isRootedDevice,
            screenBrightness = this.screenBrightness,
            elapsedTimeSinceBoot = this.elapsedTimeSinceBoot?.toMillis(),
            osVersion = this.osVersion,
            userTimeZoneOffset = this.userTimeZoneOffset,
            language = this.language,
            screenDimensions = this.screenDimensions?.let {
                ScreenDimensionsEntity(
                    width = it.width,
                    height = it.height,
                )
            },
            accountTenure = this.accountTenure?.format(dateFormat),
        )
    }
}

@JsonTypeName("AUTOMATIC")
data class AutomaticSweepingRiskSignalsEntity(
    val lastLoginDateTime: String,
    val pixKeyRegistrationDateTime: String? = null,
) : SweepingRiskSignalsEntity {
    override val type = SweepingRiskSignalsType.AUTOMATIC

    override fun toDomain(): SweepingRiskSignals {
        return AutomaticSweepingRiskSignals(
            lastLoginDateTime = ZonedDateTime.parse(this.lastLoginDateTime, dateTimeFormat),
            pixKeyRegistrationDateTime = this.pixKeyRegistrationDateTime?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        )
    }
}

@JsonTypeName("MANUAL")
data class ManualSweepingRiskSignalsEntity(
    val deviceId: String,
    val isRootedDevice: Boolean? = null,
    val screenBrightness: Number? = null,
    val elapsedTimeSinceBoot: Long? = null,
    val osVersion: String? = null,
    val userTimeZoneOffset: String? = null,
    val language: String? = null,
    val screenDimensions: ScreenDimensionsEntity? = null,
    val accountTenure: String? = null,
) : SweepingRiskSignalsEntity {
    override val type = SweepingRiskSignalsType.MANUAL

    override fun toDomain(): SweepingRiskSignals {
        return ManualSweepingRiskSignals(
            deviceId = this.deviceId,
            isRootedDevice = this.isRootedDevice,
            screenBrightness = this.screenBrightness,
            elapsedTimeSinceBoot = this.elapsedTimeSinceBoot?.let { java.time.Duration.ofMillis(it) },
            osVersion = this.osVersion,
            userTimeZoneOffset = this.userTimeZoneOffset,
            language = this.language,
            screenDimensions = this.screenDimensions?.toDomain(),
            accountTenure = this.accountTenure.let { LocalDate.parse(it, dateFormat) },
        )
    }
}

@DynamoDBDocument
data class ScreenDimensionsEntity(
    val width: Int,
    val height: Int,
) {
    fun toDomain(): ScreenDimensions {
        return ScreenDimensions(width, height)
    }
}