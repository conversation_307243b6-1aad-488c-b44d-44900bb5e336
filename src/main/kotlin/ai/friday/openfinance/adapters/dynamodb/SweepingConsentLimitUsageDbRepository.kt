package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.integrations.SweepingConsentLimitUsageRepository
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentId
import ai.friday.openfinance.app.sweepingaccount.SweepingConsentLimitUsage
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.LocalDate
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_NAME = "SWEEPING_CONSENT_LIMIT_USAGE"

data class SweepingConsentLimitUsageKey(val sweepingConsentId: SweepingConsentId) :
    AbstractKey<SweepingConsentLimitUsageEntity>(partitionKey = sweepingConsentId.value, sortKey = ENTITY_NAME)

class SweepingConsentLimitUsageDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<SweepingConsentLimitUsageEntity>(cli) {
    override fun args() = OPEN_FINANCE_TABLE_NAME to SweepingConsentLimitUsageEntity::class.java
}

@Singleton
class SweepingConsentLimitUsageDbRepository(cli: DynamoDbEnhancedClient) : SweepingConsentLimitUsageRepository {

    private val dynamoDbDAO: SweepingConsentLimitUsageDynamoDAO = SweepingConsentLimitUsageDynamoDAO(cli)

    override fun save(limitUsage: SweepingConsentLimitUsage) {
        val currentTime = getZonedDateTime()

        val sweepingConsentEntity = SweepingConsentLimitUsageEntity().apply {
            partitionKey = limitUsage.consentId.value
            rangeKey = ENTITY_NAME

            dailyAmount = limitUsage.dailyAmount
            dailyQuantity = limitUsage.dailyQuantity
            dailyWindowStart = limitUsage.dailyWindowStart.format(dateFormat)
            dailyWindowEnd = limitUsage.dailyWindowEnd.format(dateFormat)
            weeklyAmount = limitUsage.weeklyAmount
            weeklyQuantity = limitUsage.weeklyQuantity
            weeklyWindowStart = limitUsage.weeklyWindowStart.format(dateFormat)
            weeklyWindowEnd = limitUsage.weeklyWindowEnd.format(dateFormat)
            monthlyAmount = limitUsage.monthlyAmount
            monthlyQuantity = limitUsage.monthlyQuantity
            monthlyWindowStart = limitUsage.monthlyWindowStart.format(dateFormat)
            monthlyWindowEnd = limitUsage.monthlyWindowEnd.format(dateFormat)
            yearlyAmount = limitUsage.yearlyAmount
            yearlyQuantity = limitUsage.yearlyQuantity
            yearlyWindowStart = limitUsage.yearlyWindowStart.format(dateFormat)
            yearlyWindowEnd = limitUsage.yearlyWindowEnd.format(dateFormat)
            totalAmount = limitUsage.totalAmount

            updatedAt = currentTime.format(dateTimeFormat)
        }

        dynamoDbDAO.save(sweepingConsentEntity)
    }

    override fun find(id: SweepingConsentId): Either<RepositoryError, SweepingConsentLimitUsage> {
        dynamoDbDAO.findByPrimaryKey(SweepingConsentLimitUsageKey(id))?.toSweepingConsentLimitUsage()?.let {
            return it.right()
        } ?: return RepositoryError.ItemNotFound("SweepingConsentLimitUsage $id not found").left()
    }
}

fun SweepingConsentLimitUsageEntity.toSweepingConsentLimitUsage(): SweepingConsentLimitUsage {
    return SweepingConsentLimitUsage(
        consentId = SweepingConsentId(partitionKey),
        dailyAmount = dailyAmount,
        dailyQuantity = dailyQuantity,
        dailyWindowStart = LocalDate.parse(dailyWindowStart, dateFormat),
        dailyWindowEnd = LocalDate.parse(dailyWindowEnd, dateFormat),
        weeklyAmount = weeklyAmount,
        weeklyQuantity = weeklyQuantity,
        weeklyWindowStart = LocalDate.parse(weeklyWindowStart, dateFormat),
        weeklyWindowEnd = LocalDate.parse(weeklyWindowEnd, dateFormat),
        monthlyAmount = monthlyAmount,
        monthlyQuantity = monthlyQuantity,
        monthlyWindowStart = LocalDate.parse(monthlyWindowStart, dateFormat),
        monthlyWindowEnd = LocalDate.parse(monthlyWindowEnd, dateFormat),
        yearlyAmount = yearlyAmount,
        yearlyQuantity = yearlyQuantity,
        yearlyWindowStart = LocalDate.parse(yearlyWindowStart, dateFormat),
        yearlyWindowEnd = LocalDate.parse(yearlyWindowEnd, dateFormat),
        totalAmount = totalAmount,
    )
}

@DynamoDbBean
class SweepingConsentLimitUsageEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // sweepingConsentId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDBAttribute(attributeName = "DailyAmount")
    var dailyAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "DailyQuantity")
    var dailyQuantity: Long = 0

    @get:DynamoDBAttribute(attributeName = "DailyWindowStart")
    lateinit var dailyWindowStart: String

    @get:DynamoDBAttribute(attributeName = "DailyWindowEnd")
    lateinit var dailyWindowEnd: String

    @get:DynamoDBAttribute(attributeName = "WeeklyAmount")
    var weeklyAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "WeeklyQuantity")
    var weeklyQuantity: Long = 0

    @get:DynamoDBAttribute(attributeName = "WeeklyWindowStart")
    lateinit var weeklyWindowStart: String

    @get:DynamoDBAttribute(attributeName = "WeeklyWindowEnd")
    lateinit var weeklyWindowEnd: String

    @get:DynamoDBAttribute(attributeName = "MonthlyAmount")
    var monthlyAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "MonthlyQuantity")
    var monthlyQuantity: Long = 0

    @get:DynamoDBAttribute(attributeName = "MonthlyWindowStart")
    lateinit var monthlyWindowStart: String

    @get:DynamoDBAttribute(attributeName = "MonthlyWindowEnd")
    lateinit var monthlyWindowEnd: String

    @get:DynamoDBAttribute(attributeName = "YearlyAmount")
    var yearlyAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "YearlyQuantity")
    var yearlyQuantity: Long = 0

    @get:DynamoDBAttribute(attributeName = "YearlyWindowStart")
    lateinit var yearlyWindowStart: String

    @get:DynamoDBAttribute(attributeName = "YearlyWindowEnd")
    lateinit var yearlyWindowEnd: String

    @get:DynamoDBAttribute(attributeName = "TotalAmount")
    var totalAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}