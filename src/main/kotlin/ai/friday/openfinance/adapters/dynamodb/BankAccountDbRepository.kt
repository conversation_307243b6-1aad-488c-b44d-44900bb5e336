package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.BankAccountId
import ai.friday.openfinance.app.BankAccountMeta
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.BankAccountRepository
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class BankAccountDbRepository(private val dynamoDbDAO: BankAccountDynamoDAO) : BankAccountRepository {

    override fun save(bankAccount: BankAccountMeta): BankAccountMeta {
        val currentTime = getZonedDateTime()

        val bankAccountEntity = BankAccountEntity().apply {
            partitionKey = bankAccount.bankAccountId.value
            rangeKey = bankAccount.bankCode
            gSIndex1PartitionKey = bankAccount.userAccountId.value
            gSIndex1ScanKey = "USER_ACCOUNT_ID"
            gSIndex2PartitionKey = bankAccount.dataConsentId.value
            gSIndex2ScanKey = "CONSENT_ID" // TODO - risco de colisão com outros repositories
            bankCode = bankAccount.bankCode
            raw = bankAccount.raw
            createdAt = (bankAccount.createdAt ?: currentTime).format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
        }

        dynamoDbDAO.save(bankAccountEntity)

        return bankAccount.copy(updatedAt = currentTime)
    }

    override fun find(id: BankAccountId): Either<RepositoryError, BankAccountMeta> {
        val result = dynamoDbDAO.findByPartitionKey(id.value).firstOrNull() ?: return RepositoryError.ItemNotFound("BankAccount $id not found").left()
        return result.toOpenFinanceBankAccountMeta().right()
    }

    override fun findByConsentId(dataConsentId: DataConsentId): List<BankAccountMeta> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, dataConsentId.value, "CONSENT_ID").map { it.toOpenFinanceBankAccountMeta() }
    }

    private fun BankAccountEntity.toOpenFinanceBankAccountMeta(): BankAccountMeta {
        return BankAccountMeta(
            bankAccountId = BankAccountId(this.partitionKey),
            userAccountId = UserAccountId(this.gSIndex1PartitionKey),
            dataConsentId = DataConsentId(this.gSIndex2PartitionKey),
            bankCode = this.bankCode,
            raw = this.raw,
            createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
            updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        )
    }
}

@DynamoDbBean
class BankAccountEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // bankAccountId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String // type

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // userAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1ScanKey: String? = null // TODO

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // consentId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDBAttribute(attributeName = "BankCode")
    lateinit var bankCode: String

    @get:DynamoDBAttribute(attributeName = "Raw")
    lateinit var raw: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}