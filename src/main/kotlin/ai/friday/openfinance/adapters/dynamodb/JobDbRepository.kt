package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.integrations.JobRepository
import ai.friday.openfinance.app.job.Job
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class JobDbRepository(private val dynamoDbDAO: JobDynamoDAO) : JobRepository {
    override fun findAll(): List<Job> {
        return dynamoDbDAO.findByPartitionKey("JOB")
            .map { entity ->
                Job(
                    name = entity.name,
                    crons = entity.crons,
                    fixedDelay = entity.fixedDelay?.let { Duration.parse(it) },
                    running = entity.running,
                    lastStartTime = entity.lastStartTime?.let {
                        ZonedDateTime.parse(
                            it,
                            dateTimeFormat,
                        )
                    },
                    lastElapsedMinutes = entity.lastElapsedMinutes,
                    shouldLock = entity.shouldLock,
                    lockAtLeastFor = Duration.parse(entity.lockAtLeastFor),
                    lockAtMostFor = Duration.parse(entity.lockAtMostFor),
                    shutdownGracefully = entity.shutdownGracefully,
                    shutdownGracefullyMaxWaitTime = entity.shutdownGracefullyMaxWaitTime,

                )
            }
    }

    override fun save(job: Job) {
        dynamoDbDAO.save(
            JobEntity().apply {
                primaryKey = "JOB"
                scanKey = job.name
                name = job.name
                crons = job.crons
                fixedDelay = job.fixedDelay?.toString()
                running = job.running
                lastStartTime = job.lastStartTime?.format(dateTimeFormat)
                lastElapsedMinutes = job.lastElapsedMinutes
                shouldLock = job.shouldLock
                lockAtLeastFor = job.lockAtLeastFor.toString()
                lockAtMostFor = job.lockAtMostFor.toString()
                shutdownGracefully = job.shutdownGracefully
                shutdownGracefullyMaxWaitTime = job.shutdownGracefullyMaxWaitTime
            },
        )
    }
}

@DynamoDbBean
class JobEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var primaryKey: String // JOB

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var scanKey: String // JOB-NAME

    @DynamoDBAttribute(attributeName = "RegisterType")
    var registerType: String = "Job"

    @DynamoDBAttribute(attributeName = "Name")
    lateinit var name: String

    @DynamoDBAttribute(attributeName = "Crons")
    lateinit var crons: List<String>

    @DynamoDBAttribute(attributeName = "FixedDelay")
    var fixedDelay: String? = null

    @DynamoDBAttribute(attributeName = "Running")
    var running: Boolean = false

    @DynamoDBAttribute(attributeName = "LastStartTime")
    var lastStartTime: String? = null

    @DynamoDBAttribute(attributeName = "LastElapsedMinutes")
    var lastElapsedMinutes: Long? = null

    @DynamoDBAttribute(attributeName = "ShouldLock")
    var shouldLock: Boolean = false

    @DynamoDBAttribute(attributeName = "LockAtLeastFor")
    lateinit var lockAtLeastFor: String

    @DynamoDBAttribute(attributeName = "LockAtMostFor")
    lateinit var lockAtMostFor: String

    @DynamoDBAttribute(attributeName = "ShutdownGracefully")
    var shutdownGracefully: Boolean = false

    @DynamoDBAttribute(attributeName = "ShutdownGracefullyMaxWaitTime")
    var shutdownGracefullyMaxWaitTime: Int = 0
}