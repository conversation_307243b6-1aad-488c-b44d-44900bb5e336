package ai.friday.openfinance.adapters.dynamodb

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.adapters.parsers.getObjectMapper
import ai.friday.openfinance.adapters.parsers.parseListFrom
import ai.friday.openfinance.app.DataConsentId
import ai.friday.openfinance.app.OFProvider
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.creditcard.CreditCardFinanceCharges
import ai.friday.openfinance.app.creditcard.CreditCardId
import ai.friday.openfinance.app.creditcard.CreditCardInvoice
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceExternalId
import ai.friday.openfinance.app.creditcard.CreditCardInvoiceId
import ai.friday.openfinance.app.creditcard.CreditCardPayment
import ai.friday.openfinance.app.integrations.CreditCardInvoiceRepository
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class CreditCardInvoiceDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<CreditCardInvoiceEntity>(cli) {
    override fun args() = OPEN_FINANCE_TABLE_NAME to CreditCardInvoiceEntity::class.java
}

private val RANGE_KEY_PREFIX = "CREDIT_CARD_INVOICE_ENTITY"

@Singleton
class CreditCardInvoiceDbRepository(
    private val dynamoDbDAO: CreditCardInvoiceDynamoDAO,
) : CreditCardInvoiceRepository {

    override fun save(invoice: CreditCardInvoice, dataConsentId: DataConsentId, userAccountId: UserAccountId, creditCardId: CreditCardId): CreditCardInvoice {
        val entity = CreditCardInvoiceEntity().apply {
            partitionKey = invoice.invoiceId.value
            rangeKey = "$RANGE_KEY_PREFIX#${invoice.provider.name}"
            gSIndex1PartitionKey = userAccountId.value
            gSIndex1ScanKey = RANGE_KEY_PREFIX
            gSIndex2PartitionKey = creditCardId.value
            gSIndex2ScanKey = RANGE_KEY_PREFIX
            this.creditCardId = creditCardId.value
            invoiceId = invoice.invoiceId.value
            invoiceExternalId = invoice.invoiceExternalId.value
            dueDate = invoice.dueDate.format(dateFormat)
            billTotalAmount = invoice.billTotalAmount
            billMinimumAmount = invoice.billMinimumAmount
            billCurrency = invoice.billCurrency
            isInstalment = invoice.isInstalment
            financeCharges = invoice.financeCharges.let { getObjectMapper().writeValueAsString(it) }
            payments = getObjectMapper().writeValueAsString(invoice.payments)
            updatedAt = getZonedDateTime().format(dateTimeFormat)
            provider = invoice.provider.name
        }
        dynamoDbDAO.save(entity)
        return invoice
    }

    override fun find(creditCardInvoiceId: CreditCardInvoiceId): Either<RepositoryError, CreditCardInvoice> {
        val result = dynamoDbDAO.findBeginsWithOnScanKey(creditCardInvoiceId.value, RANGE_KEY_PREFIX)
            .firstOrNull()
            ?: return RepositoryError.ItemNotFound("CreditCardInvoice $creditCardInvoiceId not found").left()

        return result.toCreditCardInvoice().right()
    }

    override fun findByCreditCardId(creditCardId: CreditCardId): List<CreditCardInvoice> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, creditCardId.value, RANGE_KEY_PREFIX).map { it.toCreditCardInvoice() }
    }
}

private fun CreditCardInvoiceEntity.toCreditCardInvoice(): CreditCardInvoice {
    return CreditCardInvoice(
        invoiceId = CreditCardInvoiceId(invoiceId),
        invoiceExternalId = CreditCardInvoiceExternalId(invoiceExternalId),
        dueDate = LocalDate.parse(dueDate, dateFormat),
        billTotalAmount = billTotalAmount,
        billMinimumAmount = billMinimumAmount,
        billCurrency = billCurrency,
        isInstalment = isInstalment,
        financeCharges = financeCharges?.let { parseListFrom<CreditCardFinanceCharges>(it) } ?: emptyList(),
        payments = payments?.let { parseListFrom<CreditCardPayment>(it) } ?: emptyList(),
        provider = OFProvider.valueOf(provider),
    )
}

@DynamoDbBean
class CreditCardInvoiceEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_PARTITION_KEY)
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_RANGE_KEY)
    var gSIndex1ScanKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_PARTITION_KEY)
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = INDEX_2_RANGE_KEY)
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute("InvoiceId")
    lateinit var invoiceId: String

    @get:DynamoDbAttribute("InvoiceExternalId")
    lateinit var invoiceExternalId: String

    @get:DynamoDbAttribute("CreditCardId")
    lateinit var creditCardId: String

    @get:DynamoDbAttribute("DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute("BillTotalAmount")
    var billTotalAmount: Long = 0

    @get:DynamoDbAttribute("BillCurrency")
    lateinit var billCurrency: String

    @get:DynamoDbAttribute("BillMinimumAmount")
    var billMinimumAmount: Long = 0

    @get:DynamoDbAttribute("IsInstalment")
    var isInstalment: Boolean = false

    @get:DynamoDbAttribute("FinanceCharges")
    var financeCharges: String? = null

    @get:DynamoDbAttribute("Payments")
    var payments: String? = null

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute("Provider")
    lateinit var provider: String
}