package ai.friday.openfinance.adapters.dynamodb

import com.amazonaws.ClientConfiguration
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

@Factory
class AmazonDynamoDBFactory(
    @Property(name = "aws.dynamodb.region") private val dynamodbRegion: String,
) {
    @Singleton
    fun getAmazonDynamoDb(): AmazonDynamoDB =
        AmazonDynamoDBClientBuilder.standard()
            .withClientConfiguration(ClientConfiguration().withMaxConnections(1000))
            .withRegion(dynamodbRegion).build()

    @Singleton
    fun getDynamoDbClient(): DynamoDbClient = DynamoDbClient.builder().region(Region.of(dynamodbRegion)).build()

    @Singleton
    fun getDynamoDbAsyncClient(): DynamoDbAsyncClient =
        DynamoDbAsyncClient.builder().region(Region.of(dynamodbRegion)).build()

    @Singleton
    fun dynamoDbEnhancedClient(): DynamoDbEnhancedClient {
        return DynamoDbEnhancedClient.builder()
            .dynamoDbClient(getDynamoDbClient())
            .build()
    }

    @Singleton
    fun dynamoDbEnhancedAsyncClient(): DynamoDbEnhancedAsyncClient {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(getDynamoDbAsyncClient())
            .build()
    }
}