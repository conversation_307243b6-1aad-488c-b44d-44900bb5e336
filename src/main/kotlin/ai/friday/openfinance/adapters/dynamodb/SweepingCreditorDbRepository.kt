package ai.friday.openfinance.adapters.dynamodb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.openfinance.app.Document
import ai.friday.openfinance.app.UserAccountId
import ai.friday.openfinance.app.integrations.SweepingCreditorRepository
import ai.friday.openfinance.app.sweepingaccount.CreditorAccountType
import ai.friday.openfinance.app.sweepingaccount.CreditorId
import ai.friday.openfinance.app.sweepingaccount.CreditorType
import ai.friday.openfinance.app.sweepingaccount.ParticipantCreditor
import ai.friday.openfinance.app.sweepingaccount.SweepingCreditor
import ai.friday.openfinance.app.utils.RepositoryError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class SweepingCreditorDbRepository(private val dynamoDbDAO: SweepingCreditorDynamoDAO) : SweepingCreditorRepository {

    override fun save(creditor: SweepingCreditor): SweepingCreditor {
        val currentTime = getZonedDateTime()

        val sweepingCreditorEntity = SweepingCreditorEntity().apply {
            partitionKey = creditor.id.value
            rangeKey = creditor.type.name
            gSIndex1PartitionKey = creditor.userAccountId.value
            gSIndex1ScanKey = "USER_ACCOUNT_ID"
            gSIndex2PartitionKey = creditor.userTaxId.value
            gSIndex2ScanKey = "USER_TAX_ID"
            createdAt = creditor.createdAt?.format(dateTimeFormat) ?: currentTime.format(dateTimeFormat)
            updatedAt = currentTime.format(dateTimeFormat)
            participant = creditor.participant?.name
            creditorTaxId = creditor.creditorTaxId.value
            name = creditor.name
            ispb = creditor.ispb
            issuer = creditor.issuer
            number = creditor.number
            accountType = creditor.accountType
            status = creditor.status!!
        }

        dynamoDbDAO.save(sweepingCreditorEntity)

        return creditor.copy(updatedAt = currentTime)
    }

    override fun find(id: CreditorId): Either<RepositoryError, SweepingCreditor> {
        return dynamoDbDAO.findByPartitionKey(id.value).firstOrNull()?.toSweepingCreditor()?.let {
            return it.right()
        } ?: return RepositoryError.ItemNotFound("BankAccount $id not found").left()
    }
}

fun SweepingCreditorEntity.toSweepingCreditor(): SweepingCreditor {
    return SweepingCreditor(
        userAccountId = UserAccountId(this.gSIndex1PartitionKey),
        id = CreditorId(this.partitionKey),
        type = CreditorType.valueOf(this.rangeKey),
        userTaxId = Document(this.gSIndex2PartitionKey),
        creditorTaxId = Document(this.creditorTaxId),
        name = this.name,
        ispb = this.ispb,
        issuer = this.issuer,
        number = this.number,
        accountType = this.accountType,
        participant = this.participant?.let { ParticipantCreditor(it) },
        status = this.status,
        createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
    )
}

@DynamoDbBean
class SweepingCreditorEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_PARTITION_KEY)
    lateinit var partitionKey: String // creditorId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_RANGE_KEY)
    lateinit var rangeKey: String // type

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // userAccountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1ScanKey: String? = null // TODO

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // userTaxId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDBAttribute(attributeName = "CreditorTaxId")
    lateinit var creditorTaxId: String

    @get:DynamoDBAttribute(attributeName = "Name")
    var name: String? = null

    @get:DynamoDBAttribute(attributeName = "Ispb")
    var ispb: String? = null

    @get:DynamoDBAttribute(attributeName = "Issuer")
    var issuer: String? = null

    @get:DynamoDBAttribute(attributeName = "Number")
    var number: String? = null

    @get:DynamoDBAttribute(attributeName = "AccountType")
    var accountType: CreditorAccountType? = null

    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDBAttribute(attributeName = "Participant")
    var participant: String? = null
}