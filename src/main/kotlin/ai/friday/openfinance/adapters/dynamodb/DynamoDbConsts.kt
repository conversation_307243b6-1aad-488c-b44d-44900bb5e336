package ai.friday.openfinance.adapters.dynamodb

const val OPEN_FINANCE_TABLE_NAME = "Friday-OpenFinance"
const val INDEX_1 = "GSIndex1"
const val INDEX_2 = "GSIndex2"

const val OPEN_FINANCE_PARTITION_KEY = "PartitionKey"
const val OPEN_FINANCE_RANGE_KEY = "RangeKey"

const val INDEX_1_PARTITION_KEY = "GSIndex1PartitionKey"
const val INDEX_1_RANGE_KEY = "GSIndex1RangeKey"
const val INDEX_2_PARTITION_KEY = "GSIndex2PartitionKey"
const val INDEX_2_RANGE_KEY = "GSIndex2RangeKey"

enum class GlobalSecondaryIndexNames {
    GSIndex1, GSIndex2, GSIndex3
}