micronaut:
  server:
    ssl:
      enabled: false
    netty:
      listeners:
        httpsListener:
          port: -1
          ssl: false
    port: -1
    max-request-size: 6291456 #1024L*1024*6=>6MB
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 6291456 #6291456

integrations:
  iniciador:
    host: https://data.sandbox.iniciador.com.br
    accessTokenPath: /v1/data/auth
    clientId: FROM_AWS_SECRETS
    clientSecret: FROM_AWS_SECRETS
    link:
      permissions:
        - REGISTRATION_ALL
        - ACCOUNTS_ALL
        - CREDIT_CARDS_ALL
        - CREDIT_OPERATIONS_ALL
        - INVESTMENTS_ALL
        - EXCHANGES_ALL

open-finance-auth:
  clientid: clientid
  clientsecret: clientsecret