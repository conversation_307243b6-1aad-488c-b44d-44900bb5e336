package ai.friday.openfinance.integration

import ai.friday.openfinance.adapters.dynamodb.OPEN_FINANCE_PARTITION_KEY
import ai.friday.openfinance.adapters.dynamodb.OPEN_FINANCE_RANGE_KEY
import ai.friday.openfinance.adapters.dynamodb.OPEN_FINANCE_TABLE_NAME
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB

fun createOpenFinanceTable(amazonDynamoDB: AmazonDynamoDB?) {
    DynamoDBUtils.createOpenFinanceTable(
        amazonDynamoDB!!,
        OPEN_FINANCE_TABLE_NAME,
        OPEN_FINANCE_PARTITION_KEY,
        OPEN_FINANCE_RANGE_KEY,
        "GSIndex1PrimaryKey",
        "GSIndex1ScanKey",
        "GSIndex2PrimaryKey",
        "GSIndex2Scan<PERSON>ey",
        "GSIndex3Primary<PERSON><PERSON>",
        "GSIndex3S<PERSON><PERSON><PERSON>",
    )
}